# Stylelint 配置说明

## 概述

本项目的 Stylelint 配置基于 Umi 的最佳实践和 React 项目的样式规范，旨在提高代码质量、一致性和可维护性。

## 配置特点

### 1. 基于 Umi Max 配置

- 继承 `@umijs/max/stylelint` 配置，包含了 Umi 生态的所有最佳实践

### 2. 主题系统支持

- **CSS 变量优先**: 推荐使用 `var(--theme-*)` 而不是硬编码的颜色值
- **命名规范**: 支持 `--theme-*`, `--brand-*`, `--component-*` 等命名模式
- **兼容现有变量**: 允许现有的颜色变量如 `--red-*`, `--blue-*` 等

### 3. 兼容性考虑

- **vh/vw 单位警告**: 避免移动端兼容性问题
- **gap 属性警告**: 提醒 flexbox gap 的兼容性问题
- **vendor 前缀警告**: 推荐使用 autoprefixer 自动处理

### 4. 代码质量规则

- **选择器复杂度**: 限制选择器嵌套深度和复合选择器数量
- **命名规范**: 支持 kebab-case 和 camelCase 类名
- **性能优化**: 避免低性能选择器和过高的特异性

## 规则分类

### 基础规则

- `value-keyword-case: null` - 禁用关键字大小写检查，避免动态值问题

### 重要性和性能规则

- `declaration-no-important: warning` - 警告使用 !important
- `keyframe-declaration-no-important: true` - 禁止在关键帧中使用 !important

### 单位和兼容性规则

- `unit-disallowed-list: ['vh', 'vw']` - 警告使用视口单位
- `property-disallowed-list: ['gap']` - 警告使用 gap 属性

### 颜色相关规则

- `color-hex-length: 'short'` - 使用短格式十六进制颜色
- `color-no-hex: warning` - 推荐使用 CSS 变量而不是十六进制颜色
- `color-named: 'never'` - 避免使用命名颜色

### 字体相关规则

- `font-family-name-quotes: 'always-where-recommended'` - 字体名称引号规则
- `font-weight-notation: 'numeric'` - 使用数字字体权重

### 选择器规则

- `selector-max-compound-selectors: 6` - 限制复合选择器数量
- `max-nesting-depth: 4` - 限制嵌套深度
- `selector-class-pattern` - 类名命名模式检查

## 使用建议

### 1. CSS 变量优先

```less
// ✅ 推荐
.button {
  background-color: var(--theme-button-color-primary);
  color: var(--theme-text-color-white);
}

// ⚠️ 不推荐
.button {
  background-color: #1890ff;
  color: #ffffff;
}
```

### 2. 避免深层嵌套

```less
// ✅ 推荐
.modal {
  .header {
    /* ... */
  }
  .content {
    /* ... */
  }
  .footer {
    /* ... */
  }
}

// ⚠️ 避免
.modal {
  .content {
    .form {
      .input {
        .label {
          /* 嵌套过深 */
        }
      }
    }
  }
}
```

### 3. 使用语义化类名

```less
// ✅ 推荐
.user-profile {
  /* ... */
}
.navigation-menu {
  /* ... */
}
.submitButton {
  /* ... */
}

// ⚠️ 避免
.div1 {
  /* ... */
}
.red-text {
  /* ... */
}
```
