/**
 * 通过 yarn build 编译完成后，需要根据当前分支名称决定是否需要将入口的 index.html 文件重命名为 {branch-name}.html
 * 这是一个简易分支切换系统中的一个步骤，只在公式内部的发布到测试环境的时候使用，具体分支切换系统的逻辑请参考：
 * 如果不是 main 分支，就不用执行这个逻辑，非 main 分支就需要执行此脚本来重命名 index.html 文件
 */

/**
 * 生成 lizard-view 需要的 assets
 *
 * 需要先构建好
 */
const main = async () => {
  const startTime = Date.now();

  // 检查是否在 CI 环境中
  if (!process.env.CI_JOB_ID) {
    console.warn('[rename-entry] not in CI environment, skipping rename');
    // return;
  }

  // 检查分支名称是否存在
  if (!process.env.CI_COMMIT_REF_NAME) {
    console.warn('[rename-entry] branch name not found, skipping rename');
    // return;
  }

  // 获取当前分支名称，在 GitLab CI 环境中通过 CI_COMMIT_REF_NAME 环境变量获取
  const branchName = process.env.CI_COMMIT_REF_NAME;
  const targetEntryName = branchName === 'main' || branchName === undefined ? 'index' : branchName;
  const platform = process.env.PLATFORM ?? 'pc';

  console.log('PLATFORM:', process.env);

  // 构建后的 index.html 文件路径
  const fs = require('fs');
  const path = require('path');
  const distDir = path.resolve(__dirname, '../dist');
  const indexHtmlPath = path.join(distDir, 'index.html');

  // 检查 index.html 是否存在
  if (!fs.existsSync(indexHtmlPath)) {
    console.error('[rename-entry] index.html not found in dist directory');
    // return;
  }

  // 重命名为 {branch-name}.{platform}.html
  const targetFileName = `${targetEntryName}.${platform}.html`;
  const newHtmlPath = path.join(distDir, targetFileName);
  try {
    fs.renameSync(indexHtmlPath, newHtmlPath);
    console.log(`[rename-entry] Successfully renamed index.html to ${targetFileName}`);
  } catch (error) {
    console.error('[rename-entry] Failed to rename index.html:', error);
  }

  console.log('rename time cost:', Date.now() - startTime);
};

main().catch((e) => {
  global.console.error(e);
  process.exit(-1);
});
