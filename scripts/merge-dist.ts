/**
 * 合并 pc 及 mobile 两个平台的构建产物
 * 在完整构建流程中，会先构建 pc 平台的产物，然后将 pc 平台构建产物移动到临时路径，
 * 再构建 mobile 平台的产物，然后把之前的 pc 平台构建产物移回来和 mobile 平台的产物合并
 * 最终产物会在 dist 目录下
 *
 * 后端收到请求后会根据 ua 判断要加载 pc 平台的入口 html 文件还是 mobile 平台的入口 html 文件
 */
import fs from 'fs';
import path from 'path';

/**
 * 保存临时产物
 */
function saveTemp(platform: string): void {
  const sourcePath = path.resolve(__dirname, '../dist');
  const targetPath = path.resolve(__dirname, `../dist-${platform}`);
  if (!fs.existsSync(sourcePath)) {
    console.error(`错误：源目录 ${sourcePath} 不存在，无法保存临时产物。`);
    process.exit(1);
  }
  if (fs.existsSync(targetPath)) {
    console.log(`删除已存在的临时目录: ${targetPath}`);
    fs.rmSync(targetPath, { recursive: true, force: true });
  }
  console.log(`移动 ${sourcePath} 到 ${targetPath}`);
  fs.renameSync(sourcePath, targetPath);
  console.log('临时产物保存完成.');
}

/**
 * 递归合并目录内容
 * @param sourceDir 源目录
 * @param targetDir 目标目录
 */
function mergeRecursive(sourceDir: string, targetDir: string): void {
  // 确保目标目录存在
  if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
    console.log(`  创建目标子目录: ${targetDir}`);
  } else if (!fs.statSync(targetDir).isDirectory()) {
    // 如果目标路径存在但不是目录，这是一个冲突，需要决定如何处理
    // 在这个场景下，通常意味着构建过程有问题，直接报错退出可能更安全
    console.error(`错误：目标路径 ${targetDir} 已存在但不是一个目录。无法合并 ${sourceDir}`);
    throw new Error(`Merge conflict: Target path ${targetDir} is not a directory.`);
  }

  const items = fs.readdirSync(sourceDir);

  items.forEach((item) => {
    const sourceItemPath = path.join(sourceDir, item);
    const targetItemPath = path.join(targetDir, item);
    const stats = fs.statSync(sourceItemPath);

    if (stats.isDirectory()) {
      // 是目录，递归合并
      console.log(`  递归合并目录: ${item}`);
      mergeRecursive(sourceItemPath, targetItemPath);
      // 递归完成后，尝试删除空的源子目录
      try {
        fs.rmdirSync(sourceItemPath);
      } catch (err: any) {
        // 如果删除失败（例如目录非空，这不应该发生），记录错误
        console.warn(`  未能删除源子目录 ${sourceItemPath}: ${err.message}`);
      }
    } else if (stats.isFile()) {
      // 是文件，移动并覆盖
      console.log(`  移动文件: ${item}`);
      fs.renameSync(sourceItemPath, targetItemPath); // renameSync 会覆盖目标文件
    } else {
      console.warn(`  跳过非文件/非目录项: ${sourceItemPath}`);
    }
  });
}

/**
 * 合并产物
 */
function merge(platform: string): void {
  const sourcePath = path.resolve(__dirname, `../dist-${platform}`); // 例如 ../dist-pc
  const targetPath = path.resolve(__dirname, '../dist'); // 例如 ../dist (包含 mobile 构建产物)

  if (!fs.existsSync(sourcePath)) {
    console.error(`错误：未找到临时目录 ${sourcePath}`);
    process.exit(1);
  }

  // 如果目标目录 (例如 dist) 不存在，说明 mobile 构建可能失败或未执行
  // 这种情况下，直接将临时目录重命名为目标目录即可
  if (!fs.existsSync(targetPath)) {
    console.log(`目标目录 ${targetPath} 不存在，直接将 ${sourcePath} 重命名为 ${targetPath}`);
    fs.renameSync(sourcePath, targetPath);
    console.log('合并完成 (通过重命名).');
    return;
  }

  // 如果目标目录存在，执行递归合并
  console.log(`开始递归合并 ${sourcePath} 到 ${targetPath}...`);

  try {
    mergeRecursive(sourcePath, targetPath);
    // 在顶层递归合并完成后，删除空的源根目录
    fs.rmdirSync(sourcePath);
    console.log(`已删除空的源根目录: ${sourcePath}`);
    console.log('合并完成.');
  } catch (err) {
    console.error('合并过程中发生错误:', err);
    process.exit(1); // 合并出错时退出
  }
}

const commands: { [key: string]: (...args: any[]) => void } = {
  saveTemp,
  merge,
};

async function main(): Promise<void> {
  const commandType = process.argv[2];
  const args = process.argv.slice(3);
  if (typeof commands[commandType] === 'function') {
    try {
      commands[commandType](...args);
    } catch (err) {
      global.console.log('脚本执行错误: ', err);
      process.exit(1);
    }
  } else {
    global.console.log(`未知的命令类型: ${commandType}`);
    process.exit(1);
  }
}

main();
