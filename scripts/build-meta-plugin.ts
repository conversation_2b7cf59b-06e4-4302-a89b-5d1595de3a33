/**
 * 这个插件会在 umi build 时把一些额外的 meta 信息注入到 html 和 js 文件中
 * 具体来说，会把当前构建的分支名和构建时间，以及最新的 commit 信息注入 html head 中，并输出到控制台
 */
import type { IApi } from 'umi';

export default (api: IApi) => {
  api.describe({
    key: 'add build meta info into html and js',
  });
  const buildTime = new Date().toLocaleString();
  const isCI = !!process.env['CI'];

  if (isCI) {
    const branch = process.env['CI_COMMIT_BRANCH'];
    const shortHash = process.env['CI_COMMIT_SHORT_SHA'];
    let commitTitle = process.env['CI_COMMIT_TITLE'] || '';
    const commitAuthor = process.env['CI_COMMIT_AUTHOR'];

    // 清理提交信息中的引号，避免在生成的代码中产生语法错误
    commitTitle = commitTitle.replace(/['"]/g, '');

    const details = `${buildTime} ${shortHash} ${commitAuthor}: ${commitTitle} - ${branch}`;
    console.log('commit info: ', details);
    api.addHTMLMetas(() => ({
      name: 'build-meta',
      content: details,
    }));

    // 改进转义特殊字符的处理，确保在生成的代码中不会引起语法错误
    // 使用JSON.stringify来处理字符串，它会自动处理所有需要转义的字符
    const safeDetails = JSON.stringify(details);

    // 从JSON字符串中去掉首尾的双引号，因为我们会在模板字符串中使用它
    const escapedDetails = safeDetails.slice(1, -1);

    api.addEntryCodeAhead(() => `console.log('build meta: ${escapedDetails}')`);
  }
};
