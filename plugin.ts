import axios from 'axios';
import type { IApi } from 'umi';

export default (api: IApi) => {
  api.modifyHTML(async ($, { path }) => {
    const regex = /^[a-zA-Z0-9]{16}$/;
    // 生产环境的path不可信,必会catch到异常
    const pathArray = path.split('/');
    let guid = '';

    // 遍历 pathArray，查找第一个满足正则表达式的值为guid
    for (let i = 0; i < pathArray.length; i++) {
      if (regex.test(pathArray[i])) {
        guid = pathArray[i];

        try {
          const response = axios.get(`https://drv-dev.shimorelease.com/files/${guid}/pageJson`, {
            headers: {
              // TODO:Cookie有时候会过期（这个时间应该蛮长的，应该一个月？），需要重新获取，且需要与当前账号保持一致，否则无权限访问该文件
              Cookie: 'shimo_sid=s%3Aeb2c966a0b3a4cd996f9d200eb83bb46.GnPKQd8QQRN0A1shyTR0Fa3Il%2Fqw5YsR%2F61VEwMh6BI',
            },
          });
          const data = (await response).data;
          const links = data.styles.map((url: string) => `<link rel="stylesheet" href=${url}></link>`);
          const scripts = data.scripts.map((url: string) => `<script src=${url} defer="defer"></script>`);

          $('head').append(links);

          $('body').append([
            `<script>
                window.cow = window.cow || {}
                const envs = ${JSON.stringify(data.runtime_env)}
                envs[0].__RUNTIME_ENV__.SDK_V2_HOST = location.origin; // 本地开发的时候要请求本地的地址，通过代理发到服务器
                for (let i = 0; i < envs.length; i++) {
                    let env = envs[i]
                    for (let n in env) window[n] = env[n]
                }
            </script>`,
            ...scripts,
          ]);
        } catch (error) {
          console.error('Error fetching data:', error);
        }
        break;
      }
    }

    return $;
  });
};
