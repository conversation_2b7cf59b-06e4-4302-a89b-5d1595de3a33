image: 'reg.smvm.cn/appbase/docker:lizard-build2'

variables:
  NODELABLE: 'beta.kubernetes.io/os'
  NODELABLEVALUE: 'linux'
  LIMMEM: '2048Mi'
  LIMCPU: '1'
  MIRCTYPE: 'common'
  NPMREG: 'http://registry.npm.shimo.run'
  NPM_CONFIG_LOGLEVEL: 'error'
  GIT_SUBMODULE_STRATEGY: 'recursive'
  DOCKER_HOST: 'tcp://localhost:2375'
  DOCKER_DRIVER: 'overlay2'
  DEBUG_LIZARD: ''
  CI_PROJECT_NAME: 'drive-fe'

# 分为几个阶段 pre-build build
stages:
  - 'pre-build'
  - 'build'

default:
  cache:
    key: dependency
    paths:
      - .yarn/cache
  before_script:
    - mkdir -p .cache
    - node -v
    - npm -v
    - yarn -v || echo yarn does not exists
    - corepack enable
    - yarn -v
    - git --version
    - bash --version
    - date
    - yarn install --json --immutable # 确保 lockfile 不会变化，同时安装依赖
  after_script:
    - echo "okay"
  ## 相同分支的构建任务在新的 commit 到来时会被中断
  interruptible: true

# - bash ./scripts/notifications/ci_notification_failed.sh
# ------------- 下面开始配置 --------------- #
lint:
  stage: 'pre-build'
  script:
    - yarn umi lint
  tags:
    - 'shellrunner-docker'
  only:
    - '/^(feat|feature|hotfix|fix)-([a-z0-9.]+-)*[a-z0-9.]+$/'
    - 'main'
    - /^release-.*/
    - /^pd-.*/
    - /^co-.*/

tsc:
  stage: 'pre-build'
  script:
    - yarn tsc
  tags:
    - 'shellrunner-docker'
  only:
    - '/^(feat|feature|hotfix|fix)-([a-z0-9.]+-)*[a-z0-9.]+$/'
    - 'main'
    - /^release-.*/
    - /^pd-.*/
    - /^co-.*/

build:
  stage: 'build'
  variables:
    NODE_ENV: 'production'
    NODE_BUILD_TARGET: 'release'
  script:
    - yarn build-all
    - yarn upload
  tags:
    - 'shellrunner-docker'
  only:
    - '/^(feat|feature|hotfix|fix)-([a-z0-9.]+-)*[a-z0-9.]+$/'
    - 'main'
    - /^release-.*/
    - /^pd-.*/
    - /^co-.*/
