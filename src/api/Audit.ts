import dayjs from 'dayjs';

import type { Filters } from '@/model/audit';

import { CommonApi } from './Request';

export const defaultDates: [number, number] = [dayjs().subtract(2, 'days').valueOf(), new Date().valueOf()];

export const defaultFilters = {
  operator: '',
  operations: [],
  guid: undefined,
  dates: defaultDates,
};

const PAGE_SIZE = 50;

const deriveGuid = (link: string | undefined | null) => {
  if (!link) {
    return null;
  }
  const match = /(^|\/)([a-zA-Z0-9]{16})(%|\/|$)/.exec(link);
  return match?.[2];
};
const getSearchParams = (filters: Filters) => {
  const { operator, guid, operations, dates } = filters;
  const query = {
    operatorId: operator ? operator : undefined,
    targetFileId: deriveGuid(guid),
    limit: PAGE_SIZE,
    startTimestamp: dates ? Math.floor(dates[0] / 1000) : undefined,
    endTimestamp: dates ? Math.floor(dates[1] / 1000) : undefined,
    actionId: operations,
  };

  const searchParams = new URLSearchParams();
  for (const [key, value] of Object.entries(query)) {
    if (Array.isArray(value)) {
      value.forEach((item) => {
        if (item) {
          searchParams.append(key, item);
        }
      });
    } else if (value) {
      searchParams.append(key, value as string);
    }
  }
  return searchParams.toString();
};

export const searchLogs = (filters: Filters) => {
  getSearchParams(filters);
  return CommonApi.get(`/audits/logs?${getSearchParams(filters)}`);
};

export const loadMore = (nextUrl: string) => {
  const regex = /^\/api\/v1\//i;
  const newUrl = nextUrl.replace(regex, '');
  return CommonApi.get(`${newUrl}`);
};

export const getExportUrl = (filters: Filters) => {
  const queryParams = getSearchParams(filters);
  return CommonApi.get(`/audits/logs/export?${queryParams}`);
};

export const searchUserList = (keyword?: string) => {
  return CommonApi.post('/search', {
    keyword,
    filter: {
      user: {
        includeRecentContact: true,
        includeTeamMember: true,
      },
    },
    fetchFileRoleByFileGuid: '',
  });
};

/**
 * 获取操作列表
 */
export const getClassification = () => {
  return CommonApi.get(`/audits/classification`);
};
