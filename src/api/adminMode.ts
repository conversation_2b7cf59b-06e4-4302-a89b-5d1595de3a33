import { CommonApi } from './Request';
export async function checkAdminMode<T>(): Promise<T> {
  // 调用CommonApi的get方法，获取admin模式的检查结果
  const response = await CommonApi.get(`/admin_mode/check_and_refresh`);
  return response.data;
}
export async function setAuthKind<T, R>(authMode: R): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/auth_mode`, authMode);
  return response.data;
}

/**
 * 创建授权方式记录
 */
export async function createAuthKindRecord<T, R>(authMode: R): Promise<T> {
  const response = await CommonApi.put(`/admin_mode/auth_mode`, authMode);
  return response.data;
}

/**
 * 退出管理员模式
 */

export async function quitAdminMode(): Promise<boolean> {
  const response = await CommonApi.post(`/admin_mode/quit`);
  return response.data;
}
/**
 * 检查管理员邮箱的状态
 */
export async function checkEmailStatus<T>(): Promise<T> {
  // 调用CommonApi的get方法，获取admin模式的检查结果
  const response = await CommonApi.get(`/admin_mode/check_can_control`);
  return response.data;
}
/**
 * 获取授权方式 sms email password
 */
export async function getAuthKind<T>(): Promise<T> {
  // 调用CommonApi的get方法，获取admin模式的检查结果
  const response = await CommonApi.get(`/admin_mode/auth_mode`);
  return response.data;
}

/**
 * 检验当前管理员是否已经设置过密码
 */
export async function checkPasswordExists<T>(): Promise<T> {
  // 调用CommonApi的get方法，获取admin模式的检查结果
  const response = await CommonApi.get(`/admin_mode/check_admin_password`);
  return response.data;
}

/**
 * 向绑定邮箱发送验证码
 */

export async function sendEmailCaptchaCode({ code }: { code: string }): Promise<boolean> {
  const response = await CommonApi.post(`/admin_mode/verify_email_code`, { code });
  return response.data;
}
/**
 * 触发邮箱验证码
 */
export async function fireEmailCode<T>(): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/send_email_code`);
  return response.data;
}

/**
 * 触发验证码
 */
export async function fireCaptchaCode<T>({ voice }: { voice: boolean }): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/send_sms_code`, { voice });
  return response.data;
}

/**
 * 发送验证码
 */
export async function sendCaptchaCode<T>({ code }: { code: string }): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/verify_sms_code`, { code });
  return response.data;
}

/**
 * 发送管理员密码，准备进入管理员模式
 */
export async function sendPassword<T>({ password }: { password: string }): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/verify_admin_password`, { password });
  return response.data;
}

/**
 * 设置管理员模式
 */
export async function setAdminPassword<T>({ password }: { password: string }): Promise<T> {
  const response = await CommonApi.post(`/admin_mode/admin_password`, { password });
  return response.data;
}
