import type { CollaborationInfo, CollaboratorsData, CollaboratorsProps } from './Collaboration.type';
import { CommonApi } from './Request';

// 获取分享协作 空间 文件夹 及文件详情
export function getCollaborationDetail(guid: string) {
  return CommonApi.get<CollaborationInfo>(`/files/${guid}`);
}

// 获取协作者列表（协作者和管理者 所以人员）
export function getCollaborationList(guid: string, query: CollaboratorsProps) {
  return CommonApi.get<CollaboratorsData>(`/files/${guid}/collaborators`, { params: query });
}

//修改协作者的权限---添加协作者权限
export function updateCollaboration(guid: string, id: string, data: any) {
  return CommonApi.patch(`/files/${guid}/collaborators/${id}`, data);
}
//修改协作者中部门权限
export function updateCollaborationDepartment(guid: string, id: string, data: any) {
  return CommonApi.patch(`/files/${guid}/dep_collaborators/${id}`, data);
}

// 添加或者修改 部门协作权限
export function addCollaborationDepartment(guid: string, id: string, data: any) {
  return CommonApi.patch(`/files/${guid}/dep_collaborators/${id}`, data);
}
//移除协作者人员 权限
export function deleteCollaboration(guid: string, id: string) {
  return CommonApi.delete(`/files/${guid}/collaborators/${id}`);
}
//移除协作者中 部门权限
export function deleteCollaborationDepartment(guid: string, id: string) {
  return CommonApi.delete(`/files/${guid}/dep_collaborators/${id}`);
}

//修改上级目录协作者权限
export function updateParentCollaboration(guid: string, data: any) {
  return CommonApi.patch(`/files/${guid}`, data);
}

//关闭或者开启 分享（api/v1/files/{fileGuid}/share）
export function updateShareStatus(guid: string, data: any) {
  return CommonApi.patch(`/files/${guid}/share`, data);
}

//分享密码 的关闭开（api/v1/files/{fileGuid}/share）
export function SharePasswordStatus(guid: string, data: any) {
  return CommonApi.patch(`/files/${guid}/password`, data);
}

//修改文件过期时间
export function updateExpireTime(guid: string, data: any) {
  return CommonApi.patch(`/files/${guid}/expire`, data);
}

//查询最近联系人
export function getRecentContact() {
  return CommonApi.post(`/search/recent_contacts`, {});
}

//获取组织架构
export function getOrgDepartment(id: string) {
  return CommonApi.get(`/org/departments/${id}/shadow_subtree`);
}

//获取组织架构成员列表
export function getOrgDepartmentUser(id: string, query: any) {
  return CommonApi.get(`/org/departments/${id}/users`, { params: query });
}

//搜索人员
export function getSearchUser(query: any) {
  return CommonApi.post(`/search`, query);
}

//设置为管理者
export function setAdmin(guid: string, id: string, data: any) {
  return CommonApi.put(`/files/${guid}/admins/${id}`, data);
}

//删除管理者
export function deleteAdmin(guid: string, id: string) {
  return CommonApi.delete(`/files/${guid}/admins/${id}`);
}

//设置部门为管理者
export function setDepAdmin(guid: string, id: string, data: any) {
  return CommonApi.put(`/files/${guid}/dep_admins/${id}`, data);
}

//删除部门管理者
export function deleteDepAdmin(guid: string, id: string) {
  return CommonApi.delete(`/files/${guid}/dep_admins/${id}`);
}
