export interface CollaborationInfo {
  id: number;
  guid: string;
  name: string;
  type: string;
  isFolder: boolean;
  collaboratorCount: number;
  departmentCollaboratorCount: number;
  isSpace: boolean;
  isLocked: boolean;
  isShortcut: boolean;
  hasChildren: boolean;
  isFileAdmin: boolean;
  isCloudFile: boolean;
  isDesktop: boolean;
  role: string;
  url: string;
  isDelete: number;
  parentId: number;
  passwordProtected: boolean;
  shareMode: string;
  teamId: number;
  userId: number;
  deletedBy: string;
  updatedAt: number;
  updatedBy: number;
  createdAt: number;
  fileSize: number;
  starred: boolean;
  tags: any[];
  parentRole: string;
  parent_guid: string;
  user: User;
  updatedUser: UpdatedUser;
  lockedUser: unknown;
  sharedUser: unknown;
  permissions: Permissions;
  permissionsAndReasons: PermissionsAndReasons;
  password: string;
  views: number;
}

export interface User {
  id: number;
  name: string;
  avatar: string;
  email: string;
  accountMetadata: AccountMetadata;
}

export interface AccountMetadata {
  isExpired: boolean;
  isDingtalk: boolean;
  isWework: boolean;
  isEnterprise: boolean;
  isFreeEnterprise: boolean;
  expiredAt: TimeAt;
  isTrial: boolean;
  isPersonalPremium: boolean;
  isEnterprisePremium: boolean;
  isEnterpriseLight: boolean;
  startedAt: TimeAt;
  editionId: number;
  lastMembershipExpiredAt: TimeAt;
  lastMembershipEditionId: number;
}

export interface TimeAt {
  seconds: number;
  nanos?: number;
}

export interface UpdatedUser {
  id: number;
  name: string;
  avatar: string;
  email: string;
}

// export interface LockedUser {}
//
// export interface SharedUser {}

export interface Permissions {
  readable: boolean;
  editable: boolean;
  commentable: boolean;
  manageable: boolean;
  exitable: boolean;
  exportable: boolean;
  collaboratorManageable: boolean;
  adminManageable: boolean;
  outsiderAddable: boolean;
  childFileCreatable: boolean;
  shareModeManageable: boolean;
  teamShareModeManageable: boolean;
  copyable: boolean;
  lockable: boolean;
  unlockable: boolean;
  removable: boolean;
  downloadable: boolean;
  canDuplicate: boolean;
  canUncompress: boolean;
  canPrint: boolean;
  canSaveAsTemplate: boolean;
  canApplyPermission: boolean;
  canRename: boolean;
  canModifyAdvancedPermission: boolean;
  canCustomizeAdvancedPermission: boolean;
  moveable: boolean;
  sheetLockable: boolean;
  passwordShareable: boolean;
  fileAuthSetable: boolean;
}

export interface PermissionsAndReasons {
  /**
   * 是否能够添加外部协作者
   */
  canAddOutsider: CanAction;
  /**
   * 是否能够修改分享方式（开启开关、修改链接权限）
   */
  canChangeShareMode: CanAction;
  /**
   * 是否能够修改企业分享方式（仅限企业成员访问）
   */
  canChangeTeamShareMode: CanAction;
  /**
   * 可评论
   */
  canComment: CanAction;
  /**
   * 可以拷贝
   */
  canCopy: CanAction;
  /**
   * 目标文件夹是否能够创建子文件
   */
  canCreateChildFile: CanAction;
  /**
   * 是否能够下载文件
   */
  canDownload: CanAction;
  /**
   * 是否可写
   */
  canEdit: CanAction;
  /**
   * 是否可以退出协作
   */
  canExit: CanAction;
  /**
   * 是否能够导出
   */
  canExport: CanAction;
  /**
   * 可以加锁
   */
  canLock: CanAction;
  /**
   * 是否具有文件的管理权限
   */
  canManage: CanAction;
  /**
   * 否能够管理文件管理者
   */
  canManageAdmin: CanAction;
  /**
   * 是否能够管理文件协作者
   */
  canManageCollaborator: CanAction;
  /**
   * 可读
   */
  canRead: CanAction;
  /**
   * 是否能够删除文件
   */
  canRemove: CanAction;
  /**
   * 可解锁
   */
  canUnlock: CanAction;
  /**
   * 是否能使用移动功能
   */
  canMove: CanAction;
  /**
   * 是否能够使用密码分享功能
   */
  canSetPassword: CanAction;
  /**
   * 锁定表格单元格权限
   */
  canLockSheetCell: CanAction;
  /**
   * 是否能够创建副本
   */
  canDuplicate: CanAction;
  /**
   * 是否能够解压
   */
  canUncompress: CanAction;
  /**
   * 是否能够打印
   */
  canPrint: CanAction;
  /**
   * 保存为模板
   */
  canSaveAsTemplate: CanAction;
  /**
   * 无权访问时允许申请
   */
  canApplyPermission: CanAction;
  /**
   * 重命名
   */
  canRename: CanAction;
  /**
   * 自定义文件 tag
   */
  canModifyUserFileTag: CanAction;
  /**
   * 企业文件 tag
   */
  canModifyTeamFileTag: CanAction;
  /**
   * 谁能减少权限,恢复本层删除的权限
   */
  canModifyAdvancedPermission: CanAction;
  /**
   * 超级权限,可以添加超级权限用户
   */
  canCustomizeAdvancedPermission: CanAction;
}

/**
 * 权限及其原因
 */
export interface CanAction {
  /**
   * 是否有权限
   */
  value: boolean;
  /**
   * 没有权限的原因
   */
  reason: string;
}

export interface CollaboratorsProps {
  includeInherited: boolean;
  includeAdmin: boolean;
}

export interface CollaboratorsData {
  roles: Required<Role>[];
  admins: Role[];
}

export interface Role {
  id: number;
  name: string;
  email: string;
  avatar: string;
  mobile: string;
  status: number;
  alias: string;
  createdAt: number;
  namePinyin: string;
  teamId: number;
  teamRole: string;
  mergedInto: number;
  teamTime: number;
  mobileAccount: string;
  hasPassword: boolean;
  isSeat: boolean;
  teamRoleEnum: number;
  registerTime: number;
  role: string;
  permission?: string;
  type: number;
  canBother: boolean;
  isOutsider: boolean;
  isInherited: boolean;
}
