import { Button } from 'antd-mobile';
import { useState } from 'react';

import { CollaborationShareMobile } from '@/components/Collaboration/CollaborationShareMobile';
import { useDisclosure } from '@/hooks/useDisclosure';

export default () => {
  const [id, setId] = useState('');
  const { isOpen, open, close, toggle } = useDisclosure(false);

  function handleClick() {
    setId('dPkpKOGKl7fnwQqO');
    open();
  }

  function handleVisibleChange() {
    close();
    setId('');
  }

  return (
    <div>
      <Button onClick={handleClick}>test</Button>
      <CollaborationShareMobile close={handleVisibleChange} guid={id} toggle={toggle} visible={isOpen} />
    </div>
  );
};
