import { MoreOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Card, Table } from 'antd';
import { useEffect, useState } from 'react';

import { files } from '@/api/File';
import CollaborationShare from '@/components/Collaboration';
import { createMenu } from '@/components/ContextMenu';
import { FileName } from '@/components/fileList/components/FileName';
import { ListFilter } from '@/components/fileList/components/ListFilter';
import RenameModal from '@/components/fileList/components/RenameModal';
import styles from '@/components/fileList/index.less';
import { useFileTypeDownload } from '@/hooks/useFileTypeDownload';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { useThemeStore } from '@/store/Theme';
import { openFile, useFormatTime } from '@/utils/file';

import { dropdownItems, items } from './items';

const Recent = () => {
  const { isDark } = useThemeStore((state) => state.theme);

  const meId = useMeStore((state) => state.me.id);

  const { formatTime } = useFormatTime();

  const { downloadDiffFile } = useFileTypeDownload();

  const i18nText = {
    newTabOpens: fm('File.newTabOpens'),
    star: fm('File.star'),
    starSuccess: fm('File.starSuccess'),
    starError: fm('File.starError'),
    removeStr: fm('File.removeStar'),
    removeStarSuccess: fm('File.removeSuccess'),
    removeStarError: fm('File.removeError'),
    share: fm('File.share'),
    collaboration: fm('File.collaboration'),
    download: fm('File.download'),
    reName: fm('File.reName'),
    png: fm('File.png'),
    moveTo: fm('File.moveTo'),
    copyTo: fm('File.copyTo'),
    copy: fm('FilePathPicker.copy'),
    clearRecord: fm('File.clearRecord'),
    delete: fm('File.delete'),
    clearFilter: fm('File.clearFilter'),
    recentlyOpened: fm('File.recentlyOpened'),
    recentlyEdit: fm('File.recentlyEdit'),
    title: `${fm('deleteConfirm.title')}?`,
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.deleteSuccess'),
    error: fm('File.deleteError'),
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
  };
  const [data, setData] = useState([]);

  const [loading, setLoading] = useState<boolean>(false);

  const [renameVisible, setRenameVisible] = useState(false);

  const [params, setParams] = useState<{ FileName?: string; guid?: string }>({ FileName: '', guid: '' });

  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});

  const [shareVisible, setShareVisible] = useState(false);

  const [shareData, setShareData] = useState<any>();

  const [sorted, setSorted] = useState<'open' | 'edit'>('open');

  const reload = () => {
    setLoading(true);
    files({ type: 'used', lastAction: sorted })
      .then((res: any) => {
        if (res.status === 200) {
          const { list = [] } = res.data;
          setData(list);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const renameCallback = ({
    visible,
    params,
    refresh,
  }: {
    visible: boolean;
    params?: { fileName?: string; guid?: string };
    refresh?: boolean;
  }) => {
    setRenameVisible(visible);
    setParams(params || {});
    if (refresh) reload();
  };

  const handleContextMenu = (e: React.MouseEvent, record: any) => {
    e.preventDefault();
    setShareData(record);
    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({
        record: record,
        i18nText,
        renameCallback,
        reload,
        meId,
        setShareVisible,
        downloadDiffFile,
      }),
      theme: isDark ? 'dark' : 'light',
    });
  };

  const columns: TableColumnsType = [
    {
      title: fm('File.fileName'),
      dataIndex: 'name',
      minWidth: 240,
      render: (value: string, record: any) => {
        return <FileName name={value} record={record} />;
      },
    },
    {
      title: fm('File.createName'),
      dataIndex: ['user', 'name'],
      ellipsis: true,
      width: 160,
    },
    {
      title: sorted === 'open' ? fm('File.openTime') : fm('File.editTime'),
      dataIndex: 'lastUsedAt',
      width: 160,
      ellipsis: true,
      render: (value: number) => {
        return <>{formatTime(value * 1000)}</>;
      },
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: any, record: any) => {
        return (
          <MoreOutlined
            className="more"
            onClick={(event) => {
              event.stopPropagation();
              handleContextMenu(event, record);
            }}
          />
        );
      },
    },
  ];

  useEffect(() => {
    reload();
  }, [sorted]);

  return (
    <Card
      className={styles['mainCardTable']}
      extra={
        <ListFilter
          callback={setSorted}
          init="open"
          items={() => dropdownItems(sorted, i18nText)}
          text={sorted === 'open' ? i18nText.recentlyOpened : i18nText.recentlyEdit}
        />
      }
      title={fm('SiderMenu.siderMenuRecentText')}
    >
      <Table
        virtual
        className={tableClassName}
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        scroll={{ y: tableMaxHeight, x: 760 }}
        onRow={(record: any) => {
          return {
            onDoubleClick: () => {
              openFile(record);
            },
            onContextMenu: (event) => {
              handleContextMenu(event, record);
            },
          };
        }}
      />
      <RenameModal callback={renameCallback} params={params} visible={renameVisible} />
      <CollaborationShare guid={shareData?.guid ?? ''} visible={shareVisible} onCancel={() => setShareVisible(false)} />
    </Card>
  );
};

export default Recent;
