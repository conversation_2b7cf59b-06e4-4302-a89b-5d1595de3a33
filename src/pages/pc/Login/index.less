.loginContainer {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loginHeader {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 56px;
  flex-direction: row;
  padding: 0 12px;
  margin: 40px 0;
}

.loginHeaderChange {
  justify-content: space-around;
}

.headerContent {
  margin: 0 70px;

  :global {
    .ant-select {
      .ant-select-selector {
        border: none;
        background-color: transparent;
      }
    }
  }
}

.customSelectDropdown {
  padding: 4px 0;

  :global {
    .ant-select-item-option {
      height: 36px;
      align-items: center;
    }

    .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
      background-color: var(--theme-select-color-selected-bg);
    }
  }
}

.loginContent {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 420px;
  height: 580px;
  flex-shrink: 0;
}

.loginContentBorder {
  border: 1px solid var(--theme-separator-color-lighter);
  border-radius: 4px;
  background: var(--theme-layout-color-bg-white);
  box-shadow: 0 20px 32px 0 rgba(0, 0, 0, 6%);
}

.loginTitle {
  color: var(--theme-text-color-default);
  text-align: center;
  font-family: 'Songti SC';
  font-size: 32px;
  font-style: normal;
  font-weight: 700;
  line-height: 48px;
  z-index: 1;
  margin-top: 60px;
  margin-bottom: 64px;
}

.loginBgTop {
  position: absolute;
  width: 100%;
  height: 124px;
  background-image: url('@/assets/images/common/loginTop.png');
}

.loginBgBottom {
  position: absolute;
  bottom: 0;
  width: 100%;
  height: 198px;
  background-image: url('@/assets/images/common/loginBottom.png');
}

.loginFooter {
  position: absolute;
  bottom: 0;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-around;
  margin: 90px 0;

  :global {
    .ant-select {
      .ant-select-selector {
        border: none;
        background-color: transparent;
      }
    }
  }
}

.selectOption {
  display: inline-block;
  width: 60px;
}
