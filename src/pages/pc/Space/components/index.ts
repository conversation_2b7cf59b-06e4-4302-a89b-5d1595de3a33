import { message } from 'antd';

export const handleChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setInputValue: (value: string) => void,
  i18nText: any,
) => {
  let value = e.target.value;

  if (value.startsWith(' ')) {
    value = value.trimStart();
    message.warning(i18nText.infoWaring1);
  }
  value = value.replace(/\s+/g, ' ');
  if (value.length > 512) {
    value = value.substring(0, 512);
    message.warning(i18nText.infoWaring2);
  }
  setInputValue(value);
};
