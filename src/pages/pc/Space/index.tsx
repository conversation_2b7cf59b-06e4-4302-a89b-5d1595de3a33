import './index.less';

import { CheckOutlined, MoreOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Card, Dropdown, message, Space, Table, Tooltip } from 'antd';
import { useEffect, useState } from 'react';
import { Link } from 'umi';

import { addSpace, getSpaceList } from '@/api/space';
import { ReactComponent as Downward } from '@/assets/images/svg/downward.svg';
import { ReactComponent as NoSpace } from '@/assets/images/svg/noSpace.svg';
import { ReactComponent as SpaceIcon } from '@/assets/images/svg/spaceIcon.svg';
import CollaborationShare from '@/components/Collaboration';
import { createMenu } from '@/components/ContextMenu';
import { NoData } from '@/components/fileList/components/NoData';
import stylesTable from '@/components/fileList/index.less';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import AddModal from './components/AddModal';
import SettingsModal from './components/SettingsModal';
import { items } from './item';

const Teamspace = () => {
  const { formatTime } = useFormatTime();
  const i18nText: any = {
    teamSpaceName: $t('SiderMenu.siderMenuSpaceText'),
    countSpace: $t('Space.countSpace'),
    createSpace: $t('Space.createSpace'),
    createTeamSpace: $t('Space.createTeamSpace'),
    teamSpaceSure: $t('Space.sure'),
    teamSpaceCancel: $t('Space.cancel'),
    enterSpaceName: $t('Space.enterSpaceName'),
    SpaceNameHeader: $t('Space.SpaceNameHeader'),
    createUserName: $t('File.createName'),
    infoEmpty: $t('Space.infoEmpty'),
    createSuccess: $t('Space.infoSuccess'),
    infoWaring1: $t('Space.infoWaring1'),
    infowaring2: $t('Space.infoWaring2'),
    rightClickShare: $t('Space.rightClickShare'),
    rightClickCollaboration: $t('Space.rightClickCollaboration'),
    rightClickSetting: $t('Space.rightClickSetting'),
    rightClickDelete: $t('Space.rightClickDelete'),
    teamspaceConfirmDeletion: $t('Space.teamspaceConfirmDeletion'),
    success: $t('Space.teamspaceDeleteSuccess'),
    title: $t('deleteConfirm.title'),
    content: $t('Space.teamspaceDeleteTipText'),
    okText: $t('deleteConfirm.title'),
    cancelText: $t('deleteConfirm.cancel'),
    noTeamspace: $t('Space.noTeamspace'),
    noTeamspaceTipText: $t('Space.noTeamspaceTipText'),
  };
  const [tableData, setTableData] = useState([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [opneSetting, setOpneSetting] = useState<boolean>(false);
  const [filterType, setFilterType] = useState<string>('updatedAt');
  const [spaceDataSet, setSpaceDataSet] = useState({});
  const [shareData, setShareData] = useState<any>();
  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});
  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  const [loadingSubmit, setLoadingubmit] = useState<boolean>(false);
  const [noData, setNoData] = useState(true);
  const [shareVisible, setShareVisible] = useState(false);
  const [typeSetting, setTypeSetting] = useState('');
  const [sort, setSort] = useState(true);
  const getTableData = () => {
    setLoading(true);
    getSpaceList({ orderBy: filterType })
      .then((res: any) => {
        if (res.status === 200) {
          const list = res.data.spaces || [];
          setTableData(list);
          setNoData(list.length !== 0);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  useEffect(() => {
    if (filterType) {
      getTableData();
    }
  }, [filterType]);

  const handleAdd = () => {
    if (!inputValue) {
      message.warning(i18nText.infoEmpty);
      return false;
    }
    setLoadingubmit(true);
    addSpace(inputValue).then((res) => {
      setLoadingubmit(false);
      if (res.status === 200) {
        message.success(i18nText.createSuccess);
        setOpen(false);
        setInputValue('');
        getTableData();
      }
    });
  };
  const handleContextMenu = (e: React.MouseEvent, data: any) => {
    e.preventDefault();
    setSpaceDataSet(data);
    setShareData(data);
    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({
        data,
        i18nText,
        reload() {
          getTableData();
        },
        setOpneSetting,
        setShareVisible,
        setTypeSetting,
      }),
    });
  };
  const headleSort = () => {
    setSort(!sort);
    tableData.sort((a: any, b: any) => {
      if (sort) {
        return a.createdAt - b.createdAt;
      } else {
        return b.createdAt - a.createdAt;
      }
    });
  };
  const columns: TableColumnsType = [
    {
      title: () => i18nText.SpaceNameHeader,
      dataIndex: 'name',
      render: (value: string, record: any) => {
        return (
          <div className="spaceNameHeader">
            <div className="spaceNameIcon">
              <SpaceIcon />
            </div>
            <Tooltip placement="top" title={value}>
              <Link className="styledLink" to={`/space/${record.guid}`}>
                {value}
              </Link>
            </Tooltip>
          </div>
        );
      },
      minWidth: 500,
    },
    {
      title: () => i18nText.createUserName,
      dataIndex: 'created',
      render: (value: any) => {
        return <span>{value.user.name}</span>;
      },
      ellipsis: true,
    },
    {
      title: () => {
        return (
          <div className="sortBtn">
            <Dropdown
              menu={{
                items: [
                  {
                    label: $t('File.updateTime'),
                    key: 'updatedAt',
                    icon: filterType === 'updatedAt' ? <CheckOutlined /> : <span />,
                  },
                  {
                    label: $t('File.createdAt'),
                    key: 'createdAt',
                    icon: filterType === 'createdAt' ? <CheckOutlined /> : <span />,
                  },
                ],
                onClick: (info) => {
                  setFilterType(info.key);
                },
              }}
              placement="bottomLeft"
              trigger={['click']}
            >
              <div>
                <Button size="small" type="text">
                  <span className="filterText">
                    {filterType === 'updatedAt' ? $t('File.updateTime') : $t('File.createdAt')}
                  </span>
                </Button>
              </div>
            </Dropdown>
            <Button icon={sort ? <Downward /> : <Downward className="rotate180" />} type="text" onClick={headleSort} />
          </div>
        );
      },
      dataIndex: 'createdAt',
      render: (value: string | null | number) => {
        return <>{formatTime(value)}</>;
      },
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: string, record: any) => {
        return (
          <div className="tableMore">
            <MoreOutlined className="more" onClick={(event) => handleContextMenu(event, record)} />
          </div>
        );
      },
    },
  ];

  return (
    <Card
      className={stylesTable['mainCardTable']}
      title={
        <div className="cardTitleFlex">
          <Space>
            {i18nText.teamSpaceName}
            <div>
              <div className="cardTitleLeft">
                {tableData.length} {i18nText.countSpace}
              </div>
            </div>
          </Space>
          {/*  目前需求不要添加空间功能
          <div
            className={styles.cardTitleRight}
            onClick={() => {
              setOpen(true);
            }}>
            <PlusCircleOutlined /> {i18nText.createSpace}
          </div> */}
        </div>
      }
    >
      {noData ? (
        <Table
          virtual
          className={tableClassName}
          columns={columns}
          dataSource={tableData}
          loading={loading}
          pagination={false}
          rowKey={'guid'}
          scroll={{ y: tableMaxHeight }}
          onRow={(record) => {
            return {
              onContextMenu: (event) => {
                handleContextMenu(event, record);
              },
            };
          }}
        />
      ) : (
        <NoData
          description={i18nText.noTeamspaceTipText}
          img={
            <div className="noData">
              <NoSpace />
            </div>
          }
          title={i18nText.noTeamspace}
        />
      )}
      <AddModal
        handleAdd={handleAdd}
        i18nText={i18nText}
        inputValue={inputValue}
        loadingSubmit={loadingSubmit}
        setInputValue={setInputValue}
        setOpen={setOpen}
        visible={open}
      />
      <CollaborationShare
        enterType={typeSetting}
        guid={shareData?.guid ?? ''}
        visible={shareVisible}
        onCancel={() => setShareVisible(false)}
      />
      <SettingsModal
        setShareVisible={setShareVisible}
        setTypeSetting={setTypeSetting}
        spaceData={spaceDataSet}
        visible={opneSetting}
        onCancel={() => setOpneSetting(false)}
        onSpaceUpdated={getTableData}
      />
    </Card>
  );
};

export default Teamspace;
