.contContainer {
  width: 1000px;
  padding: 24px 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 48px 0;
  background-color: var(--theme-layout-color-bg-white);
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);

  .ant-btn {
    &:hover,
    &:focus,
    &:active {
      background-color: transparent;
      border-color: transparent;
      color: inherit;
      box-shadow: none;
    }

    &.ant-btn-primary {
      &:hover,
      &:focus,
      &:active {
        background-color: var(--theme-text-color-default);
        border-color: var(--theme-separator-color-lighter);
      }
    }

    &.ant-btn-text {
      &:hover,
      &:focus,
      &:active {
        background-color: transparent;
      }
    }
  }
}

.logDiv {
  width: 950px;
  height: 150px;
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
  background-image: url('@/assets/images/common/accountbg.png');
  background-size: cover;
}

.userEditDiv {
  width: 920px;
  height: 108px;
  margin: -36px 0 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-end;

  img {
    display: inline-block;
    width: 108px;
    height: 108px;
    border-radius: 50%;
    background-color: var(--theme-text-color-default);
    border: 4px solid var(--theme-layout-color-bg-white);
    box-sizing: border-box;
    margin-right: 0;
  }

  .ant-btn {
    color: var(--theme-text-color-guidance);
    padding: 0 18px;
    border: 1px solid var(--theme-separator-color-lighter);
  }
}

.avatarId {
  display: flex;
  align-items: end;
}

.nameDiv {
  font-size: 20px;
  font-weight: 500;
  color: var(--theme-text-color-default);
}

.idDiv {
  font-size: 14px;
  font-weight: 400;
  margin: 4px 0;
}

.settingInfoDiv {
  width: 920px;
}

.setTitleDiv {
  font-weight: 500;
  margin-bottom: 12px;
}

.setItemDiv {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .ant-btn {
    color: var(--theme-text-color-guidance);
  }

  .ant-btn-variant-text:not(:disabled):not(.ant-btn-disabled):hover {
    color: var(--theme-text-color-guidance);
  }
}

.setItemDivLeft {
  display: flex;
  align-items: center;

  .passwordIcon {
    padding: 7px 8px 6px;
    border-radius: 4px;
    border: 1px solid var(--theme-separator-color-lighter);
    background: var(--theme-box-shadow-color-level4);
  }

  .passwordSpan {
    display: inline-block;
    width: 100px;
    margin: 0 20px;
  }
}

// 修改基本信息
.ant-modal-content {
  .ant-modal-close {
    top: 20px;
    inset-inline-end: 26px;
  }
}

.modifyTitleDiv {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;

  .modifyTitle {
    color: var(--theme-text-color-medium);
  }

  input {
    display: none;
  }
}

.modifyAvatarDiv {
  margin: 14px 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  img {
    display: inline-block;
    width: 156px;
    height: 156px;
    margin-bottom: 11px;
  }

  .modifyAvatarTips {
    color: var(--theme-text-color-secondary);
    text-align: center;
  }
}

.inputLabel {
  margin-bottom: 8px;
}

.ant-form-item {
  margin-bottom: 22px;
}

.submitBtn {
  margin-bottom: 0;

  .ant-btn {
    padding: 0 18px;
  }
}

.ant-modal-footer {
  .ant-btn {
    padding: 0 18px;
  }
}

.forgetPassword {
  position: relative;

  div {
    position: absolute;
    right: 5px;
    color: var(--theme-text-color-guidance);
    top: 0;
    display: inline-block;
    z-index: 1;
    cursor: pointer;
    width: max-content;
    height: auto;
    pointer-events: auto;
  }
}

.confirm-password-no-mark .ant-form-item-label > label.ant-form-item-required {
  &::before {
    display: none;
  }
}
