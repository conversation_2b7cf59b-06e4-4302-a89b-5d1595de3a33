import './index.less';

import { <PERSON><PERSON>, Button } from 'antd';
import { useState } from 'react';

import { ReactComponent as PasswordKey } from '@/assets/images/svg/passwordKey.svg';
import { useFormatMessage as $t } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import ModifyPassword from './compoents/ModifyPassword';
import ModifyUserInfo from './compoents/ModifyUserInfo';
const AccountInfo = () => {
  const me = useMeStore((state) => state.me);
  const [visible, setVisible] = useState(false);
  const [passWordVisible, setPassWordVisibleVisible] = useState(false);
  const i18nText: any = {
    accountID: $t('Profile.accountID'),
    modifyInfo: $t('Profile.modifyInfo'),
    safetySetting: $t('Profile.safetySetting'),
    accountPd: $t('Profile.accountPd'),
    modify: $t('Profile.modify'),
  };
  return (
    <div className="contContainer">
      <div className="logDiv" />
      <div className="userEditDiv">
        <div className="avatarId">
          <Avatar size={110} src={me.avatar} />
          <div>
            <div className="nameDiv">{me.name}</div>
            <div className="idDiv">
              {i18nText.accountID} {me.id}
            </div>
          </div>
        </div>
        <Button color="primary" variant="outlined" onClick={() => setVisible(true)}>
          {i18nText.modifyInfo}
        </Button>
      </div>
      <div className="settingInfoDiv">
        <div className="setTitleDiv">{i18nText.safetySetting}</div>
        <div className="setItemDiv">
          <div className="setItemDivLeft">
            <span className="passwordIcon">
              <PasswordKey />
            </span>
            <span className="passwordSpan">{i18nText.accountPd}</span>
            {me.hasPassword ? <span>********</span> : ''}
          </div>
          <Button type="text" onClick={() => setPassWordVisibleVisible(true)}>
            {i18nText.modify}
          </Button>
        </div>
      </div>
      <ModifyUserInfo me={me} setVisible={setVisible} visible={visible} />
      <ModifyPassword setVisible={setPassWordVisibleVisible} visible={passWordVisible} />
    </div>
  );
};

export default AccountInfo;
