import { memo } from 'react';

import styles from './FileIcon.less';
import type { FileIconType } from './type';
export interface IconProps {
  size?: number;
  iconType?: `${FileIconType}`;
  className?: string;
  iconSvg: React.ReactNode;
}

export const FileIcon = memo((props: IconProps) => {
  const { iconSvg, size = 24 } = props;
  return (
    <div className={`${styles.styledContainer}`} style={{ width: size, height: size }}>
      <div style={{ width: 24, height: 24, transform: `scale(${size / 24})` }}>{iconSvg}</div>
    </div>
  );
});
