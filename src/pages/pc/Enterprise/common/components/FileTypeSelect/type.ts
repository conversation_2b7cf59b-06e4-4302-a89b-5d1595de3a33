import { fm2 } from '@/modules/Locale';

export enum FileType {
  Unknown = 0,
  FolderOrSpace = 1,
  ShimoFile = 2,
  CloudFile = 3,
}

export enum FileSubType {
  presentation = -10,
  board = -9,
  form = -8,
  mindmap = -7,
  modoc = -6,
  slide = -5,
  mosheet = -4,
  sheet = -3,
  newdoc = -2,
  spreadsheet = -1,
  document = 0,
  folder = 1,
  space = 2,
  img = 3,
  pdf = 4,
  xls = 5,
  docx = 6,
  ppt = 7,
  mp3 = 8,
  zip = 9,
  mp4 = 10,
  wps = 11,
  xmind = 12,
  unknown = -999, // 后端定义为 0，有冲突，这里特殊处理
}

/**
 * 文件类型对应表
 */
export const fileTypeSource = [
  {
    name: fm2('SiderMenu.siderMenuCreateDocText'),
    type: FileType.ShimoFile,
    subType: [FileSubType.document, FileSubType.newdoc],
    id: '1',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateMoDocText'),
    type: FileType.ShimoFile,
    subType: [FileSubType.modoc],
    id: '2',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateTableText'),
    type: FileType.ShimoFile,
    subType: [FileSubType.spreadsheet, FileSubType.sheet, FileSubType.mosheet],
    id: '3',
  },
  {
    name: fm2('SiderMenu.siderMenuCreatePptText'),
    type: FileType.ShimoFile,
    subType: [FileSubType.slide, FileSubType.presentation],
    id: '4',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateThink'),
    type: FileType.ShimoFile,
    subType: [FileSubType.mindmap],
    id: '5',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateFormText'),
    type: FileType.ShimoFile,
    subType: [FileSubType.form],
    id: '6',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateBlank'),
    type: FileType.ShimoFile,
    subType: [FileSubType.board],
    id: '7',
  },

  {
    name: fm2('SiderMenu.siderMenuCreateCloud'),
    type: FileType.CloudFile,
    subType: [
      FileSubType.img,
      FileSubType.pdf,
      FileSubType.xls,
      FileSubType.docx,
      FileSubType.ppt,
      FileSubType.mp3,
      FileSubType.zip,
      FileSubType.mp4,
      FileSubType.wps,
      FileSubType.xmind,
    ],
    id: '8',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateSpace'),
    type: FileType.FolderOrSpace,
    subType: [FileSubType.space],
    id: '9',
  },
  {
    name: fm2('SiderMenu.siderMenuCreateFolderText'),
    type: FileType.FolderOrSpace,
    subType: [FileSubType.folder],
    id: '10',
  },
];
