.styledFileItemContainer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }
}

.styledFileItemContainerBorder {
  padding: 4px 12px;
}

.styledFileItemContainerDisableHover {
  &:hover {
    background: transparent;
  }
}

.styledFileItemContext {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.styledFileItemClose {
  cursor: pointer;
  margin-right: 4px;

  &:hover {
    path[fill-opacity] {
      fill-opacity: 1;
    }
  }
}

.styledFileItemName {
  font-size: 13px;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
  margin-left: 8px;
}

.styledFileItemNameHover {
  :hover {
    color: var(--theme-text-color-guidance);
    text-decoration: underline;
  }
}

.styledFileItemNameGuidance {
  color: var(--theme-text-color-guidance);
}

.styledFileItemNameDefault {
  color: var(--theme-text-color-default);
}

.styledFileItemNamePointer {
  cursor: pointer;
}

.styledFileItemNameUnderline {
  text-decoration: underline;
}

.styledArrow {
  margin-left: 8px;
  vertical-align: text-bottom;
}

.styledCheckbox {
  &.ant-checkbox-wrapper {
    margin-right: 8px;
  }
}
