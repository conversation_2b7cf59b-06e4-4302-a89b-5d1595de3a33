import { Checkbox, Tooltip } from 'antd';
import type { CSSProperties, ReactNode } from 'react';
import { memo, useCallback } from 'react';

import { ReactComponent as ArrowRightIcon } from '@/assets/images/enterprise/main/arrow-right-icon.svg';
import { ReactComponent as CloseSvgIcon } from '@/assets/images/enterprise/main/close-icon.svg';

import { RtlReversalIcon } from '../RtlReversalIcon';
import styles from './FileItem.less';
export interface FileItemProps {
  guid: string;
  icon: ReactNode;
  name: string;

  nameTooltipDisable?: boolean;

  showCheckbox?: boolean;
  checked?: boolean;
  checkboxDisable?: boolean;
  checkboxToolTip?: string;
  onCheckedChange?: (guid: string) => void;

  clickable?: boolean;
  next?: boolean;
  onPress?: (guid: string) => void;
  preview?: boolean;
  previewOpen?: boolean;
  nameDecoration?: 'underline' | 'none';

  showClose?: boolean;
  onClose?: (guid: string) => void;

  border?: boolean;

  disableHover?: boolean;

  style?: CSSProperties;
  nameWidth?: number;

  extend?: ReactNode;
}

export const FileItem = memo((props: FileItemProps) => {
  const {
    icon,
    name,
    guid,
    showCheckbox,
    checked,
    clickable,
    next,
    onCheckedChange,
    showClose,
    onClose,
    border,
    preview,
    previewOpen,
    nameDecoration,
    disableHover,
    style,
    checkboxDisable,
    checkboxToolTip,
    nameTooltipDisable,
    nameWidth,
    extend,
    onPress,
  } = props;

  const onClick = useCallback(() => {
    if (clickable) {
      onPress?.(guid);
    }
  }, [guid, onPress, clickable]);

  const closeClick = useCallback(() => {
    onClose?.(guid);
  }, [guid, onClose]);

  const checkedChange = useCallback(() => {
    onCheckedChange?.(guid);
  }, [guid, onCheckedChange]);

  return (
    <div
      className={`${styles.styledFileItemContainer} ${border ? styles.styledFileItemContainerBorder : ''} ${
        disableHover ? styles.styledFileItemContainerDisableHover : ''
      }`}
      style={style}
    >
      <div className={styles.styledFileItemContext}>
        {showCheckbox ? (
          <Tooltip styles={{ root: { fontSize: 13 } }} title={checkboxToolTip}>
            <Checkbox
              checked={checked}
              className={styles.styledCheckbox}
              disabled={checkboxDisable}
              onClick={checkedChange}
            />
          </Tooltip>
        ) : null}
        {icon ? icon : null}
        <div
          className={`${styles.styledFileItemName} ${
            clickable || previewOpen ? styles.styledFileItemNameGuidance : styles.styledFileItemNameDefault
          } ${clickable || preview ? styles.styledFileItemNamePointer : ''} ${
            previewOpen || nameDecoration === 'underline' ? styles.styledFileItemNameUnderline : ''
          } ${preview ? styles.styledFileItemNameHover : ''}`}
          style={{ maxWidth: nameWidth || 199 }}
          onClick={onClick}
        >
          <Tooltip
            placement="topLeft"
            styles={{ root: { fontSize: 13 } }}
            title={nameTooltipDisable ? undefined : name}
          >
            {name}
          </Tooltip>
          {next ? (
            <RtlReversalIcon>
              <ArrowRightIcon className={styles.styledArrow} />
            </RtlReversalIcon>
          ) : null}
        </div>
      </div>
      {showClose ? <CloseSvgIcon className={styles.styledFileItemClose} onClick={closeClick} /> : null}
      {extend ? extend : null}
    </div>
  );
});
