import { memo, useCallback } from 'react';

import { FileTypeValue, type SpaceEntity } from '@/model/Enterprise/Trash';
import { getFileIcon } from '@/pages/pc/Enterprise/utils';

import styles from './SpaceItem.less';
interface SpaceItemProps {
  space: SpaceEntity;
  toSpace?: string;
  onChangeToSpace: (spaceGuid: string) => void;
}

export const SpaceItem = memo((props: SpaceItemProps) => {
  const { space, onChangeToSpace, toSpace } = props;
  const icon = <div className={styles.styledFileIcon}>{getFileIcon(1, FileTypeValue.Space)}</div>;
  const onClick = useCallback(() => {
    onChangeToSpace(space.guid);
  }, [onChangeToSpace, space.guid]);

  return (
    <div className={`${styles.styledSpaceItem} ${space.guid === toSpace ? styles.selectedItem : ''}`} onClick={onClick}>
      {icon}
      <div className={styles.styleName}>{space.name}</div>
    </div>
  );
});
