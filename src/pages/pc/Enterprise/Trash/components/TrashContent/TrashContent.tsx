import { useScreen } from '../../hooks/useScreen';
import { useTable } from '../../hooks/useTable';
import { TrashScreen } from '../TrashScreen';
import { TrashTable } from '../TrashTable';
import styles from './TrashContent.less';

export const TrashContent = () => {
  const { onResetTable, ...tableProps } = useTable();
  const screenProps = useScreen({
    initTableData: tableProps.initTableData,
    onResetTable,
  });

  return (
    <div className={styles.styledTrashContent}>
      <div className={styles.styledTrashCard}>
        <TrashScreen {...screenProps} />
        <TrashTable {...tableProps} />
      </div>
    </div>
  );
};
