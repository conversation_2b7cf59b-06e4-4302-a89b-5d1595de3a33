import type { SelectProps } from 'antd';
import { Avatar, Select, Spin } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import type { User } from '@/model/Enterprise/Trash';
import { fm } from '@/modules/Locale';

import styles from './DebounceSelect.less';

export interface DebounceSelectProps<ValueType = any>
  extends Omit<SelectProps<ValueType | ValueType[]>, 'options' | 'children'> {
  fetchOptions: (search: string) => Promise<ValueType[]>;
  debounceTimeout?: number;
  defaultOptions: User[];
  onChangeValue: (value: number) => void;
  value: ValueType | undefined | null;
}

export const DebounceSelect = (props: DebounceSelectProps<any>) => {
  const { fetchOptions, debounceTimeout = 300, defaultOptions, onChangeValue, ...defaultProps } = props;
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState<User[]>();
  const [allOptions, setAllOptions] = useState<User[]>([]);
  const fetchRef = useRef(0);
  const i18nText = {
    noData: fm('DebounceSelect.noData'),
  };
  const debounceFetcher = useMemo(() => {
    const loadOptions = (value: string) => {
      if (!value) {
        setOptions(defaultOptions);
        setAllOptions((pre) => pre.concat(defaultOptions));
      }
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);

      fetchOptions(value).then((newOptions) => {
        if (fetchId !== fetchRef.current) {
          // for fetch callback order
          return;
        }
        setOptions(newOptions);
        setAllOptions((pre) => pre.concat(newOptions));
        setFetching(false);
      });
    };

    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout, defaultOptions]);

  const convertUserToOption = useCallback((user: User[]) => {
    return user.map((u) => {
      const Content = () => (
        <div className={styles.styledUserItem}>
          <Avatar className={styles.avatar} src={u.avatar} />
          <div className={styles.styledContent}>
            <div className={styles.styledName}>{u.name}</div>
            <div className={styles.styledEmail}>{u.email}</div>
          </div>
        </div>
      );
      return {
        label: Content(),
        value: u.id,
      };
    });
  }, []);

  const convertValueToName = useCallback(
    (value: string | number) => <>{allOptions?.find((u) => u.id === value)?.name}</>,
    [allOptions],
  );

  const onChange = useCallback(
    (data: { value: number }) => {
      onChangeValue(data?.value);
    },
    [onChangeValue],
  );

  useEffect(() => {
    if (defaultOptions) setAllOptions(defaultOptions);
  }, [defaultOptions]);

  return (
    <Select
      allowClear
      labelInValue
      showSearch
      dropdownRender={(menu) => <div className={styles.styledSelectDropdown}>{menu}</div>}
      filterOption={false}
      getPopupContainer={(trigger) => trigger.parentNode}
      labelRender={({ value }) => convertValueToName(value)}
      notFoundContent={fetching ? <Spin size="small" /> : i18nText.noData}
      options={convertUserToOption(options ?? defaultOptions)}
      style={{ width: '100%' }}
      virtual={false}
      onChange={onChange}
      onSearch={debounceFetcher}
      {...defaultProps}
    />
  );
};
