.styledUserItem {
  display: flex;
  align-items: center;
  color: var(--theme-basic-color-primary);

  .styledContent {
    margin-left: 4px;
    max-width: 184px;
    display: flex;
    flex-direction: column;
    line-height: 20px;

    .styledName {
      color: var(--theme-text-color-default);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .styledEmail {
      color: var(--theme-text-color-secondary);
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.styledSelectDropdown {
  :global {
    .rc-virtual-list-holder {
      max-height: 300px !important;

      .rc-virtual-list-holder-inner .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        background-color: var(--theme-menu-color-bg-hover);
      }

      .ant-select-item-option-active {
        background-color: unset;
      }

      .ant-select-item {
        &:hover {
          background-color: var(--theme-menu-color-bg-hover);
        }
      }
    }
  }
}
