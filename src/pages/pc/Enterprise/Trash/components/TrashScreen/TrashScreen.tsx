import { Button, Tooltip } from 'antd';

import { ReactComponent as ScreenSvg } from '@/assets/images/enterprise/trash/screen.svg';
import { ReactComponent as SearchSvg } from '@/assets/images/enterprise/trash/search.svg';
import { fm } from '@/modules/Locale';

import type { ScreenItemProps } from './ScreenItem';
import { ScreenItem } from './ScreenItem';
import styles from './TrashScreen.less';

export interface TrashScreenProps {
  onReset: () => void;
  onSubmit: () => void;
  screens: ScreenItemProps[];
}

export const TrashScreen = (props: TrashScreenProps) => {
  const { onReset, screens, onSubmit } = props;
  const i18nText = {
    filterCriteria: fm('TrashScreen.filterCriteria'),
    clearCriteria: fm('TrashScreen.clearCriteria'),
    filter: fm('TrashScreen.filter'),
  };
  return (
    <div className={styles.styledTrashScreen}>
      <div className={styles.styledScreenHeader}>
        <div className={styles.styledTitle}>{i18nText.filterCriteria}</div>
        <Tooltip title={i18nText.clearCriteria}>
          <ScreenSvg className={styles.styledScreenSvg} onClick={onReset} />
        </Tooltip>
      </div>
      <div className={styles.styledScreenList}>
        {screens.map((item) => (
          <ScreenItem key={item.id} {...item} />
        ))}
      </div>
      <Button
        className={styles.styledScreenButton}
        icon={<SearchSvg className={styles.styledSearchSvg} />}
        onClick={onSubmit}
      >
        {i18nText.filter}
      </Button>
    </div>
  );
};
