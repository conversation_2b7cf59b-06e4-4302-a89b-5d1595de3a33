.styledScreenItem {
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-top: 12px;
}

.styledLabel {
  color: var(--theme-text-color-medium);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
}

.styledInput {
  margin-top: 8px;

  :global {
    .ant-picker-dropdown {
      .ant-picker-panels {
        .ant-picker-cell-today {
          .ant-picker-cell-inner::before {
            border: 1px solid var(--theme-datepicker-color-cell-active-bg);
          }
        }

        .ant-picker-cell-range-start,
        .ant-picker-cell-range-end {
          .ant-picker-cell-inner {
            background-color: var(--theme-datepicker-color-cell-active-bg);
          }
        }
      }
    }
  }

  .ant-select-rtl .ant-select-arrow {
    right: 11px;
    left: auto;
  }
}
