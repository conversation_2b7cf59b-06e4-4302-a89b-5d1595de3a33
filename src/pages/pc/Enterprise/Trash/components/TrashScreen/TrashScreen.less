.styledTrashScreen {
  display: flex;
  flex-direction: column;
  width: 300px;
  border-right: 1px solid var(--theme-separator-color-lighter);
  padding: 0 16px;
  box-sizing: border-box;
}

.styledScreenHeader {
  padding: 16px 0;
  display: flex;
  justify-content: space-between;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.styledTitle {
  color: var(--theme-text-color-default);
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
}

.styledScreenSvg {
  width: 24px;
  height: 24px;
  cursor: pointer;

  &:hover {
    background: var(--theme-menu-color-bg-hover);
  }

  &:active {
    background: var(--theme-menu-color-bg-active);
  }
}

.styledScreenList {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.styledScreenButton {
  color: var(--theme-text-color-default);
  text-align: center;
  font-family: 'PingFang SC';
  font-size: 14px;
  font-style: normal;
  font-weight: 400;
  line-height: 24px;
  margin: 24px 0;

  &.ant-btn:hover .ant-btn-icon svg {
    path[fill] {
      fill: var(--theme-button-color-primary-hover);
    }
  }
}

.styledSearchSvg {
  width: 18px;
  height: 18px;
}
