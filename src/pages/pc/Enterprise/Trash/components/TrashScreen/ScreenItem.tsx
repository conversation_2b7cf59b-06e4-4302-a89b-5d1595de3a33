import React, { memo } from 'react';

import styles from './ScreenItem.less';
export interface ScreenItemProps {
  id: string;
  label: string;
  input: React.ReactNode;
}

export const ScreenItem = memo((props: ScreenItemProps) => {
  const { label, input } = props;

  return (
    <div className={styles.styledScreenItem}>
      <div className={styles.styledLabel}>{label}</div>
      <div className={styles.styledInput}> {input}</div>
    </div>
  );
});
