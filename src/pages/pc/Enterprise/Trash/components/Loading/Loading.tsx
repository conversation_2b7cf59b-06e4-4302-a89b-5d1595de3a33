import React from 'react';

import styles from './index.less';
import { LoadingGif } from './LoadingGif';
export interface LoadingProps {
  prefix?: string;
  className?: string;
  text?: string;
  hasMarginBottom?: boolean;
}

export const Loading: React.FC<LoadingProps> = (props) => {
  const { prefix = 'sm-loading', className = '', text = '', hasMarginBottom = true } = props;
  return (
    <div
      className={`${prefix} ${className} ${styles.styledLoading}`}
      style={{ marginBottom: hasMarginBottom ? 10 : 'unset' }}
    >
      <span className="loading" />
      {text}
    </div>
  );
};

export const LoadingContainer: React.FC = () => {
  return (
    <div className={styles.styledLoadingContainer}>
      <LoadingGif />
    </div>
  );
};
