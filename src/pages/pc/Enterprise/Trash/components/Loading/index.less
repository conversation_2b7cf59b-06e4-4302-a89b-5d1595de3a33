@keyframes animation-name {
  0% {
    transform: rotate(0);
  }

  50% {
    transform: rotate(180deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.styledLoadingGifContainer {
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.styledLoading {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var('--theme-text-color-secondary');

  .loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    background: no-repeat;
    background-size: 20px 20px;
    animation: animation-name 0.8s infinite linear;
  }
}

.styledLoadingContainer {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
