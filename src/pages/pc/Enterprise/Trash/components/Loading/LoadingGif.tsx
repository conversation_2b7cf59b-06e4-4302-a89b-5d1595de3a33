import { memo } from 'react';

import LoadingGifIcon from '@/assets/images/enterprise/components/loading/loading.gif';

import styles from './index.less';

interface LoadingGifProps {
  size?: number;
}

const DEFAULT_ICON_SIZE = 32;

export const LoadingGif = memo((props?: LoadingGifProps) => {
  const { size = DEFAULT_ICON_SIZE } = props ?? {};
  return (
    <img
      className={styles.styledLoadingGifImage}
      loading="eager"
      src={LoadingGifIcon}
      style={{ width: size, height: size }}
    />
  );
});

export const TableLoadingGif = memo(() => {
  return (
    <div className={styles.styledLoadingGifContainer}>
      <LoadingGif size={20} />
    </div>
  );
});
