import { Tooltip } from 'antd';
import { memo, useCallback } from 'react';

import { ReactComponent as CopySvg } from '@/assets/images/enterprise/trash/copy.svg';
import type { RestoreFileRes } from '@/model/Enterprise/Trash';
import { fm } from '@/modules/Locale';
import { FileItem as CustomFileItem } from '@/pages/pc/Enterprise/common/components/FileItem';
import { copyHandle, getFileIcon } from '@/pages/pc/Enterprise/utils';

import styles from './FileItem.less';
interface FileItemProps extends RestoreFileRes {
  completeUrl: (url: string) => string;
}

export const FileItem = memo((props: FileItemProps) => {
  const { name, url, type, subType, completeUrl, guid } = props;
  const i18nText = {
    copyLink: fm('FileItem.copyLink'),
  };
  const icon = <div className={styles.StyledFileIcon}>{getFileIcon(type, subType)}</div>;

  const onCopy = useCallback(() => {
    copyHandle(completeUrl(url));
  }, [url, completeUrl]);

  const onClick = useCallback(() => {
    window.open(url);
  }, [url]);

  const extend = (
    <Tooltip placement="left" title={i18nText.copyLink}>
      <CopySvg className={styles.styledCopySvg} onClick={onCopy} />
    </Tooltip>
  );

  return (
    <div key={guid} className={styles.StyledItem}>
      <CustomFileItem
        clickable
        preview
        extend={extend}
        guid={guid}
        icon={icon}
        name={name}
        nameDecoration="underline"
        nameWidth={390}
        next={false}
        onPress={onClick}
      />
    </div>
  );
});
