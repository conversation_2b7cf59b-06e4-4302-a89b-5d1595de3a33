import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import styles from './TrashHeader.less';
//  屏蔽自动清理功能，暂未开放
// interface TrashHeaderProps {
//   onAutoClean: (flag: boolean) => void;
// }

export const TrashHeader = () => {
  const i18nText = {
    enterpriseSetting: fm('TrashHeader.enterpriseSetting'),
    enterpriseTrash: fm('TrashHeader.enterpriseTrash'),
    enterpriseId: fm('TrashHeader.enterpriseId'),
  };
  //  屏蔽自动清理功能，暂未开放
  // const { onAutoClean } = props;
  const me = useMeStore((state) => state.me);
  // const trash = useTrashStore((state) => state);
  const goToSetting = () => {
    window.location.href = `/enterprise/settings`;
  };
  //  屏蔽自动清理功能，暂未开放
  // const handleAutoClean = useCallback(() => {
  //   onAutoClean(true);
  // }, [onAutoClean]);

  return (
    <div className={styles.styledTrashHeader}>
      <div className={styles.styledCrumbs}>
        <span onClick={goToSetting}>{i18nText.enterpriseSetting}</span>
      </div>
      <div className={styles.styledTrashTitle}>
        <div className={styles.styledTrashTitleLeft}>
          <div className={styles.styledPageName}>{i18nText.enterpriseTrash}</div>
          <div className={styles.styledEnterpriseName}>
            {me?.team?.name} / {i18nText.enterpriseId} {me?.teamId}
          </div>
        </div>
        {/* 屏蔽自动清理功能，暂未开放 */}
        {/* {trash.isManageMode ? <Button onClick={handleAutoClean}>{s18n('自动清理')}</Button> : null} */}
      </div>
    </div>
  );
};
