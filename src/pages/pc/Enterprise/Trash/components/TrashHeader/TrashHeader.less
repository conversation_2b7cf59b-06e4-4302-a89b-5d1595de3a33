.styledTrashHeader {
  display: flex;
  flex-direction: column;
  padding: 16px 24px;
  min-width: 1200px;
  width: 100%;
  gap: 8px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.styledCrumbs {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;

  span {
    cursor: pointer;
  }
}

.styledTrashTitle {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.styledTrashTitleLeft {
  display: flex;
  align-items: center;
  padding: 6px 0;
}

.styledPageName {
  color: var(--theme-text-color-default);
  font-family: 'PingFang SC';
  font-size: 20px;
  font-weight: 500;
  line-height: 28px;
}

.styledEnterpriseName {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  margin-left: 12px;
}
