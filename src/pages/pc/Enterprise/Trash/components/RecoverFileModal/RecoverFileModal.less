.styledModalHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.styledSubTitle {
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 400;
  line-height: 24px;
}

.styledContent {
  margin-top: 10px;
  width: 100%;
  border-radius: 4px;
  border: 1px solid var(--theme-separator-color-lighter);
}

.styledList {
  height: 360px;
  overflow-x: hidden;
  overflow-y: auto;
  display: flex;
  padding: 12px 0;
  flex-direction: column;

  & > div:first-child {
    margin-top: 0;
  }
}

.styledTips {
  display: flex;
  width: 100%;
  padding: 8px 12px;
  box-sizing: border-box;
  border-top: 1px solid var(--theme-separator-color-lighter);
}

.styledInfoSvg {
  margin-right: 4px;
}
