import { Button, Modal } from 'antd';
import { useMemo } from 'react';

import { ReactComponent as InfoSvg } from '@/assets/images/enterprise/main/question-icon.svg';
import type { RecoverFile, RestoreFileRes, SpaceEntity } from '@/model/Enterprise/Trash';
import { fm } from '@/modules/Locale';

import { FileItem } from '../FileItem';
import { SpaceItem } from '../SpaceItem';
import styles from './RecoverFileModal.less';
export interface RecoverFileModalProps {
  submitLoading: boolean;
  mySpaces: SpaceEntity[];
  selectedFileList: RecoverFile[];
  selectSpaces: RecoverFile[];
  recoverModalOpen: boolean;
  recoverModalNext: boolean;
  toSpace?: string;
  fileLinks: RestoreFileRes[];
  onChangeToSpace: (spaceGuid: string) => void;
  onRecoverModalSubmit: () => void;
  onRecoverModalCancel: () => void;
  onRecoverModalCopyAll: () => void;
  completeUrl: (url: string) => string;
}

export const RecoverFileModal = (props: RecoverFileModalProps) => {
  const {
    submitLoading,
    recoverModalOpen,
    recoverModalNext,
    mySpaces,
    selectSpaces,
    selectedFileList,
    toSpace,
    fileLinks,
    onRecoverModalSubmit,
    onRecoverModalCancel,
    onRecoverModalCopyAll,
    onChangeToSpace,
    completeUrl,
  } = props;

  const i18nText = {
    successfullyRestored: fm('RecoverFileModal.successfullyRestored'),
    restoreFiles: fm('RecoverFileModal.restoreFiles'),
    ok: fm('RecoverFileModal.ok'),
    sure: fm('Space.sure'),
    cancel: fm('Space.cancel'),
    openBlank: fm('RecoverFileModal.openBlank'),
    spaceReset: fm('RecoverFileModal.spaceReset', { length: selectedFileList.length }),
    copyAllLink: fm('RecoverFileModal.copyAllLink'),
    selectSpaceReset: fm('RecoverFileModal.selectSpaceReset', { length: selectSpaces.length }),
  };
  const showTips = useMemo(() => selectSpaces.length && !recoverModalNext, [selectSpaces, recoverModalNext]);

  return (
    <div>
      <Modal
        centered
        cancelText={i18nText.cancel}
        footer={(_, { OkBtn, CancelBtn }) =>
          recoverModalNext ? (
            <OkBtn />
          ) : (
            <>
              <OkBtn />
              <CancelBtn />
            </>
          )
        }
        maskClosable={false}
        okButtonProps={{
          disabled: !recoverModalNext && !toSpace,
          loading: submitLoading,
        }}
        okText={recoverModalNext ? i18nText.ok : i18nText.sure}
        open={recoverModalOpen}
        title={recoverModalNext ? i18nText.successfullyRestored : i18nText.restoreFiles}
        onCancel={onRecoverModalCancel}
        onOk={recoverModalNext ? onRecoverModalCancel : onRecoverModalSubmit}
      >
        <div className={styles.styledModalHeader}>
          <div className={styles.styledSubTitle}>{recoverModalNext ? i18nText.openBlank : i18nText.spaceReset}</div>
          {recoverModalNext ? <Button onClick={onRecoverModalCopyAll}>{i18nText.copyAllLink}</Button> : null}
        </div>
        <div className={styles.styledContent}>
          <div className={styles.styledList}>
            {recoverModalNext
              ? fileLinks.map((file) => <FileItem key={file.guid} {...file} completeUrl={completeUrl} />)
              : mySpaces.map((space) => (
                  <SpaceItem key={space.guid} space={space} toSpace={toSpace} onChangeToSpace={onChangeToSpace} />
                ))}
          </div>
          {showTips ? (
            <div className={styles.styledTips}>
              <InfoSvg className={styles.styledInfoSvg} />
              {i18nText.selectSpaceReset}
            </div>
          ) : null}
        </div>
      </Modal>
    </div>
  );
};
