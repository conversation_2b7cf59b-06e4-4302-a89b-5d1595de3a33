import { Button, Empty, Table } from 'antd';
import React, { useCallback, useEffect, useState } from 'react';

import emptyPng from '@/assets/images/enterprise/components/empty/<EMAIL>';
import type { TrashTableProps } from '@/model/Enterprise/Trash';
import { fm } from '@/modules/Locale';

import { useRecoverFile } from '../../hooks/useRecoverFile';
import { Loading } from '../Loading/Loading';
import { RecoverFileModal } from '../RecoverFileModal';
import styles from './TrashTable.less';

const DEFAULT_HEIGHT = 300;

export const TrashTable = (props: TrashTableProps) => {
  const i18nText = {
    filterResult: fm('TrashTable.filterResult'),
    reset: fm('TrashTable.reset'),
    delete: fm('File.deleteCompletely'),
    noData: fm('TrashTable.noData'),
  };
  const {
    columns,
    dataSource,
    tableLoading,
    selectedRowKeys,
    initTableData,
    onChangeSelectedRowKeys,
    handleDelete,
    handleTableScroll,
  } = props;

  const { onRecoverModalOpen, ...recoverFileProps } = useRecoverFile({
    selectedRowKeys,
    initTableData,
    onChangeSelectedRowKeys,
  });
  const [scrollY, setScrollY] = useState<number>(window.innerHeight - DEFAULT_HEIGHT);

  // table动态高度
  useEffect(() => {
    const handleResize = () => {
      setScrollY(Math.max(window.innerHeight - DEFAULT_HEIGHT, 0));
    };

    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleChangeSelectedRowKeys = (guids: React.Key[]) => {
    const stringGuids = guids.filter((key): key is string => typeof key === 'string');
    onChangeSelectedRowKeys(stringGuids);
  };

  const handleRestore = useCallback(() => {
    onRecoverModalOpen(selectedRowKeys, dataSource);
  }, [onRecoverModalOpen, selectedRowKeys, dataSource]);

  return (
    <div className={styles.styledTrashTable}>
      <div className={styles.styledHeader}>
        <div className={styles.styledTitle}>{i18nText.filterResult}</div>
        <div className={styles.styledButtonGroup}>
          <Button color="primary" disabled={selectedRowKeys.length === 0} variant="solid" onClick={handleRestore}>
            {i18nText.reset}
          </Button>
          <Button danger disabled={selectedRowKeys.length === 0} onClick={handleDelete}>
            {i18nText.delete}
          </Button>
        </div>
      </div>
      <div className={styles.styledTableContainer}>
        <Table
          className={styles.styledTable}
          columns={columns}
          dataSource={dataSource}
          loading={{
            spinning: tableLoading,
            indicator: <Loading text="" />,
          }}
          locale={{
            emptyText: (
              <Empty
                className={styles.styledEmptyContainer}
                description={<div className={styles.styledEmptyText}>{i18nText.noData}</div>}
                image={emptyPng}
              />
            ),
          }}
          pagination={false}
          rowKey="guid"
          rowSelection={{
            type: 'checkbox',
            onChange: handleChangeSelectedRowKeys,
            selectedRowKeys,
            columnWidth: 40,
          }}
          scroll={{
            y: scrollY,
          }}
          onScroll={handleTableScroll}
        />
      </div>
      <RecoverFileModal {...recoverFileProps} />
    </div>
  );
};
