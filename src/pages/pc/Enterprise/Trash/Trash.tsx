import { useEffect } from 'react';
import { history } from 'umi';

import { fm } from '@/modules/Locale';
import { useTrashStore } from '@/store/Enterprise/Trash';
import { useMeStore } from '@/store/Me';

import { Loading } from './components/Loading/Loading';
import { TrashContent } from './components/TrashContent';
import { TrashHeader } from './components/TrashHeader';
import { useCheckPermission } from './hooks/useCheckPermission';
import { ManageEmpty } from './ManageEmpty';

export const Trash = () => {
  const { isEnabled } = useCheckPermission();
  const meId = useMeStore((state) => state.me.id);
  const trash = useTrashStore((state) => state);
  const i18nText = {
    loading: fm('Loading.loading'),
  };
  useEffect(() => {
    if (!meId) {
      return;
    }
    trash.setData({ isManageMode: true });
    trash.setLoading(false);
  }, [meId]);

  if (trash.loading) {
    return <Loading text={i18nText.loading} />;
  }

  if (!isEnabled) {
    history.push('/settings');
    return null;
  }

  return (
    <>
      <TrashHeader />
      {trash.isManageMode ? <TrashContent /> : <ManageEmpty />}
    </>
  );
};
