import { Empty } from 'antd';
import { useCallback, useContext } from 'react';

import { checkManageMode } from '@/api/Trash';
import emptyPng from '@/assets/images/enterprise/components/empty/<EMAIL>';
import { AdminModeDispatchContext } from '@/contexts/AdminMode';
import { fm } from '@/modules/Locale';
import { useTrashStore } from '@/store/Enterprise/Trash';

import styles from './ManageEmpty.less';
export const ManageEmpty = () => {
  const i18nText = {
    verifyTip: fm('ManageEmpty.verifyTip'),
    toVerify: fm('ManageEmpty.toVerify'),
  };
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const trash = useTrashStore((state) => state);
  const goVerify = useCallback(() => {
    openAuthModal?.({
      onSuccess: () => {
        checkManageMode().then((isManageMode) => {
          trash.setData({ isManageMode });
        });
      },
    });
  }, []);

  return (
    <Empty
      className={styles.styledEmpty}
      description={
        <div className={styles.styledManageEmpty}>
          {i18nText.verifyTip}
          <div className={styles.styledVerify} onClick={goVerify}>
            {i18nText.toVerify}
          </div>
        </div>
      }
      image={<img className={styles.emptyImg} src={emptyPng} />}
    />
  );
};
