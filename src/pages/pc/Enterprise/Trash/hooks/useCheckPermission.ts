import { useMemo } from 'react';

import { isAdmin, isCreator, isExpiredEdition } from '@/permissions/helper';
import { useMeStore } from '@/store/Me';

export const useCheckPermission = () => {
  const me = useMeStore((state) => state.me);
  const isExpired = isExpiredEdition(me);

  // 是否有权限（features接口包含'enterprise_trash'属性并且permissions接口包含'manage_enterprise_trash'属性）
  const checkPermission = useMemo(() => {
    return true;
  }, []);

  // 是否是创建者or管理员
  const isAdminCreator = useMemo(() => {
    return me && (isCreator(me) || isAdmin(me));
  }, [me]);

  // 启用（该页面可以访问）有权限或没过期的创管员
  const isEnabled = useMemo(() => {
    return (isAdminCreator && !isExpired) || checkPermission;
  }, [isAdminCreator, isExpired, checkPermission]);

  return {
    isEnabled,
  };
};
