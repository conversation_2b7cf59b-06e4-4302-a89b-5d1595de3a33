import { ExclamationCircleOutlined } from '@ant-design/icons';
import { message, Modal, type TableProps, Tooltip } from 'antd';
import debounce from 'lodash/debounce';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { checkManageMode, deleteFiles, searchApi } from '@/api/Trash';
import { AdminModeDispatchContext } from '@/contexts/AdminMode';
import type { Filter, TrashFileEntity, TrashTableProps } from '@/model/Enterprise/Trash';
import { fm2 } from '@/modules/Locale';
import { FileItem } from '@/pages/pc/Enterprise/common/components/FileItem';
import { dateTime, getFileIcon } from '@/pages/pc/Enterprise/utils';
import { useTrashStore } from '@/store/Enterprise/Trash';

import styles from '../Trash.less';
interface useTrashType extends TrashTableProps {
  initTableData: (params?: Filter, next?: string) => void;
  onResetTable: () => void;
}

// 触底防抖时长
const DEBOUNCE_TIME = 1000;
// 表格行高-加载距离
const ROW_HEIGHT = 45;

export const useTable = (): useTrashType => {
  const trash = useTrashStore((state) => state);
  const { openAuthModal } = useContext(AdminModeDispatchContext);
  const [trashList, setTrashList] = useState<TrashFileEntity[]>([]);
  const [next, setNext] = useState<string>('');
  const [tableLoading, setTableLoading] = useState<boolean>(true);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);

  const onChangeSelectedRowKeys = useCallback((guids: string[]) => {
    setSelectedRowKeys(guids);
  }, []);

  const onResetTable = useCallback(() => {
    setNext('');
    setSelectedRowKeys([]);
  }, []);

  const i18nText = {
    refreshError: fm2('useTable.refreshError'),
    usePeriodExpired: fm2('useTable.usePeriodExpired'),
    reVerify: fm2('useTable.reVerify'),
    doSuccess: fm2('useTable.doSuccess'),
    modalDeleteTitle: fm2('useTable.modalDeleteTitle'),
    modalDeleteContent: fm2('useTable.modalDeleteContent'),
    sure: fm2('Space.sure'),
    cancel: fm2('Space.cancel'),
    deleteCompletely: fm2('File.deleteCompletely'),
    fileName: fm2('useTable.fileName'),
    selectFileName: fm2('useTable.selectFileName', { length: selectedRowKeys.length }),
    creator: fm2('useTable.creator'),
    deleteUser: fm2('useTable.deleteUser'),
    deleteTime: fm2('useScreen.deleteTime'),
  };

  const checkAdminMode = useCallback(() => {
    checkManageMode().then((isManageMode) => {
      if (isManageMode) {
        message.error(i18nText.refreshError);
        return;
      }
      Modal.confirm({
        title: <div className={styles.styledModalTitle}>{i18nText.usePeriodExpired}</div>,
        content: <div className={styles.styledModalSubTitle}>{i18nText.reVerify}</div>,
        icon: <ExclamationCircleOutlined />,
        closeIcon: null,
        onOk: () => {
          openAuthModal?.({
            onSuccess: () => {
              checkManageMode().then((isManageMode) => {
                trash.setData({ isManageMode });
              });
            },
          });
        },
        centered: true,
        okText: i18nText.sure,
        footer: (_, { OkBtn }) => {
          return <OkBtn />;
        },
      });
    });
  }, []);

  const initTableData = useCallback(
    (params?: Filter, nextCode?: string) => {
      setNext('');
      setTableLoading(true);
      searchApi(params, nextCode)
        .then(({ results, next }) => {
          if (nextCode) {
            setTrashList(trashList.concat(results));
          } else {
            setTrashList(results);
          }
          setNext(next);
        })
        .catch(() => {
          setNext('');
          checkAdminMode();
        })
        .finally(() => {
          setTableLoading(false);
        });
    },
    [trashList, checkAdminMode],
  );

  const onDeleteFiles = useCallback(() => {
    deleteFiles(selectedRowKeys)
      .then(() => {
        message.success(i18nText.doSuccess);
        setSelectedRowKeys([]);
        initTableData();
      })
      .catch(() => {
        checkAdminMode();
      });
  }, [selectedRowKeys, checkAdminMode, initTableData]);

  const handleDelete = useCallback(() => {
    Modal.confirm({
      title: <div className={styles.styledModalTitle}>{i18nText.modalDeleteTitle}</div>,
      content: <div className={styles.styledModalSubTitle}>{i18nText.modalDeleteContent}</div>,
      icon: null,
      onOk: onDeleteFiles,
      footer: (_, { OkBtn, CancelBtn }) => {
        return (
          <>
            <OkBtn />
            <CancelBtn />
          </>
        );
      },
      okButtonProps: {
        danger: true,
      },
      centered: true,
      okText: i18nText.deleteCompletely,
      cancelText: i18nText.cancel,
    });
  }, [onDeleteFiles]);

  const handleTableScroll: React.UIEventHandler<HTMLDivElement> = useCallback(
    debounce((e) => {
      if (!next) {
        return; // 加载完毕
      }
      const { scrollTop, clientHeight } = e.target as HTMLDivElement;
      const { childNodes } = e.target as HTMLDivElement;
      const innerTableListHeight = (childNodes[0] as HTMLDivElement).clientHeight;
      if (innerTableListHeight - ROW_HEIGHT < scrollTop + clientHeight) {
        initTableData(undefined, next);
      }
    }, DEBOUNCE_TIME),
    [initTableData, next],
  );

  const columns: TableProps<{
    name: string;
    type: number;
    subType: number;
    guid: string;
    deleteAt: number;
    deleteBy: {
      id: number;
      name: string;
    };
    createBy: {
      id: number;
      name: string;
    };
  }>['columns'] = useMemo(
    () => [
      {
        title: selectedRowKeys.length ? i18nText.selectFileName : i18nText.fileName,
        dataIndex: 'name',
        key: 'name',
        width: '40%',
        className: 'file-columns',
        render: (text, record) => {
          const icon = getFileIcon(record.type, record.subType);
          return <FileItem disableHover guid={record.guid} icon={icon} name={text} />;
        },
      },
      {
        title: i18nText.creator,
        dataIndex: 'createdBy',
        key: 'createdBy',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Tooltip placement="top" title={value.name}>
            {value.name}
          </Tooltip>
        ),
      },
      {
        title: i18nText.deleteUser,
        dataIndex: 'deletedBy',
        key: 'deletedBy',
        width: '20%',
        ellipsis: {
          showTitle: false,
        },
        render: (value) => (
          <Tooltip placement="top" title={value.name}>
            {value.name}
          </Tooltip>
        ),
      },
      {
        title: i18nText.deleteTime,
        dataIndex: 'deletedAt',
        key: 'deletedAt',
        width: '20%',
        render: (value) => dateTime.stringifyAbsoluteFullDateTime(value),
      },
    ],
    [selectedRowKeys.length],
  );

  useEffect(() => {
    initTableData();
  }, []);

  return {
    columns,
    dataSource: trashList,
    handleDelete,
    tableLoading,
    selectedRowKeys,
    onChangeSelectedRowKeys,
    initTableData,
    handleTableScroll,
    onResetTable,
  };
};
