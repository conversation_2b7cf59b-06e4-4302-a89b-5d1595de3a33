import { message } from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';

import { checkManageMode, getSpaces, restoreFiles } from '@/api/Trash';
import type { Filter, RecoverFile, RestoreFileRes, SpaceEntity, TrashFileEntity } from '@/model/Enterprise/Trash';
import { fm2 } from '@/modules/Locale';
import { FileSubType, FileType } from '@/pages/pc/Enterprise/common/components/FileTypeSelect/type';
import { copyHandle } from '@/pages/pc/Enterprise/utils';
import { useTrashStore } from '@/store/Enterprise/Trash';

interface RecoverFileType {
  mySpaces: SpaceEntity[];
  selectedFileList: RecoverFile[];
  selectSpaces: RecoverFile[];
  recoverModalOpen: boolean;
  recoverModalNext: boolean;
  toSpace?: string;
  fileLinks: RestoreFileRes[];
  onChangeToSpace: (spaceGuid: string) => void;
  onRecoverModalSubmit: () => void;
  onRecoverModalCancel: () => void;
  onRecoverModalOpen: (selectedRowKeys: string[], trashList: TrashFileEntity[]) => void;
  onRecoverModalCopyAll: () => void;
  completeUrl: (url: string) => string;
  submitLoading: boolean;
}

interface RecoverFileProps {
  selectedRowKeys: string[];
  initTableData: (params?: Filter, next?: string) => void;
  onChangeSelectedRowKeys: (guids: string[]) => void;
}

export const useRecoverFile = (props: RecoverFileProps): RecoverFileType => {
  const { selectedRowKeys, initTableData, onChangeSelectedRowKeys } = props;
  const trash = useTrashStore((state) => state);
  const [recoverModalOpen, setRecoverModalOpen] = useState<boolean>(false);
  const [recoverModalNext, setRecoverModalNext] = useState<boolean>(false);
  const [mySpaces, setMySpaces] = useState<SpaceEntity[]>([]);
  const [toSpace, setToSpace] = useState<string>();
  const [selectedFileList, setSelectedFileList] = useState<RecoverFile[]>([]);
  const [fileLinks, setFileLinks] = useState<RestoreFileRes[]>([]);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const i18nText = {
    resetSuccess: fm2('useRecoverFile.resetSuccess'),
    spaceError: fm2('useRecoverFile.spaceError'),
  };
  const onChangeToSpace = useCallback((spaceId: string) => {
    setToSpace(spaceId);
  }, []);

  const onRecoverModalOpen = useCallback((selectedRowKeys: string[], trashList: TrashFileEntity[]) => {
    setRecoverModalOpen(true);
    setSelectedFileList(
      selectedRowKeys.map((guid) => {
        const file = trashList.find((item) => item.guid === guid) as TrashFileEntity;
        return {
          guid: file.guid,
          name: file.name,
          type: file.type,
          subType: file.subType,
        };
      }),
    );
  }, []);

  const onRecoverModalCancel = useCallback(() => {
    setRecoverModalOpen(false);
    setRecoverModalNext(false);
    setToSpace(undefined);
    setFileLinks([]);
  }, []);

  const completeUrl = useCallback((url: string) => {
    return url;
  }, []);

  const onRecoverModalCopyAll = useCallback(() => {
    const str = fileLinks.map((file) => `${file.name}\n${completeUrl(file.url)}`).join('\n');
    copyHandle(str);
  }, [fileLinks, completeUrl]);

  const onRecoverModalSubmit = useCallback(() => {
    if (!toSpace) {
      return;
    }
    setSubmitLoading(true);
    restoreFiles(selectedRowKeys, toSpace)
      .then(({ files }) => {
        message.success(i18nText.resetSuccess);
        setFileLinks(files);
        initTableData();
        onChangeSelectedRowKeys([]);
        setRecoverModalNext(true);
      })
      .catch(() => {
        checkManageMode().then((isManageMode) => {
          trash.setData({ isManageMode });
        });
      })
      .finally(() => {
        setSubmitLoading(false);
      });
  }, [toSpace, selectedRowKeys, initTableData, onChangeSelectedRowKeys]);

  const selectSpaces = useMemo(() => {
    return selectedFileList.filter((item) => {
      return item.type === FileType.FolderOrSpace && item.subType === FileSubType.space;
    });
  }, [selectedFileList]);

  const initMySpaces = useCallback(() => {
    getSpaces()
      .then((spaces) => {
        setMySpaces(spaces ?? []);
      })
      .catch(() => {
        message.error(i18nText.spaceError);
      });
  }, []);

  useEffect(() => {
    initMySpaces();
  }, []);

  return {
    submitLoading,
    mySpaces,
    selectSpaces,
    selectedFileList,
    recoverModalOpen,
    recoverModalNext,
    toSpace,
    fileLinks,
    onChangeToSpace,
    onRecoverModalSubmit,
    onRecoverModalCancel,
    onRecoverModalCopyAll,
    onRecoverModalOpen,
    completeUrl,
  };
};
