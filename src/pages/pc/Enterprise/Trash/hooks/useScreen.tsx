import { dayjs } from '@shimo/lo-dates/src/dayjs';
import { DatePicker, Input } from 'antd';
import type { ChangeEvent } from 'react';
import { useCallback, useMemo, useState } from 'react';

import { searchUserApi } from '@/api/Trash';
import type { Filter, User } from '@/model/Enterprise/Trash';
import { fm2 } from '@/modules/Locale';
import { FileTypeSelect } from '@/pages/pc/Enterprise/common/components/FileTypeSelect';

import { DebounceSelect } from '../components/DebounceSelect';
import type { ScreenItemProps } from '../components/TrashScreen/ScreenItem';
import type { TrashScreenProps } from '../components/TrashScreen/TrashScreen';

const { RangePicker } = DatePicker;
type NoUndefinedRangeValueType<T> = T extends undefined ? never : T;
export const useScreen = ({
  initTableData,
  onResetTable,
}: {
  initTableData: (params?: Filter) => void;
  onResetTable: () => void;
}): TrashScreenProps => {
  const [keyword, setKeyword] = useState<string>('');
  const [fileTypeIds, setFleTypeIds] = useState<string[]>([]);
  const [creator, setCreator] = useState<number>();
  const [deletor, setDeletor] = useState<number>();
  const [begin, setBegin] = useState<string>('');
  const [end, setEnd] = useState<string>('');
  const [recentContact] = useState<User[]>([]);
  const i18nText = {
    searchFile: fm2('useScreen.searchFile'),
    searchFilePlaceholder: fm2('useScreen.searchFilePlaceholder'),
    fileType: fm2('useScreen.fileType'),
    searchDeleteUser: fm2('useScreen.searchDeleteUser'),
    searchUserPlaceholder: fm2('useScreen.searchUserPlaceholder'),
    searchCreateUser: fm2('useScreen.searchCreateUser'),
    deleteTime: fm2('useScreen.deleteTime'),
  };
  const onChangeKeyword = useCallback((e: ChangeEvent<HTMLInputElement>) => {
    setKeyword(e.target.value);
  }, []);

  const onChangeDeletor = useCallback((value: number) => {
    setDeletor(value);
  }, []);
  const onChangeDate = useCallback((_: NoUndefinedRangeValueType<any> | null, dateStrings: [string, string]) => {
    setBegin(dateStrings[0]);
    setEnd(dateStrings[1]);
  }, []);

  const onChangeCreator = useCallback((value: number) => {
    setCreator(value);
  }, []);

  /**
   * 重置页面状态
   */
  const onReset = useCallback(() => {
    setKeyword('');
    setFleTypeIds([]);
    setCreator(undefined);
    setDeletor(undefined);
    setBegin('');
    setEnd('');
    initTableData();
    onResetTable();
  }, [initTableData, onResetTable]);

  const onSubmit = useCallback(() => {
    const params: Filter = {
      keyword,
      fileTypeIds,
      creator,
      deletor,
      begin,
      end,
    };
    initTableData(params);
    onResetTable();
  }, [keyword, fileTypeIds, creator, deletor, begin, end, initTableData, onResetTable]);

  /**
   * 筛选条件
   */
  const screens: ScreenItemProps[] = useMemo(
    () => [
      {
        id: '1',
        label: i18nText.searchFile,
        input: (
          <Input allowClear placeholder={i18nText.searchFilePlaceholder} value={keyword} onChange={onChangeKeyword} />
        ),
      },
      {
        id: '2',
        label: i18nText.fileType,
        input: <FileTypeSelect value={fileTypeIds} onChange={setFleTypeIds} />,
      },
      {
        id: '3',
        label: i18nText.searchDeleteUser,
        input: (
          <DebounceSelect
            defaultOptions={recentContact}
            fetchOptions={searchUserApi}
            placeholder={i18nText.searchUserPlaceholder}
            value={deletor}
            onChangeValue={onChangeDeletor}
          />
        ),
      },
      {
        id: '4',
        label: i18nText.searchCreateUser,
        input: (
          <DebounceSelect
            defaultOptions={recentContact}
            fetchOptions={searchUserApi}
            placeholder={i18nText.searchUserPlaceholder}
            value={creator}
            onChangeValue={onChangeCreator}
          />
        ),
      },
      {
        id: '5',
        label: i18nText.deleteTime,
        input: (
          <RangePicker
            getPopupContainer={(trigger) => trigger.parentNode! as HTMLElement}
            value={begin && end ? [dayjs(begin), dayjs(end)] : null}
            onChange={onChangeDate}
          />
        ),
      },
    ],
    [
      keyword,
      deletor,
      creator,
      begin,
      end,
      fileTypeIds,
      recentContact,
      onChangeKeyword,
      onChangeDeletor,
      onChangeCreator,
      onChangeDate,
    ],
  );
  return { onSubmit, onReset, screens };
};
