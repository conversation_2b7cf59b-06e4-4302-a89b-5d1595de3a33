.content {
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  padding: 20px;
  border: 1px solid var(--theme-separator-color-lighter);

  .infoRow {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .infoItem {
      width: 255px;

      .infoItemTitle {
        color: var(--theme-text-color-secondary);
        font-size: 14px;
        line-height: 24px;
        margin-bottom: 4px;
      }

      .nfoItemValue {
        color: var(--theme-text-color-default);
        font-size: 16px;
        font-weight: 400;
        line-height: 24px;
        word-break: break-all;
      }
    }
  }

  .infoLink {
    color: var(--theme-text-color-guidance) !important;
    text-underline-offset: 3px;
  }
}
