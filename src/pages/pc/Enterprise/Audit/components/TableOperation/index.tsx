import './index.less';

import { PlusOutlined } from '@ant-design/icons';
import { Button, DatePicker, Input, Row, Select, Space, Spin } from 'antd';
import { Col, type DatePickerProps } from 'antd/lib';
import classNames from 'classnames';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';
import debounce from 'lodash/debounce';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { defaultFilters, searchUserList } from '@/api/Audit';
import type { Filters, SelectionItem, User } from '@/model/audit';
import { fm } from '@/modules/Locale';
import { ReactComponent as CloseSVG } from '@/pages/pc/Enterprise/assets/audit/close-outline.svg';
import LoadingGif from '@/pages/pc/Enterprise/assets/audit/loading.gif';
import selectEmpty from '@/pages/pc/Enterprise/assets/audit/table_empty.svg';

import styles from './index.less';

const { RangePicker } = DatePicker;
const getYearMonth = (date: Dayjs) => date.year() * 12 + date.month();

interface Props {
  changeOperationVisible: (v: boolean) => void;
  changeOperationHeight?: (v: number) => void;
  onSearch: (filters?: Filters) => void;
  selectionList: SelectionItem[];
  setSelectionList: (select: SelectionItem[]) => void;
}

export const TableOperation: React.FC<Props> = ({
  changeOperationVisible,
  onSearch,
  changeOperationHeight,
  selectionList,
  setSelectionList,
}) => {
  const i18nText_search = {
    search: fm('Operation.search'),
    clear: fm('Operation.clear'),
    operate: fm('Operation.operate'),
    dateRange: fm('Operation.dateRange'),
  };

  const [filters, setFilters] = useState<Filters>(defaultFilters);
  const [userList, setUserList] = useState<User[]>([]);
  const [userListLoading, setUserListLoading] = useState(false);
  const operationRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!changeOperationHeight) return;
    const currentElement = operationRef.current;
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { height } = entry.contentRect;
        changeOperationHeight(height);
      }
    });
    if (currentElement) {
      resizeObserver.observe(currentElement);
    }

    return () => {
      if (currentElement) {
        resizeObserver.unobserve(currentElement);
      }
    };
  }, [changeOperationHeight, operationRef]);

  const handleUserSearch = useCallback((keyword?: string) => {
    setUserListLoading(true);
    searchUserList(keyword)
      .then((res) => {
        if (res.data.results) {
          setUserList(res.data.results);
        }
      })
      .finally(() => {
        setUserListLoading(false);
      });
  }, []);

  useEffect(() => {
    handleUserSearch();
  }, [handleUserSearch]);

  const handleUserChange = (useId: string) => {
    setFilters((pre) => ({ ...pre, operator: useId }));
  };

  const handleFileNameChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFilters((pre) => ({ ...pre, guid: e.target.value }));
  };

  const onRangeChange = (fromTo: any) => {
    if (fromTo?.[0] !== null && fromTo[1] !== null) {
      const startDate = fromTo[0].valueOf();
      const endDate = fromTo[1].valueOf();
      setFilters((pre) => ({ ...pre, dates: [startDate, endDate] }));
    }
  };

  const handleSearch = () => {
    const newFilters = {
      ...filters,
      operations: selectionList.map((item: SelectionItem) => item?.id),
    };
    setFilters(newFilters);
    onSearch(newFilters);
  };

  const deleteOneOperation = (id: string) => {
    const newSelectionList = selectionList.filter((item: SelectionItem) => item.id !== id);
    setSelectionList(newSelectionList);
  };

  /**
   * 清除筛选操作
   */
  const clearOperations = () => {
    setSelectionList([]);
    setFilters((pre) => ({
      ...defaultFilters,
      dates: pre.dates,
    }));
  };
  /**
   * 日期选择器只支持前后30天的选择范围
   */
  const disabled30DaysDate: DatePickerProps['disabledDate'] = (current, { from, type }) => {
    if (from) {
      const MAX_DAY = 30;
      const minDate = from.add(-MAX_DAY, 'days');
      const maxDate = from.add(MAX_DAY, 'days');

      switch (type) {
        case 'year':
          return current.year() < minDate.year() || current.year() > maxDate.year();

        case 'month':
          return getYearMonth(current) < getYearMonth(minDate) || getYearMonth(current) > getYearMonth(maxDate);

        default:
          return Math.abs(current.diff(from, 'days')) >= MAX_DAY;
      }
    }

    return false;
  };

  const handleDatePickerOpen = (open: boolean) => {
    if (open) {
      const noteTextId = 'noteTextId';
      let datePickerFooter = document.querySelector('.ant-picker-ranges');

      const addNoteDateFooter = () => {
        const noteText = document.createElement('li');
        noteText.innerHTML = i18nText_search.dateRange;
        noteText.id = noteTextId;
        noteText.setAttribute('style', `color: var(--theme-text-color-secondary)`);
        datePickerFooter?.insertBefore(noteText, datePickerFooter?.firstChild);
      };

      if (datePickerFooter) {
        if (document.getElementById(noteTextId)) {
          return;
        }

        addNoteDateFooter();
      }

      if (!datePickerFooter) {
        setTimeout(() => {
          datePickerFooter = document.querySelector('.ant-picker-ranges');
          addNoteDateFooter();
        }, 100);
      }
    }
  };

  return (
    <div ref={operationRef} className={styles.tableOperation}>
      <Row className={styles.formList}>
        <div className={styles.rangePickerFormItem}>
          <div className={styles.formItemName}>{fm('Operation.filter.time')}</div>
          <RangePicker
            className={styles.optionRangePicker}
            disabledDate={disabled30DaysDate}
            format={'YYYY-MM-DD HH:mm'}
            showTime={{ format: 'HH:mm' }}
            value={filters.dates && [dayjs(filters.dates[0]), dayjs(filters.dates[1])]}
            onChange={onRangeChange}
            onOpenChange={handleDatePickerOpen}
          />
        </div>
        <Col className={styles.formItem}>
          <div className={styles.formItemName}>{fm('Operation.filter.operator')}</div>
          <Select
            showSearch
            allowClear={{
              clearIcon: <CloseSVG />,
            }}
            defaultActiveFirstOption={false}
            filterOption={false}
            notFoundContent={
              userListLoading ? (
                <Spin indicator={<img src={LoadingGif} />} size="small" />
              ) : (
                <div className={styles.operationEmptyHolder}>
                  <img alt={fm('Operation.filter.noData')} src={selectEmpty} />
                  <div className={styles.holderText}>{fm('Operation.filter.noData')}</div>
                </div>
              )
            }
            optionRender={({ data }) => {
              return (
                <div className={styles.uerItem}>
                  <img className={styles.avatarIMG} src={data.avatar} />
                  <div>
                    <div className={styles.userName}>{data.label}</div>
                    <div className={styles.email}>{data.email}</div>
                  </div>
                </div>
              );
            }}
            options={userList.map((item: User) => ({
              value: item.user.id,
              label: item.user.name,
              email: item.user.email,
              avatar: item.user.avatar,
            }))}
            placeholder={fm('Operation.filter.userEmailTel')}
            style={{
              width: '100%',
            }}
            suffixIcon={null}
            value={filters.operator || undefined}
            onChange={handleUserChange}
            onSearch={debounce(handleUserSearch, 300)}
          />
        </Col>
        <Col className={styles.formItem}>
          <div className={styles.formItemName}>{fm('Operation.filter.file')}</div>
          <Input placeholder={fm('Operation.filter.fileUrl')} value={filters.guid} onInput={handleFileNameChange} />
        </Col>
      </Row>
      <Row className={styles.search}>
        <div
          className={classNames({
            [styles.formItem]: true,
          })}
        >
          <div
            className={classNames({
              [styles.formItemName]: true,
              [styles.hasValue]: true,
            })}
          >
            {fm('Operation.filter.operation')}
          </div>
          <div className={styles.operationAdd}>
            {selectionList.map((item) => {
              return (
                <div key={item.id} className={styles.operationButton}>
                  <span style={{ marginRight: 4 }}>{item.name}</span>
                  <span
                    className={styles.closeBtn}
                    onClick={() => {
                      deleteOneOperation(item.id);
                    }}
                  >
                    <CloseSVG />
                  </span>
                </div>
              );
            })}
            <Button icon={<PlusOutlined />} type="dashed" onClick={() => changeOperationVisible(true)}>
              {i18nText_search.operate}
            </Button>
          </div>
        </div>
        <Space align="start" size={8}>
          <Button type="primary" onClick={handleSearch}>
            {i18nText_search.search}
          </Button>
          <Button onClick={clearOperations}>{i18nText_search.clear}</Button>
        </Space>
      </Row>
    </div>
  );
};
