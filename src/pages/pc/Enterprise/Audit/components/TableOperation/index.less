.tableOperation {
  display: flex;
  padding: 20px;
  flex-direction: column;
  align-items: flex-start;
  border-radius: 8px;
  border: 1px solid var(--theme-basic-color-lighter);
  background: var(--theme-layout-color-bg-white);
  margin: 16px 0;

  .formList {
    margin-bottom: 16px;
    width: 100%;
  }

  .search {
    width: 100%;

    .hasValue {
      height: 100%;
      align-items: start;
      padding-top: 5px;
    }
  }
}

.firstRow {
  width: 100%;
  display: flex;
  margin-bottom: 16px;
}

.formItem {
  display: flex;
  align-items: center;
  flex: 1;
  margin-right: 24px;

  &:last-child {
    margin-right: 0;
  }
}

.rangePickerFormItem {
  display: flex;
  align-items: center;
  margin-right: 24px;
}

.formItemName {
  flex-shrink: 0;
  font-size: 14px;
  line-height: 22px;
  color: var(--theme-text-color-default);
  margin-right: 8px;
}

.plusOperationBtn {
  display: flex;
  padding: 4px 10px;
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  box-shadow: 0 2px 0 0 rgba(0, 0, 0, 2%);
  cursor: pointer;
  color: var(--theme-text-color-default);
}

.plusIcon {
  margin-right: 4px;
}

.searchButtons {
  display: flex;
  align-items: center;

  & > button:first-child {
    margin-right: 4px;
  }
}

.uerItem {
  font-size: 14px;
  line-height: 22px;
  display: flex;
  align-items: center;
}

.avatarIMG {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin-right: 8px;
}

.email {
  color: var(--theme-text-color-secondary);
}

.userName {
  color: var(--theme-text-color-default);
}

.operationAdd {
  display: flex;
  flex-wrap: wrap;
}

.operationButton {
  padding: 4px 10px;
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  display: flex;
  align-items: center;
  margin-right: 4px;
  margin-bottom: 6px;

  svg {
    display: flex;
    align-items: center;
    color: var(--theme-text-color-secondary);

    &:hover {
      color: var(--theme-text-color-default);
    }

    cursor: pointer;
  }
}

.operationEmptyHolder {
  display: flex;
  flex-direction: column;
  align-items: center;

  img {
    width: 94px;
    height: 70px;
    margin: 30px 0;
  }
}

.holderImg {
  width: 160px;
  height: 130px;
}

.holderText {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  margin-bottom: 12px;
}
