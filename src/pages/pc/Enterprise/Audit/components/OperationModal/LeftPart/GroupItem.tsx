import { Checkbox } from 'antd';
import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import classNames from 'classnames';
import type { FC } from 'react';

import type { operationNode } from '@/model/audit';
import { fm } from '@/modules/Locale';

import { useAuditStore } from '../../..//store';
import styles from './index.less';

/**
 * 根据关键字生成高亮字段结构
 * @param text - 文本
 * @param keyword - 搜索字段
 */
export const generateHighlightText = (
  text: string,
  keyword?: string,
): {
  highlight: boolean;
  value: string;
}[] => {
  if (!keyword) {
    return [{ value: text, highlight: false }];
  }
  const regex = RegExp(keyword);
  const match = regex.exec(text);
  if (match) {
    const { index } = match;

    const matchEnd = index + keyword.length;
    const texts = [
      {
        value: text.slice(0, index),
        highlight: false,
      },
      { value: text.slice(index, matchEnd), highlight: true },
      {
        value: text.slice(matchEnd),
        highlight: false,
      },
    ];
    return texts.filter((item) => item.value);
  }
  return [
    {
      value: text,
      highlight: false,
    },
  ];
};

interface Props {
  groupIndeterminate: boolean;
  groupItemChecked: boolean;
  groupItem: operationNode;
  groupCheckChange: (e: CheckboxChangeEvent) => void;
  operationCheckChange: (e: CheckboxChangeEvent, id: string) => void;
  currentGroupSet: Set<string>;
  keyword?: string;
}

export const GroupItem: FC<Props> = ({
  groupIndeterminate,
  groupItemChecked,
  groupCheckChange,
  groupItem,
  operationCheckChange,
  currentGroupSet,
  keyword,
}) => {
  const { description } = useAuditStore((state) => state);
  const groupName = description?.[groupItem.id] ?? '';
  const groupContent = generateHighlightText(groupName, keyword);

  return (
    <div className={styles.operationGroupContainer}>
      <div className={styles.operationGroupTitle}>
        <Checkbox checked={groupItemChecked} indeterminate={groupIndeterminate} onChange={groupCheckChange}>
          <span>
            <span className={styles.groupLabel}>
              {groupContent.map((item) => {
                return (
                  <span
                    key={`${item.value}`}
                    className={classNames({
                      [styles.highlight]: item.highlight,
                      [styles.text]: true,
                    })}
                  >
                    {item.value}
                  </span>
                );
              })}
            </span>
            <span className={styles.numNote}>
              {groupItem?.children?.length || 0}
              {fm('Operation.nape')}
            </span>
          </span>
        </Checkbox>
        {currentGroupSet.size > 0 && (
          <span className={styles.selectedNum}>{fm('Operation.selectNapeNum', { num: currentGroupSet.size })}</span>
        )}
      </div>
      <div className={styles.operationList}>
        {groupItem?.children?.map((operationItem: { id: string }) => {
          const name = description?.[operationItem.id] ?? '';
          const content = generateHighlightText(name, keyword);
          return (
            <div key={operationItem.id} className={styles.operationItem}>
              <Checkbox
                checked={currentGroupSet.has(operationItem.id)}
                onChange={(e) => {
                  operationCheckChange(e, operationItem.id);
                }}
              >
                {content.map((item) => {
                  return (
                    <span
                      key={`${item.value}`}
                      className={classNames({
                        [styles.highlight]: item.highlight,
                        [styles.text]: true,
                      })}
                    >
                      {item.value}
                    </span>
                  );
                })}
              </Checkbox>
            </div>
          );
        })}
      </div>
    </div>
  );
};
