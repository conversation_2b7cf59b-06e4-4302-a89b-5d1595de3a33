.leftPart {
  flex-basis: 408px;
  height: 100%;
  display: flex;
  flex-direction: column;
  margin-right: 10px;

  .ant-input-group-addon {
    button.ant-btn {
      min-width: auto;
    }
  }
}

.actionContainer {
  margin-top: 10px;
  border-radius: 2px;
  border: 1px solid var(--theme-separator-color-lighter);
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 0;
}

.tabs {
  display: flex;
  padding: 0 12px;
  align-items: flex-end;
  align-self: stretch;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
}

.tab {
  color: var(--theme-text-color-default);
  font-size: 13px;
  line-height: 20px;
  padding: 8px 0;
  cursor: pointer;
  position: relative;
  margin-right: 24px;

  &::after {
    position: absolute;
    width: 100%;
    height: 2px;
    background: var(--theme-text-color-default);
    content: '';
    bottom: 0;
    left: 0;
    display: none;
  }

  &.active::after {
    display: block;
  }

  &.active {
    .tabTitle {
      color: var(--theme-text-color-default);
    }
  }

  .tabTitle {
    color: var(--theme-text-color-secondary);
  }

  .num {
    position: absolute;
    right: -20px;
    top: 50%;
    transform: translateY(-50%);
    border-radius: 10px;
    background: var(--theme-text-color-guidance);
    color: var(--theme-text-color-white);
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
  }
}

.tabContentContainer {
  overflow-y: auto;
  padding-bottom: 24px;
  flex: 1;
  height: 0;
}

.operationGroupContainer {
  margin-top: 8px;
}

.operationGroupTitle {
  background: var(--theme-menu-color-bg-hover);
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
}

.groupLabel {
  color: var(--theme-text-color-default);
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
}

.numNote {
  color: var(--theme-text-color-secondary);
  font-size: 13px;
  line-height: 20px;
  margin-left: 6px;
}

.selectedNum {
  color: var(--theme-text-color-guidance);
  font-size: 13px;
  line-height: 20px;
}

.operationList {
  display: flex;
  flex-wrap: wrap;
  position: relative;
  padding: 8px 0 0 36px;
}

.operationItem {
  flex-basis: calc(50% - 6px);
  margin-bottom: 8px;

  .ant-checkbox-wrapper {
    .ant-checkbox {
      align-self: start;
      padding-top: 3px;
    }
  }

  &:nth-child(2n + 1) {
    margin-right: 8px;
  }
}

.searchList {
  height: 456px;
  overflow-y: auto;
  padding-bottom: 24px;
  box-sizing: border-box;

  & > div:first-child {
    margin-top: 0;
  }
}

.text {
  color: var(--theme-text-color-default);
  font-size: 13px;

  &.highlight {
    background: var(--theme-text-color-highlight-bg);
  }
}

.emptyHolder {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.emptyImg {
  width: 220px;
  height: 180px;
}

.emptyText {
  color: var(--theme-text-color-secondary);
  font-size: 14px;
  line-height: 24px;
}
