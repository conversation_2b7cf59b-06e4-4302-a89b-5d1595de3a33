import type { CheckboxChangeEvent } from 'antd/es/checkbox';
import classNames from 'classnames';
import type { FC } from 'react';
import { useEffect, useState } from 'react';

import type { operationNode, SelectionList } from '@/model/audit';

import { useAuditStore } from '../../../store';
import { GroupItem } from './GroupItem';
import styles from './index.less';

interface Props {
  removeOperationFromList: (id: string) => void;
  addOperationToList: (v: { operationId: string; groupId: string; name: string; tabId: string }) => void;
  selectionList: SelectionList;
}

export const TabModeList: FC<Props> = ({ removeOperationFromList, addOperationToList, selectionList }) => {
  const DefaultTabIndex = 0;
  const { description, auditEvents } = useAuditStore((state) => state);

  const DefaultSelectData = (auditEvents || []).map((tabItem: operationNode) => {
    return (tabItem?.children || []).map(() => new Set<string>());
  });

  const [activeTab, setActiveTab] = useState(auditEvents?.[DefaultTabIndex]);
  const [activeTabIndex, setActiveTabIndex] = useState(DefaultTabIndex);

  const [selectedData, setSelectedData] = useState([...DefaultSelectData]);

  useEffect(() => {
    const defaultSelectData = [...DefaultSelectData];
    selectionList.forEach(({ groupId, id, tabId }) => {
      const tabIndex = auditEvents.findIndex((tabItem: operationNode) => tabItem.id === tabId);
      const groupIndex = auditEvents[tabIndex]?.children?.findIndex((group: operationNode) => group.id === groupId);
      const groupSet = defaultSelectData?.[tabIndex]?.[`${groupIndex as number}`];
      groupSet?.add(id);
    });

    setSelectedData([...defaultSelectData]);
  }, [DefaultSelectData, auditEvents, selectionList]);

  /**
   * 单个操作选中状态发生变化时
   */
  const operationChange = ({
    e,
    groupIndex,
    operationItemId,
    groupId,
    tabId,
  }: {
    e: CheckboxChangeEvent;
    groupIndex: number;
    operationItemId: string;
    groupId: string;
    tabId: string;
  }) => {
    const tabItemArray = selectedData[activeTabIndex];
    const groupSet = tabItemArray[groupIndex];

    if (e.target.checked) {
      groupSet.add(operationItemId);
      addOperationToList({
        groupId,
        operationId: operationItemId,
        name: description[operationItemId] || '',
        tabId,
      });
    } else {
      removeOperationFromList(operationItemId);
      groupSet.delete(operationItemId);
    }

    // 触发更新
    setSelectedData([...selectedData]);
  };

  /**
   * 整个组选择变化
   */
  const groupOperationCheckChange = ({
    e,
    groupIndex,
    groupId,
    tabId,
  }: {
    e: CheckboxChangeEvent;
    groupIndex: number;
    groupId: string;
    tabId: string;
  }) => {
    const operationSet = selectedData[activeTabIndex][groupIndex];
    const currentOperationList = auditEvents?.[activeTabIndex]?.children?.[groupIndex].children;
    (currentOperationList || []).forEach((item: operationNode) => {
      if (!e.target.checked) {
        // 删除
        removeOperationFromList(item.id);
      } else {
        // 添加
        operationSet.add(item.id);
        addOperationToList({
          tabId,
          groupId,
          operationId: item.id,
          name: description[item.id] || '',
        });
      }
    });
    if (!e.target.checked) {
      operationSet.clear();
    }

    setSelectedData([...selectedData]);
  };

  return (
    <>
      <div className={styles.tabs}>
        {(auditEvents || []).map((item: operationNode, tabIndex: number) => {
          const handleTabClick = () => {
            setActiveTab(item);
            setActiveTabIndex(tabIndex);
          };
          const totalSelect = selectedData[tabIndex].reduce((lastV: number, curSetItem: Set<string>) => {
            return lastV + curSetItem.size;
          }, 0);

          return (
            <div
              key={item.id}
              className={classNames({
                [styles.active]: item.id === activeTab.id,
                [styles.tab]: true,
              })}
              onClick={handleTabClick}
            >
              <span className={styles.tabTitle}>{description[item.id]}</span>
              {<div className={styles.num}>{totalSelect || 0}</div>}
            </div>
          );
        })}
      </div>
      <div className={styles.tabContentContainer}>
        {(activeTab?.children || []).map((item: operationNode, groupIndex: number) => {
          const currentGroupSet = selectedData[activeTabIndex][groupIndex];
          const total = auditEvents?.[activeTabIndex]?.children?.[groupIndex]?.children?.length || 0;

          return (
            <GroupItem
              key={item.id}
              currentGroupSet={currentGroupSet}
              groupCheckChange={(e) => {
                groupOperationCheckChange({
                  e,
                  groupIndex,
                  groupId: item.id,
                  tabId: activeTab.id,
                });
              }}
              groupIndeterminate={currentGroupSet.size > 0 && currentGroupSet.size < total}
              groupItem={item}
              groupItemChecked={currentGroupSet.size === total}
              operationCheckChange={(e, id) => {
                operationChange({
                  e,
                  groupIndex,
                  operationItemId: id,
                  groupId: item.id,
                  tabId: activeTab.id,
                });
              }}
            />
          );
        })}
      </div>
    </>
  );
};
