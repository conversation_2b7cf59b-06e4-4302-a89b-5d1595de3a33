import { Input } from 'antd';
import type { FC } from 'react';
import { useEffect, useState } from 'react';

import type { operationNode, SelectionList } from '@/model/audit';
import { fm } from '@/modules/Locale';

import { useAuditStore } from '../../../store';
import styles from './index.less';
import { SearchList } from './SearchList';
import { TabModeList } from './TabModeList';

const { Search } = Input;

interface Props {
  removeOperationFromList: (id: string) => void;
  addOperationToList: (v: { operationId: string; groupId: string; tabId: string; name: string }) => void;
  selectionList: SelectionList;
}

export const LeftPart: FC<Props> = ({ removeOperationFromList, addOperationToList, selectionList }) => {
  const [keyword, setKeyword] = useState<string>('');
  const [searchList, setSearchList] = useState<operationNode[]>([]);
  const [listSets, setListSets] = useState<Set<string>[]>([]);
  const { description, auditEvents } = useAuditStore((state) => state);

  const getListSet = (list: operationNode[], selectionList: SelectionList) => {
    const listSet: Set<string>[] = [];
    list.forEach((groupItem) => {
      const setItem = new Set<string>();
      const selectionInCurrentGroup = selectionList.filter(
        (item: { groupId: string }) => item.groupId === groupItem.id,
      );

      selectionInCurrentGroup.forEach((operationItem: { id: string }) => {
        setItem.add(operationItem.id);
      });

      listSet.push(setItem);
    });

    return listSet;
  };
  /**
   * 搜索
   * @param keyword - 搜索字段
   */
  const handleSearch = (keyword: string) => {
    setKeyword(keyword);
    const regex = RegExp(keyword);
    const isMatch = (id: string) => regex.exec(description[id] || '');
    const resultData: operationNode[] = [];

    // 本地搜索功能
    auditEvents.forEach((tabItem: operationNode) => {
      (tabItem.children || []).forEach((groupItem) => {
        if (isMatch(groupItem.id)) {
          // group 完整匹配 根据之前的逻辑，当组完全匹配时，整个组都要显示包括没有匹配的子元素
          resultData.push({ ...groupItem, tabId: tabItem.id });
        } else {
          // 子元素匹配 根据之前的逻辑 当子元素匹配时 只显上层父元素和匹配的子元素
          const matchChildren = (groupItem.children || []).filter((item: { id: string }) => isMatch(item.id));

          if (matchChildren.length > 0) {
            resultData.push({
              ...groupItem,
              tabId: tabItem.id,
              children: matchChildren,
            });
          }
        }
      });
    });

    setListSets(getListSet(resultData, selectionList));
    setSearchList(resultData);
  };

  useEffect(() => {
    setListSets(getListSet(searchList, selectionList));
  }, [selectionList, searchList]);

  const changeListSets = (v: Set<string>[]) => {
    setListSets(v);
  };

  return (
    <div className={styles.leftPart}>
      <Search allowClear placeholder={fm('Operation.operateSearch')} onSearch={handleSearch} />
      <div className={styles.actionContainer}>
        {!keyword ? (
          <TabModeList
            addOperationToList={addOperationToList}
            removeOperationFromList={removeOperationFromList}
            selectionList={selectionList}
          />
        ) : (
          <SearchList
            addOperationToList={addOperationToList}
            changeListSets={changeListSets}
            keyword={keyword}
            listSets={listSets}
            removeOperationFromList={removeOperationFromList}
            searchList={searchList}
          />
        )}
      </div>
    </div>
  );
};
