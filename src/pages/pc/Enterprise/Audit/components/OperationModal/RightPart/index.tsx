import classNames from 'classnames';
import type { FC } from 'react';

import type { SelectionList } from '@/model/audit';
import { fm } from '@/modules/Locale';
import { ReactComponent as CloseSVG } from '@/pages/pc/Enterprise/assets/audit/close-outline.svg';

import styles from './index.less';

export const MAX_LENGTH = 20;

interface Props {
  removeOperationFromList: (id: string) => void;
  clearAll: () => void;
  selectionList: SelectionList;
}

export const RightPart: FC<Props> = ({ removeOperationFromList, clearAll, selectionList }) => {
  const deleteOne = (id: string) => {
    removeOperationFromList(id);
  };

  const deleteAll = () => {
    clearAll();
  };

  return (
    <div className={styles.rightPart}>
      <div
        className={classNames({
          [styles.error]: selectionList.length > MAX_LENGTH,
          [styles.selectedNotice]: true,
        })}
      >
        {fm('Operation.selectNapeNum', { num: selectionList.length })},{fm('Operation.selectNapeNumMax', { num: 20 })}
      </div>
      <div className={styles.selectedListContainer}>
        <div className={styles.clearTextContainer}>
          <span className={styles.clearText} onClick={deleteAll}>
            {fm('Operation.operateClear')}
          </span>
        </div>
        <div className={styles.listContainer}>
          {selectionList.map((item) => {
            return (
              <div key={item.id} className={styles.selectedItem}>
                <div className={styles.itemName}>{item.name}</div>
                <div
                  className={styles.closeContainer}
                  onClick={() => {
                    deleteOne(item.id);
                  }}
                >
                  <CloseSVG />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};
