import { createStore, useStore } from 'zustand';

import type { AuditEvents, Description } from '@/model/audit';

interface AuditState {
  description: Description;
  auditEvents: AuditEvents;
}

interface AuditAction {
  setDescription: (description: Description) => void;
  setAudiEvents: (auditEvents: AuditEvents) => void;
}

type AuditStore = AuditState & AuditAction;

export const auditStore = createStore<AuditStore>((set) => ({
  description: {},
  setDescription: (description) => set(() => ({ description })),
  auditEvents: [{ id: '', children: [] }],
  setAudiEvents: (auditEvents) => set(() => ({ auditEvents })),
}));

export const useAuditStore = <U>(selector: (state: AuditStore) => U) => {
  return useStore(auditStore, selector);
};
