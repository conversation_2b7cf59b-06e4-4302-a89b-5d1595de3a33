import type { TableColumnsType } from 'antd';
import { Button, message, Table, Typography } from 'antd';
import { debounce } from 'lodash';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { defaultFilters, getClassification, getExportUrl, loadMore, searchLogs } from '@/api/Audit';
import { ModuleHeader } from '@/components/ManagementDesktop/ModuleHeader';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import type { Filters, LogRecord, SelectionItem } from '@/model/audit';
import { fm } from '@/modules/Locale';
import tableLoading from '@/pages/pc/Enterprise/assets/audit/loading.gif';
import { ReactComponent as OutputSVG } from '@/pages/pc/Enterprise/assets/audit/output.svg';
import tableEmpty from '@/pages/pc/Enterprise/assets/audit/table_empty.svg';
import { downloadFile, useFormatTime } from '@/utils/file';

import { InfoModal } from './components/InfoModal';
import { OperationModal } from './components/OperationModal';
import { TableOperation } from './components/TableOperation';
import styles from './index.less';
import { useAuditStore } from './store';

const OperationLog = () => {
  const i18nText_tabel = {
    createdAt: fm('Operation.createdAt'),
    operator: fm('Operation.operator'),
    action: fm('Operation.action'),
    operand: fm('Operation.operand'),
    relate: fm('Operation.relate'),
    ip: fm('Operation.ip'),
    more: fm('Operation.more'),
    terminal: fm('Operation.terminal'),
    addFilter: fm('Operation.addFilter'),
    viewDetail: fm('Operation.viewDetail'),
    noOperationData: fm('Operation.noOperationData'),
    exportLoading: fm('Operation.exportLoading'),
    exportSuccess: fm('Operation.exportSuccess'),
    exportError: fm('Operation.exportError'),
    deleted: fm('Operation.deleted'),
  };

  const isLoadingRef = useRef(false);
  const [lastUrl, setLastUrl] = useState('');
  const [selectionList, setSelectionList] = useState<SelectionItem[]>([]);
  const [operationVisible, setOperationVisible] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const { tableMaxHeight, tableClassName, debouncedCalculate } = useTableMaxHeight({});
  const [currentRecord, setCurrentRecord] = useState<LogRecord | null>(null);
  const [infoVisible, setInfoVisible] = useState(false);
  const [logList, setLogList] = useState<LogRecord[]>([]);
  const [exportParams, setExportParams] = useState<Filters>(defaultFilters);

  const { formatTime } = useFormatTime();
  const [messageApi, contextHolder] = message.useMessage();

  const { description, setDescription, setAudiEvents } = useAuditStore((state) => state);

  const getClassificationFetch = useCallback(async () => {
    try {
      const res = await getClassification();
      const { description = {}, auditEvents = [] } = res.data;
      setDescription(description);
      setAudiEvents(auditEvents);
    } catch {}
  }, [setAudiEvents, setDescription]);

  const reload = (filters?: Filters) => {
    setLoading(true);
    setExportParams(filters || defaultFilters);
    searchLogs(filters || defaultFilters)
      .then((res) => {
        if (res.status === 200) {
          const { data, nextUrl } = res?.data;
          if (Array.isArray(data) && data.length) {
            setLogList(data);
          } else {
            setLogList([]);
          }
          setLastUrl(nextUrl);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const columns: TableColumnsType = [
    {
      title: i18nText_tabel.createdAt,
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 160,
      render: (text) => {
        return (
          <Typography.Text ellipsis={{ tooltip: { title: formatTime(text), placement: 'topLeft' } }}>
            {formatTime(text * 1000)}
          </Typography.Text>
        );
      },
    },
    {
      title: i18nText_tabel.operator,
      dataIndex: 'operator',
      key: 'operator',
      width: 160,
      render: (operator, { operatorAvatar }) => {
        const regEx = /\](.*)</;
        const result = regEx.exec(operator);
        let operatorString = operator;
        if (result) {
          operatorString = result[1];
        }

        return (
          <div className={styles.styledOperatorContainer}>
            {operatorAvatar && <img alt="" className={styles.operationAvatar} src={operatorAvatar} />}
            <Typography.Text ellipsis={{ tooltip: { title: operatorString, placement: 'topLeft' } }}>
              {operatorString}
            </Typography.Text>
          </div>
        );
      },
    },
    {
      title: i18nText_tabel.action,
      dataIndex: 'action',
      key: 'action',
      render: (text) => (
        <Typography.Text ellipsis={{ tooltip: { title: description?.[text], placement: 'topLeft' } }}>
          {description?.[text]}
        </Typography.Text>
      ),
    },
    {
      title: i18nText_tabel.operand,
      dataIndex: 'operand',
      key: 'operand',
      render: (text, { notes, targetFile }) => {
        let txt;
        if (targetFile?.url && targetFile.name) {
          txt = targetFile.name;
          return (
            <Typography.Link
              underline
              className={styles.fileLink}
              href={targetFile.url}
              rel="noreferrer"
              target="_blank"
            >
              <Typography.Text className={styles.fileLink} ellipsis={{ tooltip: { title: txt, placement: 'topLeft' } }}>
                {txt}
              </Typography.Text>
            </Typography.Link>
          );
        }
        txt = (
          <>
            {targetFile?.name ? `[${i18nText_tabel.deleted}]` : null}
            {targetFile?.name ?? notes}
          </>
        );
        return <Typography.Text ellipsis={{ tooltip: { title: txt, placement: 'topLeft' } }}>{txt}</Typography.Text>;
      },
    },
    {
      title: i18nText_tabel.relate,
      dataIndex: 'relate',
      key: 'relate',
      render: (text, record) => {
        return (
          <Typography.Text ellipsis={{ tooltip: { title: record?.operator, placement: 'topLeft' } }}>
            {record?.operator}
          </Typography.Text>
        );
      },
    },
    {
      title: i18nText_tabel.ip,
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      render: (text) => {
        return <Typography.Text ellipsis={{ tooltip: { title: text, placement: 'topLeft' } }}>{text}</Typography.Text>;
      },
    },
    {
      title: i18nText_tabel.more,
      key: 'more',
      width: 120,
      render: (_, record) => {
        return (
          <Typography.Link
            className={styles.detailLink}
            onClick={() => {
              setCurrentRecord(record as LogRecord);
              setInfoVisible(true);
            }}
          >
            {i18nText_tabel.viewDetail}
          </Typography.Link>
        );
      },
    },
  ];

  const setMessage = useCallback(
    (type: 'success' | 'error' | 'loading', msg: string) => {
      messageApi.open({
        type,
        content: msg,
        duration: type === 'loading' ? undefined : 2,
      });
    },
    [messageApi],
  );

  const handleExport = useCallback(() => {
    setMessage('loading', i18nText_tabel.exportLoading);
    getExportUrl({
      ...exportParams,
    })
      .then((res) => {
        const {
          request: { responseURL },
        } = res;
        downloadFile(responseURL);
        setMessage('success', i18nText_tabel.exportSuccess);
      })
      .catch(() => {
        setMessage('error', i18nText_tabel.exportError);
      });
  }, [
    exportParams,
    i18nText_tabel.exportError,
    i18nText_tabel.exportLoading,
    i18nText_tabel.exportSuccess,
    setMessage,
  ]);

  const exportBtn = useMemo(
    () => (
      <Button icon={<OutputSVG />} onClick={debounce(handleExport, 500)}>
        {fm('Operation.export')}
      </Button>
    ),
    [handleExport],
  );

  useEffect(() => {
    getClassificationFetch();
    reload();
  }, [getClassificationFetch]);

  // 处理表格滚动
  const handleTableScroll = (events: React.UIEvent<HTMLDivElement>) => {
    const { scrollTop, scrollHeight, clientHeight } = events.target as HTMLDivElement;
    const threshold = 20;

    // 避免重复加载
    if (isLoadingRef.current) return;

    // 计算是否接近底部
    const isNearBottom = scrollHeight - scrollTop <= clientHeight + threshold;
    if (isNearBottom) {
      if (lastUrl) {
        isLoadingRef.current = true;
        // 保证不会重复加载数据
        // 如果有更多
        loadMore(lastUrl)
          .then((res) => {
            const { data, nextUrl } = res.data || {};
            if (Array.isArray(data) && data.length) {
              setLogList((pre) => [...pre, ...data]);
            }
            if (lastUrl === nextUrl || !nextUrl) {
              setLastUrl('');
            } else {
              setLastUrl(nextUrl);
            }
          })
          .finally(() => {
            isLoadingRef.current = false;
          });
      }
    }
  };

  const changeOperationVisible = (v: boolean) => {
    setOperationVisible(v);
  };

  return (
    <div style={{ padding: '0 16px' }}>
      {contextHolder}
      <ModuleHeader>{exportBtn}</ModuleHeader>
      <TableOperation
        changeOperationHeight={debouncedCalculate}
        changeOperationVisible={changeOperationVisible}
        selectionList={selectionList}
        setSelectionList={setSelectionList}
        onSearch={reload}
      />
      <div className={styles.tableContainer}>
        <Table
          virtual
          className={tableClassName}
          columns={columns}
          dataSource={logList}
          loading={{
            spinning: loading,
            indicator: <img src={tableLoading} />,
          }}
          locale={{
            emptyText: (
              <div className={styles.emptyHolder} style={{ height: tableMaxHeight - 34 }}>
                <img src={tableEmpty} />
                <div className={styles.holderText}>{i18nText_tabel.noOperationData}</div>
              </div>
            ),
          }}
          pagination={false}
          scroll={{ y: tableMaxHeight }}
          onScroll={handleTableScroll}
        />
      </div>
      <OperationModal
        hideModal={() => {
          changeOperationVisible(false);
        }}
        outerSelectionList={selectionList}
        setSelectOperations={setSelectionList}
        visible={operationVisible}
      />
      <InfoModal
        fm={i18nText_tabel}
        item={currentRecord}
        visible={infoVisible}
        onCancel={() => {
          setInfoVisible(false);
          setCurrentRecord(null);
        }}
      />
    </div>
  );
};

export default OperationLog;
