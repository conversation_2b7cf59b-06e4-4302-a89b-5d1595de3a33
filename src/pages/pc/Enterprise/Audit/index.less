.tableContainer {
  border-radius: 8px;
  border: 1px solid var(--theme-separator-color-lighter);
  margin-top: 16px;
  overflow: hidden;
  background: var(--theme-layout-color-bg-white);

  .ant-spin-nested-loading > div > .ant-spin {
    max-height: 100%;
  }

  .ant-table-wrapper table > .ant-table-cell {
    height: 44px;
    padding: 0 16px;
  }

  .ant-table-thead > tr > .ant-table-cell {
    height: 50px;
    font-size: 14px;
  }

  .ant-table-body {
    background: var(--theme-layout-color-bg-white);

    .ant-table-placeholder,
    .ant-table-row {
      background: transparent;
      line-height: 20px;

      .ant-table-cell {
        background-color: var(--theme-layout-color-bg-white);

        &:hover {
          background-color: var(--theme-layout-color-bg-white);
        }
      }
    }
  }

  .ant-table-tbody .ant-table-row {
    background-color: var(--theme-layout-color-bg-white);
  }

  .emptyHolder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    img {
      width: 123px;
      height: 91px;
      margin-bottom: 40px;
    }

    .holderText {
      color: var(--theme-text-color-secondary);
      font-size: 13px;
      line-height: 20px;
      margin-bottom: 12px;
    }
  }
}

.fileLink {
  text-decoration: underline !important;
  color: var(--theme-text-color-default) !important;
  text-underline-offset: 3px;

  &:hover {
    color: var(--theme-text-color-guidance) !important;
  }
}

.styledOperatorContainer {
  display: flex;
  align-items: center;
}

.operationAvatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-right: 8px;
  flex-shrink: 0;
}

.targetFileName {
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.detailLink {
  color: var(--theme-text-color-guidance) !important;
}

.operationTitle {
  margin-left: 16px;
}

.operationSubTitle {
  font-size: 14px;
  color: var(--theme-text-color-secondary);
  font-weight: 400;
  margin-left: 12px;
}

.exportBtn {
  margin-right: 24px;
}
