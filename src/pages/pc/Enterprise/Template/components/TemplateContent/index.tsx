import { Button, Empty, Form, message, Modal, Spin, Table } from 'antd';
import classNames from 'classnames';
import { useCallback, useEffect, useState } from 'react';

import { deleteTemplate, loadTemplateTypeData } from '@/api/Template';
import { ReactComponent as EmptySvg } from '@/assets/images/enterprise/template/empty.svg';
import { FileCreateTypeKey, FileCreateValueKey, FilePathTypeKey, TableActionKey } from '@/model/Common';
import type { OptionType, TemplateDataItem, TemplateTypeSelectProps } from '@/model/Template';
import { fm2 } from '@/modules/Locale';
import { getFileIcon } from '@/pages/pc/Enterprise/utils';

import { AddTemplatePop } from '../AddTemplatePop';
import { TemplateTypeSelect } from '../TemplateTypeSelect';
import { columns } from './columns';
import styles from './index.less';

const siderMenuCreateDocText = fm2('SiderMenu.siderMenuCreateDocText');
const siderMenuCreateMoDocText = fm2('SiderMenu.siderMenuCreateMoDocText');
const siderMenuCreateTableText = fm2('SiderMenu.siderMenuCreateTableText');
const siderMenuCreateMoTableText = fm2('SiderMenu.siderMenuCreateMoTableText');
const siderMenuCreatePptText = fm2('SiderMenu.siderMenuCreatePptText');
const siderMenuCreateFormText = fm2('SiderMenu.siderMenuCreateFormText');
export const options: OptionType[] = [
  {
    typeValue: FileCreateTypeKey.doc,
    label: siderMenuCreateDocText,
    icon: null,
    value: FileCreateValueKey.doc,
    fileValue: FilePathTypeKey.doc,
  },
  {
    typeValue: FileCreateTypeKey.docx,
    label: siderMenuCreateMoDocText,
    icon: null,
    value: FileCreateValueKey.docx,
    fileValue: FilePathTypeKey.docx,
  },
  {
    typeValue: FileCreateTypeKey.sheet,
    label: siderMenuCreateTableText,
    icon: null,
    value: FileCreateValueKey.sheet,
    fileValue: FilePathTypeKey.sheet,
  },
  {
    typeValue: FileCreateTypeKey.table,
    label: siderMenuCreateMoTableText,
    icon: null,
    value: FileCreateValueKey.table,
    fileValue: FilePathTypeKey.table,
  },
  {
    typeValue: FileCreateTypeKey.presentation,
    label: siderMenuCreatePptText,
    icon: null,
    value: FileCreateValueKey.presentation,
    fileValue: FilePathTypeKey.presentation,
  },
  {
    typeValue: FileCreateTypeKey.form,
    label: siderMenuCreateFormText,
    icon: null,
    value: FileCreateValueKey.form,
    fileValue: FilePathTypeKey.form,
  },
];
export function TemplateContent() {
  const optionRender = (option: { data: OptionType }) => (
    <div className={classNames('selectIcon', styles.selectIcon)}>
      <span>{getFileIcon(2, option.data.value)}</span>
      <span>{option.data.label}</span>
    </div>
  );

  const [dataList, setDataList] = useState<TemplateDataItem[]>([]);
  const [selectType, setSelectType] = useState<number>();
  const [isLoading, setIsLoading] = useState(true);
  // 加载数据的函数
  const fetchData = useCallback(
    async (value?: number) => {
      try {
        setIsLoading(true);
        const {
          data: { publicTemplates },
        } = await loadTemplateTypeData({ type: value, page: 1, pageSize: 20 });
        setDataList(publicTemplates || []);
      } finally {
        setIsLoading(false);
      }
    },
    [setIsLoading, setDataList],
  );

  const typeChange = useCallback(
    (item?: OptionType) => {
      setSelectType(item?.value);
      fetchData(item?.value);
    },
    [setSelectType, fetchData],
  );

  const typeSelectProp: TemplateTypeSelectProps = {
    options,
    optionRender,
    typeChange,
  };

  const handleCreate = useCallback(() => {
    AddTemplatePop({
      type: TableActionKey.add,
      typeSelectProp: { ...typeSelectProp, disabled: true },
      onOk: () => {
        fetchData(selectType);
      },
    });
  }, [typeSelectProp, selectType, fetchData]);

  useEffect(() => {
    fetchData();
  }, []);

  const handleEdit = useCallback(
    (record: TemplateDataItem) => {
      AddTemplatePop({
        type: TableActionKey.edit,
        formValues: record,
        typeSelectProp: { ...typeSelectProp, disabled: true, defaultValue: record.type },
        onOk: () => {
          fetchData(selectType);
        },
      });
    },
    [typeSelectProp, selectType, fetchData],
  );

  const handleDelete = useCallback(
    async (record: TemplateDataItem) => {
      Modal.confirm({
        title: fm2('TemplateContent.deleteTemplate'),
        width: 400,
        content: fm2('TemplateContent.deleteTemplateContent'),
        className: 'delete-confirm-modal',
        icon: null,
        centered: true,
        closable: false,
        maskClosable: true,
        onOk: async () => {
          try {
            await deleteTemplate(record?.id);
            message.success(fm2('TemplateContent.deleteSuccess'));
            fetchData(selectType);
          } catch (error) {
            message.error(fm2('TemplateContent.deleteFail'));
          }
        },
      });
    },
    [selectType, fetchData],
  );

  const tableColumns = columns(handleEdit, handleDelete);

  return (
    <div className={styles.content}>
      <div className={styles.filterTop}>
        <Form>
          <TemplateTypeSelect {...typeSelectProp} />
        </Form>
        <Button type="primary" onClick={() => handleCreate()}>
          {fm2('TemplateContent.create')}
        </Button>
      </div>
      {isLoading ? (
        <Spin fullscreen />
      ) : (
        <div className={styles.templateList}>
          {dataList.length ? (
            <Table
              virtual
              columns={tableColumns}
              dataSource={dataList}
              pagination={false}
              rowKey="id"
              scroll={{ y: 412 }}
            />
          ) : (
            <Empty
              className={styles.emptyContent}
              description={
                <>
                  <div className={styles.emptyText}>
                    {fm2('TemplateContent.createTip')}
                    <Button className={styles.createBtn} type="link" onClick={() => handleCreate()}>
                      {fm2('TemplateContent.createRightNow')}
                    </Button>
                  </div>
                  <div className={styles.desc}>{fm2('TemplateContent.youCanSeeTemplate')}</div>
                </>
              }
              image={<EmptySvg />}
              styles={{ image: { height: 260 } }}
            />
          )}
        </div>
      )}
    </div>
  );
}
