import { Button, Image, Typography } from 'antd';
import dayjs from 'dayjs';

import type { TemplateDataItem } from '@/model/Template';
import { fm2 } from '@/modules/Locale';

import { options } from './index';
import styles from './index.less';
export const columns = (onEdit: (record: TemplateDataItem) => void, onDelete: (record: TemplateDataItem) => void) => [
  {
    title: 'ID',
    dataIndex: 'id',
    key: 'id',
  },
  {
    title: fm2('AddTemplatePop.templateName'),
    dataIndex: 'name',
    key: 'name',
    render: (text: string) => (
      <Typography.Text ellipsis={{ tooltip: text }} style={{ maxWidth: 150 }}>
        {text}
      </Typography.Text>
    ),
  },
  {
    title: fm2('AddTemplatePop.templateType'),
    key: 'type',
    render: (record: TemplateDataItem) => options.find((item) => item.value === record.type)?.label,
  },
  {
    title: fm2('AddTemplatePop.img'),
    key: 'img',
    render: (record: TemplateDataItem) => <Image height={75} src={record.img} width={75} />,
  },
  {
    title: fm2('AddTemplatePop.updateTime'),
    dataIndex: 'utime',
    key: 'utime',
    render: (value: number) => {
      return dayjs(value * 1000).format('YYYY.MM.DD HH:mm');
    },
  },
  {
    title: fm2('AddTemplatePop.action'),
    key: 'action',
    render: (record: TemplateDataItem) => (
      <div className={styles.action}>
        <Button type="link" onClick={() => onEdit(record)}>
          {fm2('AddTemplatePop.edit')}
        </Button>
        <Button danger type="link" onClick={() => onDelete(record)}>
          {fm2('AddTemplatePop.delete')}
        </Button>
      </div>
    ),
  },
];
