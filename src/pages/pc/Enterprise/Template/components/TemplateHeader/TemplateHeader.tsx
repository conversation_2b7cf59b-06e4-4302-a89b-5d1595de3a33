import { history } from 'umi';

import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import styles from './TemplateHeader.less';

export const TemplateHeader = () => {
  const i18nText = {
    enterpriseSetting: fm('TemplateHeader.enterpriseSetting'),
    enterpriseTemplateSetting: fm('TemplateHeader.enterpriseTemplateSetting'),
    enterpriseId: fm('TemplateHeader.enterpriseId'),
  };
  const me = useMeStore((state) => state.me);
  const goToSetting = () => {
    history.push('/settings');
  };

  return (
    <div className={styles.styledTrashHeader}>
      <div className={styles.styledCrumbs}>
        <span onClick={goToSetting}>{i18nText.enterpriseSetting}</span>
      </div>
      <div className={styles.styledTrashTitle}>
        <div className={styles.styledTrashTitleLeft}>
          <div className={styles.styledPageName}>{i18nText.enterpriseTemplateSetting}</div>
          <div className={styles.styledEnterpriseName}>
            {me?.team?.name} / {i18nText.enterpriseId} {me?.teamId}
          </div>
        </div>
      </div>
    </div>
  );
};
