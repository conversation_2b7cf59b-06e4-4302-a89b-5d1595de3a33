import { Form, Select } from 'antd';

import { FromTypeKey } from '@/model/Common';
import type { TemplateTypeSelectProps } from '@/model/Template';
import { fm2 } from '@/modules/Locale';

export function TemplateTypeSelect({
  defaultValue,
  options,
  optionRender,
  typeChange,
  required = false,
  disabled = false,
  from = FromTypeKey.filter,
}: TemplateTypeSelectProps) {
  const setType = (value: number) => {
    const item = options.find((item: { value: number }) => item.value === value);
    typeChange(item);
  };
  return (
    <Form.Item
      colon={false}
      label={fm2('AddTemplatePop.templateType')}
      name="type"
      rules={[{ required, message: fm2('AddTemplatePop.selectType') }]}
    >
      <Select
        allowClear
        defaultValue={defaultValue}
        disabled={disabled}
        getPopupContainer={(trigger) => trigger.parentNode}
        optionRender={(option) => optionRender(option)}
        options={options}
        placeholder={
          from === FromTypeKey.form ? fm2('AddTemplatePop.autoSelectType') : fm2('AddTemplatePop.selectType')
        }
        style={{ width: 290 }}
        onChange={(value) => setType(value)}
      />
    </Form.Item>
  );
}
