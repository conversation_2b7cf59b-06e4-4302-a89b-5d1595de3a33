import type { UploadFile, UploadProps } from 'antd';
import { Button, Form, Image as AntImage, Input, message, Modal, Upload } from 'antd';
import type { RcFile } from 'antd/es/upload';
import { memo, useCallback, useEffect, useState } from 'react';

import { uploadAvatarToken } from '@/api/profile';
import { createTemplate, updateTemplate } from '@/api/Template';
import { ReactComponent as UploadSvg } from '@/assets/images/enterprise/template/uplaod.svg';
import { FromTypeKey, TableActionKey } from '@/model/Common';
import type { FormModalProps, TemplateDataItem } from '@/model/Template';
import { fm2 } from '@/modules/Locale';
import { isURL } from '@/pages/pc/Enterprise/utils';
import { compressImage } from '@/utils';

import { TemplateTypeSelect } from '../TemplateTypeSelect';
import styles from './index.less';

const i18nText: any = {
  createSuccess: fm2('AddTemplatePop.createSuccess'),
  createFailed: fm2('AddTemplatePop.createFailed'),
  editSuccess: fm2('AddTemplatePop.editSuccess'),
  editFailed: fm2('AddTemplatePop.editFailed'),
  noFileType: fm2('AddTemplatePop.noFileType'),
  inputSureLink: fm2('AddTemplatePop.inputSureLink'),
  uploadFailed: fm2('AddTemplatePop.uploadFailed'),
  tokenFailed: fm2('AddTemplatePop.tokenFailed'),
  limitJPG: fm2('AddTemplatePop.limitJPG'),
  noUp2MB: fm2('AddTemplatePop.noUp2MB'),
  inputTemplateName: fm2('AddTemplatePop.inputTemplateName'),
  selectType: fm2('AddTemplatePop.selectType'),
  autoSelectType: fm2('AddTemplatePop.autoSelectType'),
  inputLink: fm2('AddTemplatePop.inputLink'),
  uploadImgPlace: fm2('AddTemplatePop.uploadImgPlace'),
  templateName: fm2('AddTemplatePop.templateName'),
  templateLink: fm2('AddTemplatePop.templateLink'),
  templateImg: fm2('AddTemplatePop.templateImg'),
  uploadImg: fm2('AddTemplatePop.uploadImg'),
  uploadImgRetry: fm2('AddTemplatePop.uploadImgRetry'),
  createTemplate: fm2('AddTemplatePop.createTemplate'),
  editTemplate: fm2('AddTemplatePop.editTemplate'),
};
// 主组件，不再导出空组件
const ModalContent = memo(({ type = TableActionKey.add, typeSelectProp, ...props }: FormModalProps) => {
  const { onOk, formValues } = props;
  const { options } = typeSelectProp;
  const [form] = Form.useForm();
  const newTypeProp = {
    from: FromTypeKey.form,
    ...typeSelectProp,
    typeChange: () => {
      //拦截状态变化列表刷新数据的方法 不可删除
    },
  };

  // 图片上传状态管理
  const [uploading, setUploading] = useState(false);
  const [uploadFile, setUploadFile] = useState<UploadFile | null>(null);

  useEffect(() => {
    if (formValues) {
      // 更新上传状态
      setUploadFile({
        uid: `-${Date.now()}`,
        name: formValues.name || '',
        status: 'done',
        url: formValues.img,
      });
    }
  }, [formValues]);

  const handleOk = useCallback(async () => {
    await form.validateFields();
    const values = form.getFieldsValue();
    try {
      if (type === TableActionKey.add) {
        await createTemplate(values);
      } else {
        // 只发送已更改的字段
        const updatedFields: any = {};

        for (const key in values) {
          if (values[key] !== formValues?.[key]) {
            updatedFields[key] = values[key];
          }
        }
        await updateTemplate({ ...updatedFields, id: formValues?.id });
      }
      message.success(type === TableActionKey.add ? i18nText.createSuccess : i18nText.editSuccess);
      onOk?.(form.getFieldsValue());
      props.onClose?.();
    } catch (error) {
      message.error(type === TableActionKey.add ? i18nText.createFailed : i18nText.editFailed);
    }
  }, [form, createTemplate, updateTemplate]);

  const inputHandleChange = useCallback(
    async (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = e.target.value;
      if (value) {
        if (isURL(value)) {
          if (value.includes('/') && value.split('/').length > 2) {
            const guid = value.split('/')[value.split('/').length - 1];
            //获取文件类型
            const pathFiletype = value.split('/')[value.split('/').length - 2];
            const item = options.find((item) => item.fileValue === pathFiletype);
            if (item) {
              form.setFieldValue('guid', guid);
              form.setFieldValue('type', item.value);
            } else {
              form.setFieldValue('guid', '');
              form.setFieldValue('type', null);
              message.error(i18nText.noFileType);
            }
          } else {
            form.setFieldValue('guid', '');
            form.setFieldValue('type', null);
            message.error(i18nText.inputSureLink);
          }
        } else if (form.getFieldValue('type')) {
          form.setFieldValue('guid', value);
        }
      }
    },
    [form, options],
  );

  // 监听上传文件状态变化，更新表单值
  useEffect(() => {
    if (uploadFile && uploadFile.status === 'done' && uploadFile.url) {
      form.setFieldValue('img', uploadFile.url);
    }
  }, [uploadFile, form]);

  // 自定义上传请求
  const customRequest = useCallback(
    (options: { file: any }) => {
      const { file } = options;
      const payload = [
        {
          bucket: 'avatar', //固定
          filename: file.name,
          fileSize: file.size,
        },
      ];
      uploadAvatarToken(payload)
        .then((resToken) => {
          const res = resToken.data.datas[0];
          const formData = new FormData();
          formData.append('accessToken', res.formData.accessToken);
          formData.append('download', res.formData.download);
          formData.append('file', file);
          // 向这个地址https://drv-dev.shimorelease.com/uploader/upload上传图片(接口返回数据)
          fetch(resToken.data.url, {
            method: 'POST',
            body: formData,
          })
            .then((response) => {
              if (!response.ok) {
                message.error(fm2('Request.netError'));
              }
              return response.json();
            })
            .then((data) => {
              if (data.code !== 0) {
                message.error(i18nText.uploadFailed);
                return;
              }
              const imagesUrl = data.data?.images;
              // 更新上传状态
              setUploadFile({
                ...file,
                status: 'done',
                url: imagesUrl,
              });
              setUploading(false);
              // 更新表单值并触发表单校验
              form.setFieldsValue({ img: imagesUrl });
              form.validateFields(['img']);
            })
            .catch(() => {
              message.error(i18nText.uploadFailed);
              form.setFieldsValue({ img: '' });
              form.validateFields(['img']);
              setUploading(false);
            });
        })
        .catch(() => {
          form.setFieldsValue({ img: '' });
          form.validateFields(['img']);

          message.error(i18nText.tokenFailed);
          setUploading(false);
        });
    },
    [form, setUploadFile, setUploading],
  );
  // 处理上传前的文件校验
  const beforeUpload = useCallback(
    async (file: File) => {
      const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
      if (!isJpgOrPng) {
        message.error(i18nText.limitJPG);
        return false;
      }

      const isLt2M = file.size / 1024 / 1024 < 2;
      if (!isLt2M) {
        message.error(i18nText.noUp2MB);
        return false;
      }

      // 更新上传文件状态
      setUploadFile({
        uid: `-${Date.now()}`,
        name: file.name,
        status: 'uploading',
        originFileObj: file as RcFile,
      });

      setUploading(true);
      try {
        // 压缩图片
        const compressedFile = await compressImage(file);

        // 如果压缩后的文件小于原始文件大小，则使用压缩后的文件
        if (compressedFile.size < file.size) {
          // 使用压缩后的文件进行上传
          customRequest({
            file: compressedFile as RcFile,
          });

          return false; // 阻止默认上传
        } else {
          customRequest({
            file,
          });
          return false;
        }
      } catch (error) {
        customRequest({
          file,
        });
        return false;
      }
    },
    [setUploadFile, setUploading, compressImage, customRequest],
  );

  const uploadProps: UploadProps = {
    accept: 'image/*',
    customRequest: customRequest,
    beforeUpload: beforeUpload,
    maxCount: 1,
    className: 'upload-list-inline',
    showUploadList: false,
  };

  const rules = {
    name: [
      {
        required: true,
        message: i18nText.inputTemplateName,
        placeholder: i18nText.inputTemplateName,
      },
    ],
    type: [
      {
        required: true,
        message: i18nText.selectType,
        placeholder: i18nText.autoSelectType,
      },
    ],
    guid: [
      {
        required: true,
        message: i18nText.inputLink,
        placeholder: i18nText.inputLink,
      },
    ],
    img: [
      {
        required: true,
        message: i18nText.uploadImgPlace,
      },
    ],
  };
  return (
    <div>
      <Form
        colon={false}
        form={form}
        initialValues={formValues}
        labelCol={{ span: 7 }}
        layout="horizontal"
        preserve={false}
        wrapperCol={{ span: 17 }}
      >
        <Form.Item label={i18nText.templateName} name="name" rules={rules.name}>
          <Input maxLength={10} placeholder={rules.name[0].placeholder} />
        </Form.Item>
        <Form.Item label={i18nText.templateLink} name="guid" rules={rules.guid}>
          <Input allowClear placeholder={rules.guid[0].placeholder} onBlur={inputHandleChange} />
        </Form.Item>
        <TemplateTypeSelect {...newTypeProp} />
        <Form.Item className={styles.imagesUploadForm} label={i18nText.templateImg} name="img" rules={rules.img}>
          {uploadFile?.status === 'done' && <AntImage className={styles.imagesSet} src={uploadFile?.url} width={75} />}
          <Upload {...uploadProps} className={styles.imagesUpload}>
            <Button icon={<UploadSvg />} loading={uploading} type="link">
              {uploadFile?.status === 'done' ? i18nText.uploadImgRetry : i18nText.uploadImg}
            </Button>
          </Upload>
        </Form.Item>
      </Form>
      <div className={styles.footer}>
        <Button color="default" variant="solid" onClick={handleOk}>
          {fm2('FilePathPicker.confirm')}
        </Button>
        <Button onClick={props.onClose}>{fm2('FilePathPicker.cancel')}</Button>
      </div>
    </div>
  );
});

export function AddTemplatePop(props: FormModalProps) {
  const { type } = props;

  // 创建Modal实例
  let modalInstance: any = null;

  // 关闭弹窗的函数
  function closeModal() {
    if (modalInstance) {
      modalInstance.destroy();
    }
  }

  // 修改onOk回调，确保关闭Modal
  const enhancedProps = {
    ...props,
    onClose: closeModal,
    onOk: (formValues: TemplateDataItem) => {
      props.onOk?.(formValues);
      closeModal();
    },
  };

  const title = type === TableActionKey.add ? i18nText.createTemplate : i18nText.editTemplate;

  // 使用Modal.info创建弹窗
  modalInstance = Modal.confirm({
    title,
    width: 496,
    content: <ModalContent {...enhancedProps} />,
    className: 'addTemplatePop',
    icon: null,
    footer: null,
    centered: true,
    closable: true,
    maskClosable: true,
    onCancel: closeModal,
  });
}
