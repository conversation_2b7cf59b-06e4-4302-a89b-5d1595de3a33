import { message } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';

import { fm2 } from '@/modules/Locale';

export const copyHandle = (content: string, messageApi?: MessageInstance) => {
  const i18nText = {
    success: fm2('copyHandle.success'),
    error: fm2('copyHandle.error'),
    errorForCopy: fm2('copyHandle.errorForCopy'),
  };
  const messageInstance = messageApi ?? message;
  try {
    navigator.clipboard.writeText(content).then(
      () => {
        messageInstance.open({
          type: 'success',
          content: i18nText.success,
        });
      },
      () => {
        throw new Error(i18nText.error);
      },
    );
  } catch (error) {
    messageInstance.open({
      type: 'error',
      content: i18nText.errorForCopy,
    });
  }
};

/**
 * 复制简单文本不建议用
 *
 * 新复制方法 注意新方法可能需要事件触发 自动触发会有问题
 *
 * 注意这里按照规范来说 write 为高级命令 有诸多权限和协议问题
 *
 * 复制内容，兼容有HTML标签的文本
 *
 * @param content 为HTML字符串 也就是innerHTML的内容
 */
export function newCopy(content: string) {
  const textHtmlBlob = new Blob([content], { type: 'text/html' });
  const options = {
    'text/html': textHtmlBlob,
  };
  // eslint-disable-next-line
  const newBlobData = new window['ClipboardItem'](options);
  // eslint-disable-next-line
  navigator.clipboard['write']([newBlobData])
    .then(() => {
      console.log('Text copied to clipboard');
    })
    .catch(() => {
      // 如果出错使用老方法
      // oldCopy(content);
    });
}
