import { ReactComponent as AeSvg } from '@/assets/images/enterprise/fileIcon/ae.svg';
import { ReactComponent as AiSvg } from '@/assets/images/enterprise/fileIcon/ai.svg';
import { ReactComponent as BoardSvg } from '@/assets/images/enterprise/fileIcon/board.svg';
import { ReactComponent as CloudSvg } from '@/assets/images/enterprise/fileIcon/cloud.svg';
import { ReactComponent as DocSvg } from '@/assets/images/enterprise/fileIcon/doc.svg';
import { ReactComponent as FolderSvg } from '@/assets/images/enterprise/fileIcon/folder.svg';
import { ReactComponent as FormSvg } from '@/assets/images/enterprise/fileIcon/form.svg';
import { ReactComponent as MindSvg } from '@/assets/images/enterprise/fileIcon/mind.svg';
import { ReactComponent as MindmapSvg } from '@/assets/images/enterprise/fileIcon/mindmap.svg';
import { ReactComponent as ModocSvg } from '@/assets/images/enterprise/fileIcon/modoc.svg';
import { ReactComponent as MusicSvg } from '@/assets/images/enterprise/fileIcon/music.svg';
import { ReactComponent as PdfSvg } from '@/assets/images/enterprise/fileIcon/pdf.svg';
import { ReactComponent as PicSvg } from '@/assets/images/enterprise/fileIcon/pic.svg';
import { ReactComponent as PptxSvg } from '@/assets/images/enterprise/fileIcon/pptx.svg';
import { ReactComponent as PresentationSvg } from '@/assets/images/enterprise/fileIcon/presentation.svg';
import { ReactComponent as PsSvg } from '@/assets/images/enterprise/fileIcon/ps.svg';
import { ReactComponent as SheetSvg } from '@/assets/images/enterprise/fileIcon/sheet.svg';
import { ReactComponent as SketchSvg } from '@/assets/images/enterprise/fileIcon/sketch.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/enterprise/fileIcon/space.svg';
import { ReactComponent as TableSvg } from '@/assets/images/enterprise/fileIcon/table.svg';
import { ReactComponent as VideoSvg } from '@/assets/images/enterprise/fileIcon/video.svg';
import { ReactComponent as WordSvg } from '@/assets/images/enterprise/fileIcon/word.svg';
import { ReactComponent as WpsSvg } from '@/assets/images/enterprise/fileIcon/wps.svg';
import { ReactComponent as XlsSvg } from '@/assets/images/enterprise/fileIcon/xls.svg';
import { ReactComponent as ZipSvg } from '@/assets/images/enterprise/fileIcon/zip.svg';
import { FileTypeValue } from '@/model/Enterprise/Trash';
import { FileIcon } from '@/pages/pc/Enterprise/common/components/FileIcon';
import { FileIconType } from '@/pages/pc/Enterprise/common/components/FileIcon/type';
const getFileTypeIcon = (type: `${FileIconType}`) => {
  switch (type) {
    // 石墨文件
    case FileIconType.Folder:
      return <FolderSvg />;
    case FileIconType.Document:
      return <ModocSvg />;
    case FileIconType.Spreadsheet:
      return <SheetSvg />;
    case FileIconType.Doc:
      return <DocSvg />;
    case FileIconType.Sheet:
      return <SheetSvg />;
    case FileIconType.Mosheet:
      return <SheetSvg />;
    case FileIconType.Modoc:
      return <ModocSvg />;
    case FileIconType.Mindmap:
      return <MindmapSvg />;
    case FileIconType.Form:
      return <FormSvg />;
    case FileIconType.Slide:
      return <PresentationSvg />;
    case FileIconType.TableViewForm:
      return <FormSvg />;
    case FileIconType.QuizForm:
      return <FormSvg />;
    case FileIconType.Board:
      return <BoardSvg />;
    case FileIconType.Presentation:
      return <PresentationSvg />;
    case FileIconType.Table:
      return <TableSvg />;
    case FileIconType.Shortcut:
      return <CloudSvg />;

    // 云文件
    case FileIconType.Xmind:
      return <MindSvg />;
    case FileIconType.Wps:
      return <WpsSvg />;
    case FileIconType.Video:
      return <VideoSvg />;
    case FileIconType.Zip:
      return <ZipSvg />;
    case FileIconType.Ppt:
      return <PresentationSvg />;
    case FileIconType.Word:
      return <WordSvg />;
    case FileIconType.Excel:
      return <XlsSvg />;
    case FileIconType.Pdf:
      return <PdfSvg />;
    case FileIconType.Ae:
      return <AeSvg />;
    case FileIconType.Image:
      return <PicSvg />;
    case FileIconType.Ai:
      return <AiSvg />;
    case FileIconType.Psd:
      return <PsSvg />;
    case FileIconType.Sketch:
      return <SketchSvg />;

    // 其他格式
    case FileIconType.Cloud:
      return <CloudSvg />;
    case FileIconType.Space:
      return <SpaceSvg />;

    case FileIconType.Unknown:
    default:
      return <CloudSvg />;
  }
};
const getFileIconType = (
  type: number, // 1、文件夹空间 2、石墨文件 3、云文件
  subType: FileTypeValue,
  isSpace?: boolean,
): FileIconType => {
  if (type === 1) {
    switch (subType) {
      case FileTypeValue.Folder:
        if (isSpace) {
          return FileIconType.Space;
        }
        return FileIconType.Folder;
      case FileTypeValue.Space:
        return FileIconType.Space;
      default:
        return FileIconType.Cloud;
    }
  }
  if (type === 2) {
    switch (subType) {
      case FileTypeValue.Table:
        return FileIconType.Table;
      case FileTypeValue.Presentation:
        return FileIconType.Presentation;
      case FileTypeValue.Board:
        return FileIconType.Board;
      case FileTypeValue.Form:
        return FileIconType.Form;
      case FileTypeValue.Mindmap:
        return FileIconType.Mindmap;
      case FileTypeValue.Modoc:
        return FileIconType.Modoc;
      case FileTypeValue.Sheet:
        return FileIconType.Sheet;
      case FileTypeValue.Doc:
        return FileIconType.Doc;
      default:
        return FileIconType.Unknown;
    }
  }
  switch (subType) {
    case FileTypeValue.Img:
      return FileIconType.Image;
    case FileTypeValue.Pdf:
      return FileIconType.Pdf;

    // TODO: 需要添加更多的文件类型
    case FileTypeValue.Xls:
      return FileIconType.Excel;
    case FileTypeValue.Docx:
      return FileIconType.Word;
    case FileTypeValue.Ppt:
      return FileIconType.Ppt;
    case FileTypeValue.Mp3:
      return FileIconType.Audio;

    case FileTypeValue.Zip:
      return FileIconType.Zip;
    case FileTypeValue.Mp4:
      return FileIconType.Video;
    case FileTypeValue.Wps:
      return FileIconType.Wps;
    case FileTypeValue.Xmind:
      return FileIconType.Xmind;
    default:
      return FileIconType.Unknown;
  }
};

// 目前有两种判断方式，一种是根据type和isSpace结合判断。一种是使用subType判断………
export const getFileIcon = (
  type: number,
  subType: FileTypeValue,
  isSpace?: boolean,
  size?: number,
): React.ReactNode => {
  const iconType = getFileIconType(type, subType, isSpace);
  const iconSvg = getFileTypeIcon(iconType);

  return <FileIcon iconSvg={iconSvg} size={size ?? 20} />;
};
/**
 * 通过文件获取对应的 icon
 *
 * /lizard-api/teams/mine/outsiders/6037871/files
 * 该接口返回的 file 并没有 subType，所以只通过 type 判断
 */
export const getOuterFileIcon = (type: FileTypeValue) => {
  switch (type) {
    case FileTypeValue.Board:
      return <BoardSvg />;
    case FileTypeValue.Doc:
      return <DocSvg />;
    case FileTypeValue.Docx:
      return <WordSvg />;
    case FileTypeValue.Folder:
      return <SpaceSvg />;
    case FileTypeValue.Form:
      return <FormSvg />;
    case FileTypeValue.Img:
      return <PicSvg />;
    case FileTypeValue.Mindmap:
      return <MindmapSvg />;
    case FileTypeValue.Modoc:
      return <ModocSvg />;
    case FileTypeValue.Mp3:
      return <MusicSvg />;
    case FileTypeValue.Mp4:
      return <VideoSvg />;
    case FileTypeValue.Pdf:
      return <PdfSvg />;
    case FileTypeValue.Ppt:
      return <PptxSvg />;
    case FileTypeValue.Presentation:
      return <PresentationSvg />;
    case FileTypeValue.Sheet:
      return <SheetSvg />;
    case FileTypeValue.Space:
      return <SpaceSvg />;
    case FileTypeValue.Table:
      return <TableSvg />;
    case FileTypeValue.Wps:
      return <WpsSvg />;
    case FileTypeValue.Xls:
      return <XlsSvg />;
    case FileTypeValue.Xmind:
      return <MindSvg />;
    case FileTypeValue.Zip:
      return <ZipSvg />;

    default:
      return <CloudSvg />;
  }
};
