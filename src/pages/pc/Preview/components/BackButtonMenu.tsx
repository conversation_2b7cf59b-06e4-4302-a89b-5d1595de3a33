import { CaretDownOutlined } from '@ant-design/icons';
import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';

import { BackToPopover } from '@/components/BackToPopover';

import pageStyles from '../index.less';
import styles from './BackButtonMenu.less';

type OnItemClick = NonNullable<Parameters<typeof BackToPopover>[0]['onItemClick']>;
type RecentFilesTyp = NonNullable<Parameters<typeof BackToPopover>[0]['recentFiles']>;
interface BackButtonMenuProps {
  title: string;
  recentFiles: RecentFilesTyp;
  onClick: OnItemClick;
}

export default function BackButtonMenu({ title, recentFiles, onClick }: BackButtonMenuProps) {
  return (
    <Tooltip placement="bottom" title={title}>
      <Popover
        arrow={false}
        content={<BackToPopover recentFiles={recentFiles} onItemClick={onClick} />}
        placement="bottomLeft"
        trigger="click"
        zIndex={1000}
      >
        <div className={classNames(pageStyles.btn, styles.arrowDownBtn)}>
          <CaretDownOutlined />
        </div>
      </Popover>
    </Tooltip>
  );
}
