import { LeftOutlined } from '@ant-design/icons';
import { Tooltip } from 'antd';
import classNames from 'classnames';

import pageStyles from '../index.less';
import styles from './BackButton.less';

interface BackButtonProps {
  title: string;
  onClick: () => void;
}

export default function BackButton({ title, onClick }: BackButtonProps) {
  return (
    <Tooltip placement="bottom" title={title}>
      <div className={classNames(pageStyles.btn, styles.arrowRightBtn)} onClick={onClick}>
        <LeftOutlined />
      </div>
    </Tooltip>
  );
}
