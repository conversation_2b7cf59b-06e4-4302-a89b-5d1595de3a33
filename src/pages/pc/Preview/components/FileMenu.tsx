import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import { forwardRef, useImperativeHandle, useState } from 'react';

import { ReactComponent as MoreIcon } from '@/assets/images/svg/more.svg';
import { FileMenuPopover } from '@/components/FileMenuPopover';

import pageStyles from '../index.less';
import styles from './FileMenu.less';

type OnItemClick = NonNullable<Parameters<typeof FileMenuPopover>[0]['onItemClick']>;
interface FileMenuProps {
  title: string;
  isStar: boolean;
  fileType: string;
  onItemClick: OnItemClick;
}

export interface FileMenuRef {
  setVisible: (v: boolean) => void;
}

const FileMenu = forwardRef<FileMenuRef, FileMenuProps>(({ title, isStar, fileType, onItemClick }, ref) => {
  const [fileMenuVisible, setFileMenuVisible] = useState(false);

  useImperativeHandle(ref, () => ({
    setVisible: setFileMenuVisible,
  }));

  return (
    <Tooltip placement="bottom" title={title}>
      <Popover
        arrow={false}
        content={
          <FileMenuPopover
            fileType={fileType}
            isFavorite={isStar}
            onItemClick={onItemClick}
            onOpenInNewTab={() => {}}
          />
        }
        destroyTooltipOnHide={true}
        open={fileMenuVisible}
        placement="bottomLeft"
        trigger="click"
        zIndex={1000}
        onOpenChange={(status) => setFileMenuVisible(status)}
      >
        <div className={classNames(pageStyles.btn, styles.moreBtn)}>
          <MoreIcon height={14.5} width={14.5} />
        </div>
      </Popover>
    </Tooltip>
  );
});

export default FileMenu;
