import { PlusOutlined } from '@ant-design/icons';
import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';

import { AddNewPopover } from '@/components/AddNewPopover';

import pageStyles from '../index.less';
import styles from './CreateButton.less';

type OnItemClick = NonNullable<Parameters<typeof AddNewPopover>[0]['onItemClick']>;
type OnOpenInNewTab = NonNullable<Parameters<typeof AddNewPopover>[0]['onOpenInNewTab']>;
interface CreateButtonProps {
  title: string;
  onItemClick: OnItemClick;
  onOpenInNewTab: OnOpenInNewTab;
}

export default function CreateButton({ title, onItemClick, onOpenInNewTab }: CreateButtonProps) {
  return (
    <Tooltip placement="bottom" title={title}>
      <Popover
        arrow={false}
        content={<AddNewPopover onItemClick={onItemClick} onOpenInNewTab={onOpenInNewTab} />}
        placement="bottomLeft"
        trigger="click"
        zIndex={1000}
      >
        <div className={classNames(pageStyles.btn, styles.plusBtn)}>
          <PlusOutlined />
        </div>
      </Popover>
    </Tooltip>
  );
}
