.togglePreviewImg {
  @gap: 46px;
  @size: 76px;
  @offset: 50px; // 与header高度一致

  display: flex;
  align-items: center;
  justify-content: center;

  .btn {
    position: fixed;
    bottom: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    width: @size;
    height: @size;
    border-radius: 50%;
    background-color: var(--theme-text-color-deep);
    color: var(--theme-basic-color-bg-default);
    font-size: 14px;
    cursor: pointer;

    .icon {
      font-size: 30px;
    }

    &.disabled {
      cursor: not-allowed;
      background-color: var(--theme-mask-layer-color-light);
      color: var(--theme-text-color-arrow);
    }
  }

  .preBtn {
    left: @gap;
  }

  .nextBtn {
    right: @gap;
  }

  &.modal {
    position: absolute;
    width: 100%;
    height: 100%;
    left: 0;
    top: 0;
    background-color: var(--theme-basic-color-bg-default);
    z-index: 1000;
  }

  .emptyImg {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-top: -@offset;

    .txt {
      color: var(--theme-tooltip-color-bg);
      font-size: 28px;
      font-weight: 500;
    }
  }
}
