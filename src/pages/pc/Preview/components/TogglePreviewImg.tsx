import { LeftOutlined, RightOutlined } from '@ant-design/icons';
import classNames from 'classnames';

import img from '@/assets/images/svg/noData.svg';
import { fm } from '@/modules/Locale';

import styles from './TogglePreviewImg.less';

interface TogglePreviewImgProps {
  showEmptyPage: boolean;
  disableNextBtn: boolean;
  onClickPre: () => void;
  onClickNext: () => void;
}

export default ({ showEmptyPage, disableNextBtn, onClickPre, onClickNext }: TogglePreviewImgProps) => {
  const i18n_noData = fm('File.noData');

  return (
    <div
      className={classNames(styles.togglePreviewImg, {
        [styles.modal]: showEmptyPage,
      })}
    >
      {/* 前一张按钮 */}
      <div
        className={classNames(styles.btn, styles.preBtn, {
          [styles.disabled]: showEmptyPage,
        })}
        onClick={() => onClickPre()}
      >
        <LeftOutlined className={styles.icon} />
      </div>
      {/* 后一张按钮 */}
      <div
        className={classNames(styles.btn, styles.nextBtn, {
          [styles.disabled]: disableNextBtn,
        })}
        onClick={() => onClickNext()}
      >
        <RightOutlined className={styles.icon} />
      </div>

      {/* empty img */}
      {showEmptyPage && (
        <div className={styles.emptyImg}>
          <img src={img} />
          <span className={styles.txt}>{i18n_noData}</span>
        </div>
      )}
    </div>
  );
};
