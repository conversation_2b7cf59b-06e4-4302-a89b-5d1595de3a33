import { fm } from '@/modules/Locale';

export function useHeaderI18n() {
  return {
    // 单独变量（可用于 props）
    backButtonTipText: fm('Header.backButtonTipText'),
    loadingText: fm('Common.loadingText'),
    inputPlaceholder: fm('Header.inputPlaceholder'),
    unfavorited: fm('Header.unfavorited'),
    favorited: fm('Header.favorited'),
    backToButtonTipText: fm('Header.backToButtonTipText'),
    createButtonTipText: fm('Header.createButtonTipText'),
    shareButtonText: fm('Header.shareButtonText'),
    editButtonText: fm('Header.editButtonText'),
    downloadButtonText: fm('Header.downloadButtonText'),
    sharingCollaborationButtonText: fm('Header.sharingCollaborationButtonText'),
    fileMenuButtonTipText: fm('Header.fileMenuButtonTipText'),

    fileInfo: fm('FileMenuPopover.fileInfo'),

    // 分组文案对象（用于 confirm、message 组件等）
    i18nText: {
      delete: fm('File.delete'),
      title: fm('deleteConfirm.title'),
      content: fm('deleteConfirm.content'),
      okText: fm('deleteConfirm.title'),
      cancelText: fm('deleteConfirm.cancel'),
      action: fm('File.delete'),
      success: fm('deleteConfirm.success'),
      error: fm('deleteConfirm.error'),
      noSupport: fm('Editor.noSupport'),
    },

    messageI18n: {
      fileDownloadErr: fm('File.downloadErr'),
      fileStartDownload: fm('File.startDownload'),
      fileStartImport: fm('File.startImport'),
      fileIsLoading: fm('File.isLoading'),
      fileImportSuccess: fm('File.importSuccess'),
      fileImportErr: fm('File.importErr'),
      fileImportTypeErr: fm('File.importTypeErr'),
    },
  };
}
