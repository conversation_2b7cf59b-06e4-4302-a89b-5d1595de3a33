import { message } from 'antd';
import { useEffect, useState } from 'react';
import { history } from 'umi';

import * as fileApi from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { fm } from '@/modules/Locale';
import type { FileDetail } from '@/types/api';

export function useImagePreviewNavigator(fileDetail: FileDetail | null) {
  const [showEmptyPage, setShowEmptyPage] = useState(false);
  const [disableNextBtn, setDisableNextBtn] = useState(false);
  const i18n_lastImg = fm('File.isLastImg');

  useEffect(() => {
    if (!fileDetail?.guid || !fileDetail?.parent_guid) return;

    const fetchAndCheck = async () => {
      const [, res] = await catchApiResult(fileApi.getFileDir(fileDetail.parent_guid));
      const list = res?.data.list || [];
      const imgList = list.filter((item) => item.type === 'img');
      const targetIndex = imgList.findIndex((item) => item.guid === fileDetail.guid);
      setDisableNextBtn(targetIndex === -1 || targetIndex === imgList.length - 1);
    };

    fetchAndCheck();
  }, [fileDetail?.guid, fileDetail?.parent_guid]);

  const checkFileExist = async () => {
    const [err] = await catchApiResult(fileApi.fileDetail(fileDetail?.guid));
    if (err) {
      history.push(`/error?code=${err?.code}`);
      return false;
    }
    return true;
  };

  const handleClickPre = async () => {
    // clear sideEffect
    if (disableNextBtn) setDisableNextBtn(false);

    if (showEmptyPage) return;
    const _res = await checkFileExist();
    if (!_res) return;

    const [, res] = await catchApiResult(fileApi.getFileDir(fileDetail?.parent_guid || ''));
    const list = res?.data.list || [];
    const imgList = list.filter((item) => item.type === 'img');
    const targetIndex = imgList.findIndex((item) => item.guid === fileDetail?.guid);
    if (targetIndex === -1) return;
    // 到头了
    if (targetIndex === 0) {
      setShowEmptyPage(true);
      return;
    }

    history.push(`/files/${imgList[targetIndex - 1].guid}`);
  };
  const handleClickNext = async () => {
    if (disableNextBtn) return;
    const _res = await checkFileExist();
    if (!_res) return;

    const [, res] = await catchApiResult(fileApi.getFileDir(fileDetail?.parent_guid || ''));
    const list = res?.data.list || [];
    const imgList = list.filter((item) => item.type === 'img');
    const targetIndex = imgList.findIndex((item) => item.guid === fileDetail?.guid || '');
    if (targetIndex === -1) return;

    if (showEmptyPage) {
      setShowEmptyPage(false);
      history.push(`/files/${imgList[0].guid}`);
      return;
    }

    if (targetIndex === imgList.length - 1) {
      message.open({
        type: 'warning',
        content: i18n_lastImg,
      });
      setDisableNextBtn(true);
      return;
    }

    history.push(`/files/${imgList[targetIndex + 1].guid}`);
  };

  return {
    showEmptyPage,
    disableNextBtn,
    handleClickPre,
    handleClickNext,
  };
}
