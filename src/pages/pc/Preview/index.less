.page {
  @headerHeight: 50px;

  width: 100%;
  height: 100%;
  overflow: hidden;
  position: relative;

  .header {
    display: flex;
    align-items: center;
    padding: 0 20px;
    box-sizing: border-box;
    width: 100%;
    height: 50px;
    background: var(--theme-basic-color-bg-default);
    box-shadow: 0 -1px 0 0 var(--theme-basic-color-lighter) inset;
  }

  .btn {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 28px;
    color: var(--theme-text-color-medium);
    border: 1px solid var(--theme-basic-color-lighter);
    border-radius: 2px;
    background: var(--theme-efficiency-button-color-bg-primary);
    cursor: pointer;

    &:hover {
      background: var(--theme-basic-color-bg-default);
      border: 1px solid var(--theme-basic-color-light);
    }
  }

  .textBtn {
    padding: 0 10px;
    font-size: 12px;
    font-weight: 400;
    margin-right: 10px;
  }

  .btnLayout {
    margin-left: auto;
  }

  .fileTitle {
    font-size: 14px;
    line-height: 24px;
    color: var(--theme-basic-color-primary);
    font-weight: 500;
    margin-left: 10px;
  }

  .iframe {
    width: 100%;
    height: calc(100% - @headerHeight);
    border: none;
  }
}
