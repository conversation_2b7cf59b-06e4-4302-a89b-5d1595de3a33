import { MoreOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { Button, Card, Space, Table } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import { deleteTrashes, getTrashList } from '@/api/File';
import { ReactComponent as TrashNodataSvg } from '@/assets/images/empty/trash.svg';
import { ReactComponent as TrashSvg } from '@/assets/images/sidepanel/trash.svg';
import { createMenu } from '@/components/ContextMenu';
import deleteConfirm from '@/components/fileList/components/deleteConfirm';
import { FileName } from '@/components/fileList/components/FileName';
import { NoData } from '@/components/fileList/components/NoData';
import styles from '@/components/fileList/index.less';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { fm } from '@/modules/Locale';
import { useThemeStore } from '@/store/Theme';
import { useFormatTime } from '@/utils/file';

import { items } from './items';

type DataType = {
  guid: string;
  updatedAt: number;
  createdAt: number;
  name: string;
  type: string;
  isSpace: boolean;
  [key: string]: any;
};

const Trash = () => {
  const { isDark } = useThemeStore((state) => state.theme);

  const { formatTime } = useFormatTime();

  const i18nText = {
    recover: fm('File.recover'),
    deleteCompletely: fm('File.deleteCompletely'),
    title: `${fm('deleteConfirm.title')}?`,
    content: fm('deleteConfirm.content'),
    okText: fm('File.deleteCompletely'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.deleteSuccess'),
    error: fm('File.deleteError'),
    resetFirst: fm('File.resetFirst'),
    resetError: fm('File.resetError'),
    noTrashTitle: fm('File.noTrashTitle'),
    noTrashDescription: fm('File.noTrashDescription'),
  };

  const i18nText_deleteConfirm = {
    title: fm('File.clearTrash'),
    content: fm('File.clearTrashWarn'),
    okText: fm('File.clearTrash'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.clearTrashSuccess'),
    error: fm('File.clearTrashError'),
  };

  const [data, setData] = useState<DataType[]>([]);

  const [noData, setNoData] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});

  const reload = () => {
    setLoading(true);
    getTrashList()
      .then((res: any) => {
        if (res.status === 200) {
          const { data: dataList } = res;
          setData(dataList || []);
          setNoData(dataList ? dataList.length === 0 : true);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const clearTrash = () => {
    deleteConfirm({
      i18nText: i18nText_deleteConfirm,
      data: null,
      api: deleteTrashes,
      callback: reload,
    });
  };

  const handleContextMenu = (e: React.MouseEvent, record: any) => {
    e.preventDefault();

    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({ record, i18nText, reload }),
      theme: isDark ? 'dark' : 'light',
    });
  };

  const columns: TableColumnsType = [
    {
      title: fm('File.fileName'),
      dataIndex: 'name',
      minWidth: 240,
      ellipsis: true,
      render: (value: string, record: any) => {
        return <FileName disabled name={value} record={record} />;
      },
    },
    {
      title: fm('File.deleteTime'),
      width: 160,
      dataIndex: 'updatedAt',
      ellipsis: true,
      render: (value) => formatTime(value * 1000),
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: any, record: object) => {
        return (
          <MoreOutlined
            className="more"
            onClick={(event) => {
              event.stopPropagation();
              handleContextMenu(event, record);
            }}
          />
        );
      },
    },
  ];

  const sortedList = useMemo(() => {
    const spaceData = data
      .filter((item) => item.type === 'folder' && item.isSpace)
      .sort((a, b) => a['updatedAt'] - b['updatedAt']);
    const folderData = data
      .filter((item) => item.type === 'folder' && !item.isSpace)
      .sort((a, b) => a['updatedAt'] - b['updatedAt']);
    const other = data.filter((item) => item.type !== 'folder').sort((a, b) => a['updatedAt'] - b['updatedAt']);
    return [...spaceData, ...folderData, ...other];
  }, [data]);

  useEffect(() => {
    reload();
  }, []);

  return (
    <Card
      className={styles['mainCardTable']}
      extra={
        <Button disabled={data.length === 0} icon={<TrashSvg />} size="small" type="text" onClick={clearTrash}>
          {fm('File.clearTrash')}
        </Button>
      }
      title={
        <Space>
          <span>{fm('SiderMenu.siderMenuTrashText')}</span>
        </Space>
      }
    >
      {!noData ? (
        <Table
          className={tableClassName}
          columns={columns}
          dataSource={sortedList}
          loading={loading}
          pagination={false}
          scroll={{ y: tableMaxHeight, x: 760 }}
          onRow={(record) => {
            return {
              onContextMenu: (event) => {
                handleContextMenu(event, record);
              },
            };
          }}
        />
      ) : (
        <NoData description={i18nText.noTrashDescription} img={<TrashNodataSvg />} title={i18nText.noTrashTitle} />
      )}
    </Card>
  );
};

export default Trash;
