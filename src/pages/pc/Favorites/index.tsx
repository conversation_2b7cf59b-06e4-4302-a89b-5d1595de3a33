import { CheckOutlined, MoreOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import { <PERSON><PERSON>, Card, Dropdown, Table } from 'antd';
import { useEffect, useMemo, useState } from 'react';

import { getStarredFileList } from '@/api/File';
import { ReactComponent as DesktopSvg } from '@/assets/images/empty/desktop.svg';
import { ReactComponent as Downward } from '@/assets/images/svg/downward.svg';
import CollaborationShare from '@/components/Collaboration';
import { createMenu } from '@/components/ContextMenu';
import { FileName } from '@/components/fileList/components/FileName';
import { NoData } from '@/components/fileList/components/NoData';
import RenameModal from '@/components/fileList/components/RenameModal';
import styles from '@/components/fileList/index.less';
import { useFileTypeDownload } from '@/hooks/useFileTypeDownload';
import { useTableMaxHeight } from '@/hooks/useTableMaxHeight';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { useThemeStore } from '@/store/Theme';
import { openFile, useFormatTime } from '@/utils/file';

import { items } from './items';

type FilterType = 'updatedAt' | 'createdAt';

type DataType = {
  guid: string;
  updatedAt: number;
  createdAt: number;
  name: string;
  type: string;
  [key: string]: any;
};

const Favorites = () => {
  const { isDark } = useThemeStore((state) => state.theme);

  const { formatTime } = useFormatTime();

  const { downloadDiffFile } = useFileTypeDownload();

  const i18nText = {
    newTabOpens: fm('File.newTabOpens'),
    star: fm('File.star'),
    starSuccess: fm('File.starSuccess'),
    starError: fm('File.starError'),
    removeStr: fm('File.removeStar'),
    removeStarSuccess: fm('File.removeSuccess'),
    removeStarError: fm('File.removeError'),
    share: fm('File.share'),
    collaboration: fm('File.collaboration'),
    download: fm('File.download'),
    reName: fm('File.reName'),
    png: fm('File.png'),
    moveTo: fm('File.moveTo'),
    copyTo: fm('File.copyTo'),
    copy: fm('FilePathPicker.copy'),
    delete: fm('File.delete'),
    title: `${fm('deleteConfirm.title')}?`,
    content: fm('deleteConfirm.content'),
    okText: fm('deleteConfirm.title'),
    cancelText: fm('deleteConfirm.cancel'),
    success: fm('File.deleteSuccess'),
    error: fm('File.deleteError'),
    downloadSuccess: fm('File.downloadSuccess'),
    downloadError: fm('File.downloadError'),
    noShareTitle: fm('File.noShareTitle'),
    noShareDescription: fm('File.noShareDescription'),
  };
  const meId = useMeStore((state) => state.me.id);

  const [data, setData] = useState<DataType[]>([]);

  const [noData, setNoData] = useState(false);

  const [loading, setLoading] = useState<boolean>(false);

  const [renameVisible, setRenameVisible] = useState(false);

  const [params, setParams] = useState<{ FileName?: string; guid?: string }>({ FileName: '', guid: '' });

  const { tableMaxHeight, tableClassName } = useTableMaxHeight({});

  const [filterType, setFilterType] = useState<FilterType>('updatedAt');

  const [sortOrder, setSortOrder] = useState<'ascend' | 'descend'>('descend'); // 降序

  const [shareVisible, setShareVisible] = useState(false);
  const [shareData, setShareData] = useState<any>();
  const reload = () => {
    setLoading(true);
    getStarredFileList({ orderBy: filterType })
      .then((res: any) => {
        if (res.status === 200) {
          setData(res.data || []);
          setNoData(res.data ? res.data?.length === 0 : true);
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const renameCallback = ({
    visible,
    params,
    refresh,
  }: {
    visible: boolean;
    params?: { fileName?: string; guid?: string };
    refresh?: boolean;
  }) => {
    setRenameVisible(visible);
    setParams(params || {});
    if (refresh) reload();
  };

  const handleContextMenu = (e: React.MouseEvent, record: any) => {
    e.preventDefault();
    setShareData(record);
    createMenu.show({
      position: { x: e.clientX, y: e.clientY },
      items: items({
        record: record,
        i18nText,
        renameCallback,
        reload,
        meId,
        setShareVisible: setShareVisible,
        downloadDiffFile,
      }),
      theme: isDark ? 'dark' : 'light',
    });
  };

  const columns: TableColumnsType = [
    {
      title: fm('File.fileName'),
      dataIndex: 'name',
      minWidth: 160,
      render: (value: string, record: any) => {
        return <FileName name={value} record={record} />;
      },
    },
    {
      title: fm('File.createName'),
      dataIndex: ['user', 'name'],
      width: 160,
      ellipsis: true,
    },
    {
      title: () => {
        return (
          <>
            <Dropdown
              menu={{
                items: [
                  {
                    label: fm('File.updatedAt'),
                    key: 'updatedAt',
                    icon: filterType === 'updatedAt' ? <CheckOutlined /> : <span />,
                  },
                  {
                    label: fm('File.createdAt'),
                    key: 'createdAt',
                    icon: filterType === 'createdAt' ? <CheckOutlined /> : <span />,
                  },
                ],
                onClick: ({ key }: { key: any }) => {
                  setFilterType(key);
                },
              }}
              overlayClassName="updateTimeFilter"
              trigger={['click']}
            >
              <Button size="small" style={{ fontSize: '12px' }} type="text">
                <span>{filterType === 'updatedAt' ? fm('File.updatedAt') : fm('File.createdAt')}</span>
              </Button>
            </Dropdown>
            <Button
              icon={sortOrder === 'descend' ? <Downward /> : <Downward style={{ transform: 'rotate(180deg)' }} />}
              type="text"
              onClick={() => setSortOrder(sortOrder === 'ascend' ? 'descend' : 'ascend')}
            />
          </>
        );
      },
      dataIndex: filterType,
      width: 160,
      render: (value: number) => {
        return <>{formatTime(value * 1000)}</>;
      },
    },
    {
      title: '',
      dataIndex: 'options',
      width: 120,
      render: (value: any, record: object) => {
        return <MoreOutlined className="more" onClick={(event) => handleContextMenu(event, record)} />;
      },
    },
  ];

  const sortedList = useMemo(() => {
    return [...data].sort((a, b) => {
      if (a.type === 'folder' && b.type !== 'folder') return -1;
      if (a.type !== 'folder' && b.type === 'folder') return 1;

      const valueA = a[filterType];
      const valueB = b[filterType];

      return sortOrder === 'descend' ? valueB - valueA : valueA - valueB;
    });
  }, [data, filterType, sortOrder]);

  useEffect(() => {
    reload();
  }, []);

  return (
    <Card className={styles['mainCardTable']} title={fm('SiderMenu.siderMenuFavoritesText')}>
      {!noData ? (
        <Table
          virtual
          className={tableClassName}
          columns={columns}
          dataSource={sortedList}
          loading={loading}
          pagination={false}
          scroll={{ y: tableMaxHeight, x: 760 }}
          onRow={(record: any) => {
            return {
              onDoubleClick: () => {
                openFile(record);
              },
              onContextMenu: (event) => {
                handleContextMenu(event, record);
              },
            };
          }}
        />
      ) : (
        <NoData description={i18nText.noShareDescription} img={<DesktopSvg />} title={i18nText.noShareTitle} />
      )}
      <RenameModal callback={renameCallback} params={params} visible={renameVisible} />
      <CollaborationShare guid={shareData?.guid ?? ''} visible={shareVisible} onCancel={() => setShareVisible(false)} />
    </Card>
  );
};

export default Favorites;
