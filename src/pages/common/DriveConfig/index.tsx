/**
 * 这个页面是方便测试用的。主要的功能包括以下几点：
 * 1、管理和快速切换前端分支（这个功能只在测试环境下生效，打包给客户的版本就算在这里配置了分支也没有意义，因为分支代码不会打包给客户）
 *
 * ⚠️注意：
 * 因为这是一个测试用的配置页面，所以相关代码逻辑不想混入项目的正常业务逻辑中，基于这个考虑，所有代码逻辑都放到这一个页面中实现了。
 */

import cookies from 'js-cookie';
import { useCallback, useRef, useState } from 'react';

import styles from './index.less';

export default function DriveConfig() {
  const branchInputRef = useRef<HTMLInputElement>(null);
  const [branches, setBranches] = useState<string[]>(JSON.parse(localStorage.getItem('drv-config-branches') ?? '[]'));
  const [selectedBranch, setSelectedBranch] = useState<string>(
    localStorage.getItem('drv-config-selected-branch') ?? '',
  );

  const addBranch = useCallback(() => {
    if (!branchInputRef.current) return;
    const branch = branchInputRef.current.value;
    branchInputRef.current.value = '';
    if (!branch) return;
    if (branches.includes(branch)) return;
    const newBranches = [...branches, branch].sort();
    localStorage.setItem('drv-config-branches', JSON.stringify(newBranches));
    setBranches(newBranches);
  }, [branches]);

  const delBranch = useCallback(
    (branch: string) => {
      const newBranches = branches.filter((b) => b !== branch);
      localStorage.setItem('drv-config-branches', JSON.stringify(newBranches));
      setBranches(newBranches);
      if (selectedBranch === branch) {
        setSelectedBranch('');
        localStorage.removeItem('drv-config-selected-branch');
        cookies.remove('branch');
      }
    },
    [branches],
  );

  const deleteAll = useCallback(() => {
    localStorage.removeItem('drv-config-branches');
    localStorage.removeItem('drv-config-selected-branch');
    setBranches([]);
    setSelectedBranch('');
    cookies.remove('branch');
  }, []);

  const selectBranch = useCallback(
    (branch: string) => {
      if (selectedBranch === branch) return;
      localStorage.setItem('drv-config-selected-branch', branch);
      setSelectedBranch(branch);
      cookies.set('branch', branch, { expires: 365, path: '/' });
    },
    [selectedBranch],
  );

  return (
    <div className={styles.container}>
      <h1 className={styles.title}>Drive Config</h1>
      <section className={styles.section}>
        <h2>分支管理</h2>
        <input ref={branchInputRef} type="text" />
        <button className={styles.button} type="button" onClick={addBranch}>
          Add
        </button>
        <button className={styles.button} type="button" onClick={deleteAll}>
          Delete All
        </button>
        <ol>
          {branches.map((branch) => {
            return (
              <li
                key={branch}
                className={selectedBranch === branch ? styles.selected : ''}
                onClick={() => selectBranch(branch)}
              >
                {branch}
                <button
                  className={styles.button}
                  type="button"
                  onClick={(e) => {
                    delBranch(branch);
                    e.stopPropagation();
                  }}
                >
                  del
                </button>
              </li>
            );
          })}
        </ol>
      </section>
    </div>
  );
}
