@charset "UTF-8";

@import './assets/styles/light.less';
@import './assets/styles/dark.less';

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  min-height: 100%;
  font-family:
    BlinkMacSystemFont, 'PingFang SC', Helvetica, Tahoma, Arial, 'Microsoft YaHei', '微软雅黑', '黑体', <PERSON><PERSON>,
    sans-serif, Sim<PERSON>un, '宋体', serif;
}

body {
  overflow: auto;
}

* {
  margin: 0;
  padding: 0;
  outline: none;
}

#root {
  width: 100%;
  height: 100%;
}

/* stylelint-disable-next-line media-feature-name-no-vendor-prefix */
@media screen and (-webkit-min-device-pixel-ratio: 2), screen and (min-resolution: 2dppx) {
  body {
    -webkit-font-smoothing: antialiased;
  }
}

/**
 * 全局弹框样式 tooltip popover
 */

html body .ant-tooltip {
  &.tooltip-offset-y {
    top: 55px !important; // 覆盖 style 里面的样式
  }

  .ant-tooltip-inner[class*='ant-tooltip-inner'] {
    border-radius: 2px;
    background-color: var(--theme-brand-color);
    padding: 5px 8px 4px;
    min-height: initial;
    font-size: 12px;
    font-weight: 400;
    color: var(--theme-text-color-white);
  }

  /* 箭头样式调整 */
  .ant-tooltip-arrow {
    &::before {
      bottom: -2px;
      clip-path: polygon(1.6569px 100%, 50% 1.6569px, 14.3431px 100%, 1.6569px 100%);
      background-color: var(--theme-brand-color);
    }

    &::after {
      bottom: -2px;
      background-color: var(--theme-brand-color);
    }
  }
}

.ant-layout {
  background: var(--theme-layout-color-bg-white);
  color: var(--theme-brand-color);

  .ant-layout-content {
    background: inherit;
    color: inherit;
  }

  .ant-layout-sider-children {
    display: flex;
    flex-direction: column;
  }
}

//sdk 全屏隐藏header
.sm-sheet-full-screen .editor-header {
  display: none;
}

html body .ant-popover {
  &.popover-offset-y {
    top: 47px !important; // 覆盖 style 里面的样式
  }

  .ant-popover-arrow {
    &::before {
      background: var(--theme-basic-color-bg-default);
    }
  }

  .ant-popover-inner[class*='ant-popover-inner'] {
    border-radius: 2px;
    padding: 0;
    border: 1px solid var(--theme-basic-color-lighter);
    box-shadow: 0 8px 18px 0 var(--theme-box-shadow-color-level6);
  }
}

html body .layoutContainer {
  .ant-layout {
    background: var(--theme-layout-color-bg-white);
  }
}

.updateTimeFilter {
  .ant-dropdown-menu {
    width: 140px;
    font-size: 13px;
    padding: 4px 0;

    .ant-dropdown-menu-item {
      padding: 8px 16px;
      font-size: 13px;
      flex-direction: row-reverse;
    }
  }
}

.highlight {
  color: var(--theme-basic-color-guidance);
  background-color: unset;
}

.ant-table-header {
  .ant-table-thead > tr > th {
    border: none;
  }
}

.ant-table-wrapper .ant-table,
.ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder:hover > td,
.ant-table-wrapper .ant-table-tbody > tr.ant-table-placeholder:hover {
  background: var(--theme-layout-color-bg-new-page);
}

.ant-table-tbody {
  .ant-table-row {
    cursor: default;

    &:hover {
      border-radius: 2px;
      border: 1px solid var(--theme-box-shadow-color-level10);
      margin: -1px;

      .ant-table-cell {
        background: var(--theme-menu-color-bg-hover);
      }
    }

    .ant-table-cell {
      color: var(--theme-text-color-secondary);

      /* 正文/M-Regular */
      font-family: 'PingFang SC';
      font-style: normal;
      font-weight: 400;
      line-height: 28px;
    }

    .ant-table-selection-column {
      line-height: 34px;
    }
  }
}

.ant-table-body .ant-table-placeholder .ant-table-cell {
  height: 100%;
  background: var(--theme-layout-color-bg-new-page);
}

.ant-popover-inner-content {
  background: var(--theme-layout-color-bg-white);
}

body div .ant-model-root .ant-modal-centered .ant-model {
  top: 50%;
  transform: translate(0, -50%);
}

.ant-modal {
  .ant-modal-content {
    padding: 24px 32px;

    .ant-modal-close {
      top: 20px;
      inset-inline-end: 26px;
    }

    .ant-modal-header {
      margin-bottom: 16px;
    }
  }
}

//CreateWithNamePop
.ant-form-item&.nameFormItem {
  margin-bottom: 40px;
}

.ant-modal-footer {
  display: flex;
  flex-direction: row-reverse;
  gap: 12px;
}

.ant-btn-default {
  background: linear-gradient(360deg, var(--theme-button-color-default) -0.09%, var(--theme-text-color-white) 100%);
}

/**
 ** closeUploadBoard 样式
*/
.closeUploadBoard {
  top: 50%;
  transform: translate(0, -50%);
}

.closeUploadBoard .ant-modal-confirm-body-wrapper {
  .ant-modal-confirm-paragraph {
    row-gap: 16px;
  }

  .ant-modal-confirm-btns {
    margin-top: 28px;

    .ant-btn-default {
      color: var(--theme-text-color-white);
      border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
      background: var(--theme-efficiency-button-color-bg);

      &:hover {
        color: var(--theme-text-color-white);
        border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
        background: var(--theme-efficiency-button-color-bg);
      }
    }

    .ant-btn-color-primary {
      background: linear-gradient(360deg, var(--theme-button-color-default) -0.09%, var(--theme-text-color-white) 100%);
      color: var(--theme-text-color-default);
      box-shadow: none;
      margin-inline-start: 12px;

      &:hover {
        border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
        background: linear-gradient(
          360deg,
          var(--theme-button-color-default) -0.09%,
          var(--theme-text-color-white) 100%
        );
        color: var(--theme-text-color-default);
      }
    }
  }
}

// 实心按钮
html body .ant-btn-variant-solid {
  color: var(--theme-text-color-white);
  border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
  background: var(--theme-efficiency-button-color-bg);

  &:not(:disabled):not(.ant-btn-disabled):hover {
    background: var(--theme-efficiency-button-color-bg);
    border: 1px solid var(--theme-box-shadow-color-level6, --theme-separator-color-lighter);
    box-shadow: none;
    color: var(--theme-text-color-white);
  }

  &:disabled {
    background: var(--theme-button-color-disabled) !important;
  }
}

.ant-modal-confirm-btns .ant-btn-variant-solid {
  background: var(--theme-efficiency-button-color-bg);
}

// 全局弹框的警告按钮
.ant-modal-confirm-btns .ant-btn-color-dangerous {
  background: var(--theme-button-color-dangerous);
}

// 全局的取消按钮
.ant-btn-variant-outlined:not(:disabled):not(.ant-btn-disabled):hover {
  border-color: var(--theme-separator-color-lighter);
  color: var(--theme-text-color-default);
}

// 空心按钮
html body .ant-btn-variant-outlined {
  color: var(--theme-basic-color-primary);

  &:not(:disabled):not(.ant-btn-disabled):hover {
    color: var(--theme-drive-button-color-active);
    border-color: var(--theme-basic-color-primary);
    background: var(--theme-basic-color-bg-default);
  }
}

.ant-btn {
  border-radius: 2px;
}

html body .ant-btn:not(:disabled):focus-visible {
  outline: none;
}

// 确认删除弹框样式 与 创建文件夹弹框样式
html body .ant-modal {
  .ant-modal-content {
    border-radius: 4px;
    box-shadow: 0 20px 32px 0 var(--theme-box-shadow-color-level6);

    .ant-modal-body {
      .ant-modal-confirm-content {
        margin-top: 16px;
      }
    }
  }

  &.delete-confirm-modal {
    .ant-modal-content {
      padding: 24px 32px;

      .ant-modal-close {
        color: var(--theme-text-color-secondary);
        top: 20px;
        inset-inline-end: 26px;

        &:hover {
          color: var(--theme-text-color-default);
          background-color: var(--theme-text-button-color-hover);
        }
      }

      .ant-modal-body {
        .ant-modal-confirm-title {
          color: var(--theme-text-color-header);
          font-weight: 500;
          line-height: 24px;
        }

        .ant-modal-confirm-content {
          line-height: 24px;
          margin-top: 16px;
        }

        .ant-modal-confirm-btns {
          display: flex;
          flex-direction: row-reverse;
          margin-top: 28px;

          .ant-btn {
            margin-left: 12px;
          }

          .ant-btn-variant-solid {
            background: var(--theme-basic-color-alert);

            &:hover {
              background: var(--theme-button-color-alert-hover);
            }
          }

          .ant-btn-default {
            &:hover {
              color: var(--theme-text-color-default);
              background: var(--theme-basic-color-bg-default);
              border-color: var(--theme-basic-color-light);
            }
          }
        }
      }
    }
  }

  &.create-folder-modal {
    .ant-modal-content {
      background-color: var(--theme-basic-color-bg-default);

      .ant-modal-close {
        color: var(--theme-text-color-secondary);
        top: 20px;
        inset-inline-end: 26px;

        &:hover {
          color: var(--theme-text-color-default);
          background-color: var(--theme-text-button-color-hover);
        }
      }

      .ant-modal-body {
        .ant-modal-confirm-title {
          color: var(--theme-text-color-header);
          font-weight: 500;
          line-height: 24px;
        }

        .ant-modal-confirm-content {
          line-height: 24px;
          margin-top: 16px;

          .ant-input-outlined {
            color: var(--theme-text-color-default);
            border-color: var(--theme-basic-color-lighter);
            background-color: var(--theme-basic-color-bg-default);

            &:focus {
              border-color: var(--theme-basic-color-primary);
              box-shadow: none;
            }

            &::placeholder {
              color: var(--theme-basic-color-black);
            }
          }

          .ant-btn-variant-solid {
            &:disabled {
              color: var(--theme-text-color-disabled);
            }
          }
        }
      }
    }
  }

  //创建模版库弹框
  &.addTemplatePop {
    .ant-form .ant-form-item .ant-select-dropdown {
      .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        background-color: var(--theme-select-color-active-bg);
      }

      .ant-select-item-option {
        padding: 5px 12px;

        .selectIcon {
          display: flex;
          flex-direction: row;
          gap: 8px;
        }
      }
    }

    .ant-input-outlined {
      height: 34px;
      border-radius: 2px;
      border-color: var(--theme-input-color-active-border);
      box-shadow: var(--theme-input-number-color-active-shadow);

      &:focus {
        border-color: var(--theme-basic-color-primary);
        box-shadow: var(--theme-input-number-color-active-shadow);
      }
    }

    .ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer) {
      &:hover {
        .ant-select-selector {
          border-color: var(--theme-basic-color-primary);
        }
      }
    }

    .ant-select-focused.ant-select-outlined:not(.ant-select-disabled):not(.ant-select-customize-input):not(
        .ant-pagination-size-changer
      ) {
      .ant-select-selector {
        border-color: var(--theme-basic-color-primary);
        box-shadow: var(--theme-input-number-color-active-shadow);

        &:hover {
          border-color: var(--theme-basic-color-primary);
        }
      }
    }

    .ant-select-selector {
      height: 34px;
      border-radius: 2px;
    }

    .ant-modal-confirm-paragraph {
      max-width: 100%;
    }

    .ant-modal-confirm-body {
      .ant-select {
        width: 100% !important;
      }
    }

    .ant-modal-confirm-btns {
      display: flex;
      flex-direction: row-reverse;

      .ant-btn-default {
        margin-left: 8px;
      }
    }
  }
}

// 消息提示
html body .ant-message {
  .ant-message-notice-wrapper {
    .ant-message-notice {
      .ant-message-notice-content {
        min-width: 90px;
        max-width: 500px;
        padding: 12px 24px;
        border-radius: 2px;
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        border: 1px solid var(--theme-basic-color-lighter);
        box-shadow: 0 20px 32px 0 var(--theme-box-shadow-color-level10);
        color: var(--theme-text-color-default);
        background: var(--theme-basic-color-bg-default);

        .anticon {
          font-size: 18px;
        }

        .ant-message-custom-content {
          .know {
            margin-left: 10px;
            color: var(--theme-basic-color-notice);
          }
        }
      }
    }
  }
}

// 全局的item hover active 样式
.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):hover {
  background-color: var(--theme-menu-color-bg-hover);
}

.ant-menu-light:not(.ant-menu-horizontal) .ant-menu-item:not(.ant-menu-item-selected):active {
  background-color: var(--theme-menu-color-bg-active);
}

.ant-menu-light .ant-menu-item-selected,
.ant-menu-light > .ant-menu .ant-menu-item-selected {
  background-color: var(--theme-menu-color-bg-active);
  color: var(--theme-text-color-default);
}

.ant-menu-light > .ant-menu .ant-menu-item {
  padding-left: 16px;
}

// 位置选择下拉框样式
html body .ant-select-dropdown {
  &.locationSelectDropdown {
    box-shadow: 0 8px 18px 0 var(--theme-box-shadow-color-level6);
    background-color: var(--theme-basic-color-bg-default);

    .ant-select-item {
      font-size: 13px;
      font-weight: 400;
      color: var(--theme-basic-color-primary);
      background-color: var(--theme-basic-color-bg-default);

      &:hover {
        background-color: var(--theme-basic-color-bg-default);
      }

      &.ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
        background-color: var(--theme-basic-color-bg-default);
        font-weight: 400;
        color: var(--theme-basic-color-primary);
      }

      &.ant-select-item-option-active:not(.ant-select-item-option-disabled) {
        background-color: var(--theme-basic-color-bg-default);
        font-weight: 400;
        color: var(--theme-basic-color-primary);
      }
    }
  }
}

.collaborationModal {
  .ant-select-item {
    font-size: 12px;
  }
}

body .ant-modal-wrap .ant-modal .ant-modal-content.templateModel {
  padding: 0;
  min-width: 1008px;

  .ant-tabs-nav {
    padding: 14px 24px 0;
    margin: 0;

    &::before {
      border-color: var(--theme-separator-color-lighter);
    }
  }

  .ant-tabs-content-holder {
    padding: 16px;
  }
}

.ant-dropdown
  .ant-dropdown-menu
  .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled):hover,
.ant-dropdown-menu-submenu
  .ant-dropdown-menu
  .ant-dropdown-menu-item.ant-dropdown-menu-item-danger:not(.ant-dropdown-menu-item-disabled):hover {
  color: inherit;
  background-color: var(--theme-box-shadow-color-level4);
}

//企业回收站的tree下拉选择器
.myTreeSelect {
  .ant-tree-select-dropdown {
    padding: 0;
  }
}
