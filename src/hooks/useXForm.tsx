import { DatePicker, Form, Input, InputNumber, Select, Switch } from 'antd';
import type { ClassAttributes, InputHTMLAttributes } from 'react';
import React, { useCallback, useEffect, useMemo } from 'react';

import type { XColumnItem, XFormProps } from '@/model/XForm';
import { fm } from '@/modules/Locale';

type FormItemType = keyof typeof FormItemMap;
type FormComponentType = React.ComponentType<unknown>;

const FormItemMap = {
  input: Input,
  select: Select,
  datePicker: DatePicker,
  number: InputNumber,
  switch: Switch,
  textarea: Input.TextArea,
} as const;

interface FormItemComponentProps {
  placeholder?: string;
  disabled?: boolean;
  options?: unknown[];
  [key: string]: unknown;
}

export const useXForm = ({
  form: propForm,
  labelPosition,
  labelWidth,
  initialValues,
  onReset,
  columns,
  onFinish,
  disabled: formDisabled,
}: XFormProps) => {
  // 创建 form 实例
  const [form] = Form.useForm();
  const formInstance = propForm || form;
  const pleaseEnter = fm('Management.pleaseEnter');

  // 计算布局相关的属性
  const getLayoutProps = useMemo(() => {
    if (labelPosition === 'top') {
      return {
        layout: 'vertical' as const,
        labelCol: undefined,
        wrapperCol: undefined,
      };
    }
    return {
      layout: 'horizontal' as const,
      labelCol: { flex: `0 0 ${labelWidth}px` },
      wrapperCol: { flex: '1' },
      labelAlign: labelPosition as 'left' | 'right',
    };
  }, [labelPosition, labelWidth]);

  // 当 initialValues 变化时更新表单值（添加默认值处理）
  useEffect(() => {
    if (initialValues) {
      try {
        formInstance.setFieldsValue(initialValues);
      } catch (error) {
        console.error('设置表单初始值出错:', error);
      }
    } else {
      formInstance.resetFields();
    }
  }, [initialValues, formInstance]);

  // 重置表单
  const handleReset = useCallback(() => {
    formInstance.resetFields();
    onReset?.();
  }, [formInstance, onReset]);

  // 提交表单（增强类型安全）
  const handleFinish = useCallback(
    (values: Record<string, unknown>) => {
      try {
        onFinish?.(values);
      } catch (error) {
        console.error('表单提交出错:', error);
        // 可以在这里添加错误处理逻辑，如显示错误提示等
      }
    },
    [onFinish],
  );

  // 渲染自定义组件
  const renderCustomComponent = useCallback(
    (component: React.ReactNode | React.ComponentType<unknown>, index: number) => (
      <Form.Item key={index}>
        {React.isValidElement(component) ? component : React.createElement(component as React.ComponentType<unknown>)}
      </Form.Item>
    ),
    [],
  );

  // 获取表单项组件
  const getFormComponent = useCallback((type?: FormItemType) => (type ? FormItemMap[type] : undefined), []);

  // 构建组件props
  const buildComponentProps = useCallback(
    (item: XColumnItem, formDisabled: boolean): FormItemComponentProps => {
      const { placeholder, label, options, disabled, ...restProps } = item;
      const props: FormItemComponentProps = {
        placeholder: placeholder || `${pleaseEnter}${label}`,
        disabled: disabled || formDisabled,
        ...restProps,
      };
      if (item.type === 'select' && options) {
        props.options = options;
      }
      return props;
    },
    [pleaseEnter],
  );

  // 渲染表单项
  const renderFormItem = useCallback(
    (item: XColumnItem, index: number) => {
      if (item.component) {
        return renderCustomComponent(item.component, index);
      }
      const { type, name, label, rules = [], required, hidden } = item;
      const itemRules = required ? [{ required: true, message: `${pleaseEnter}${label}` }, ...rules] : rules;
      const Component = getFormComponent(type);
      if (!Component) return null;
      const componentProps = buildComponentProps(item, formDisabled || false);
      return (
        <Form.Item key={name} hidden={hidden} label={label} name={name} rules={itemRules}>
          {React.isValidElement(Component)
            ? Component
            : React.createElement(
                Component as FormComponentType,
                componentProps as (InputHTMLAttributes<HTMLInputElement> & ClassAttributes<HTMLInputElement>) | null,
              )}
        </Form.Item>
      );
    },
    [formDisabled, renderCustomComponent, pleaseEnter, getFormComponent, buildComponentProps],
  );

  const formItems = useMemo(() => {
    if (!columns || columns.length === 0) return null;
    return columns.map(renderFormItem);
  }, [columns, renderFormItem]);
  return {
    renderFormItem,
    formItems,
    handleReset,
    handleFinish,
    formInstance,
    getLayoutProps,
  };
};
