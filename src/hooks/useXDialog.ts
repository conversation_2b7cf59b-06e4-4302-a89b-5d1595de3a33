import type { ReactNode } from 'react';
import { useMemo, useState } from 'react';

import type { CustomModalProps } from '@/model/Dialog';

export const useXDialog = ({
  height = 'auto',
  width = 420,
  className = 'x-dialog',
  children,
  footer = undefined,
}: Partial<CustomModalProps> = {}) => {
  const [openDialog, setOpenDialog] = useState(false);
  const [dialogContent, setDialogContent] = useState<ReactNode>(children);
  const [modalProps, setModalProps] = useState({
    width,
    height,
    className,
    footer,
    title: '',
    onOk: undefined as (() => void) | undefined,
    modalStyles: {
      body: {
        maxHeight: typeof height === 'number' ? `${height}px` : height,
        overflowY: 'auto' as const,
        overflowX: 'hidden' as const,
        padding: '0px',
      },
      content: {
        padding: '20px 24px',
      },
    },
  });

  return useMemo(
    () => ({
      ...modalProps,
      openDialog,
      dialogContent,
      onCancel: () => setOpenDialog(false),
      open: (content?: ReactNode, newProps?: Omit<CustomModalProps, 'children'>) => {
        if (content) setDialogContent(content);
        if (newProps) {
          setModalProps((prev) => ({
            ...prev,
            ...(newProps.title && { title: String(newProps.title) }),
            ...(typeof newProps.footer !== 'undefined' && { footer: newProps.footer }),
            ...(newProps.height && { height: newProps.height }),
            ...(newProps.width && { width: newProps.width }),
            ...(newProps.className && { className: newProps.className }),
            ...(newProps.onOk && { onOk: newProps.onOk }),
          }));
        }
        setOpenDialog(true);
      },
    }),
    [modalProps, openDialog, dialogContent],
  );
};
