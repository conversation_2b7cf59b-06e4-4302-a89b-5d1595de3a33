import { cloneDeep } from 'lodash';
import { useCallback, useEffect, useMemo } from 'react';

import { ReactComponent as AuditIcon } from '@/assets/images/management/sidebar/audit-icon.svg';
import { ReactComponent as MemberIcon } from '@/assets/images/management/sidebar/members-icon.svg';
import { ReactComponent as PackagesIcon } from '@/assets/images/management/sidebar/packages-icon.svg';
import { ReactComponent as PerformanceIcon } from '@/assets/images/management/sidebar/performance-icon.svg';
import { ReactComponent as SettingsIcon } from '@/assets/images/management/sidebar/settings-icon.svg';
import { ReactComponent as SeatsIcon } from '@/assets/images/management/sidebar/whitelist-icon.svg';
import type { BreadcrumbModel, MenuItem } from '@/model/Menus';
import { GetMenuTypeEnum, MenuKey } from '@/model/Menus';
import { fm } from '@/modules/Locale';
import { useMenuStore } from '@/store/ManagementSilder';

interface UseMenusOptions {
  isShowBreadcrumb?: boolean;
}

export default function useMenus({ isShowBreadcrumb = true }: UseMenusOptions = {}) {
  const menus = useMenuStore((state) => state.menus);
  const setMenus = useMenuStore((state) => state.setMenus);
  // 菜单数据预缓存
  const menusMap = useMenuStore((state) => state.menusMap);
  const setMenusMap = useMenuStore((state) => state.setMenusMap);

  const setSelectedMenuItem = useMenuStore((state) => state.setSelectedMenuItem);
  const selectedMenuItem = useMenuStore((state) => state.selectedMenuItem);

  const menusStatic = [
    {
      key: MenuKey.efficiency,
      icon: <PerformanceIcon />,
      label: fm('Management.board'),
      title: fm('Management.board'),
    },
    {
      key: MenuKey.members,
      icon: <MemberIcon />,
      label: fm('Management.memberList'),
      title: fm('Management.memberList'),
    },
    {
      key: MenuKey.audit,
      icon: <AuditIcon />,
      label: fm('Management.auditLog'),
      title: fm('Management.auditLog'),
    },
    {
      key: MenuKey.packages,
      icon: <PackagesIcon />,
      label: fm('Management.kitValuePack'),
      title: fm('Management.kitValuePack'),
    },
    {
      key: MenuKey.whitelist,
      icon: <SeatsIcon />,
      label: fm('Management.onlineSeatWhiteList'),
      title: fm('Management.onlineSeatWhiteList'),
    },
    {
      key: MenuKey.settings,
      icon: <SettingsIcon />,
      label: fm('Management.settings'),
      title: fm('Management.settings'),
    },
  ];
  /**
   * 根据 key 查找菜单项
   * type 查所有层0级，查当前1
   */
  const findMenuItemByKey = useCallback(
    (
      key: string,
      type: GetMenuTypeEnum = GetMenuTypeEnum.GetMenuItem,
    ): Partial<MenuItem>[] | Partial<MenuItem> | undefined => {
      const allMenuValues = Array.from(menusMap.values());
      const currentData = allMenuValues.find((items) => items.some((item) => item.key === key));
      if (type) {
        return currentData ? currentData.find((item) => item.key === key) : undefined;
      } else {
        if (currentData && currentData.length) {
          const resultData = (currentData as Partial<MenuItem>[]).find((item) => item.key === key);
          return currentData.length > 1 ? [currentData[0], resultData as Partial<MenuItem>] : currentData;
        } else {
          return [];
        }
      }
    },
    [menusMap],
  );

  /**
   * 删除菜单项
   * @param key 菜单项的 key
   * @param menus 菜单项数组
   */
  const deleteFormMenus = useCallback(({ key, menus }: { key: string; menus: MenuItem[] }) => {
    const index = menus.findIndex((item) => item?.key === key);
    if (index > -1) {
      menus.splice(index, 1);
    }
  }, []);

  // 菜单数据预处理
  const setMenusCache = useCallback(
    (menus: MenuItem[]) => {
      const newMenusMap = new Map();
      menus.forEach((item) => {
        if (item.children && item.children.length > 0) {
          const current = cloneDeep(item);
          delete current.children;
          delete current.icon;
          newMenusMap.set(item.key, [current, ...item.children]);
        } else {
          newMenusMap.set(item.key, [item]);
        }
      });
      setMenusMap(newMenusMap);
    },
    [setMenusMap],
  );

  const breadcrumbList = useMemo<BreadcrumbModel[]>(() => {
    if (!isShowBreadcrumb) return [];
    if (selectedMenuItem) {
      const menusList = findMenuItemByKey(selectedMenuItem.key as string, GetMenuTypeEnum.GetAllMenuItem);
      if (menusList) {
        return (menusList as Required<MenuItem>[]).map((item) => ({
          title: item.title,
          path: item.key,
        }));
      }
    }
    return [];
  }, [selectedMenuItem, findMenuItemByKey, isShowBreadcrumb]);
  const sliderMenus = useMemo(() => menusStatic, []);
  // 设置菜单权限的代码暂时停止
  useEffect(() => {
    setMenusCache(sliderMenus);
    setMenus(sliderMenus);
  }, [sliderMenus, setMenus, setMenusCache]);
  return {
    menus,
    menusMap,
    setMenus,
    deleteFormMenus,
    setSelectedMenuItem,
    selectedMenuItem,
    findMenuItemByKey,
    breadcrumbList,
  };
}
