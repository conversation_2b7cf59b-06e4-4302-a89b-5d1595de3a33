import { match } from '@formatjs/intl-localematcher';
import { setLocale as setUmiLocale } from 'umi';

import type { Locale } from '@/modules/Locale';
import { getStoredLocale, removeStoredLocale, setCookieLocale, setStoredLocale } from '@/modules/Locale';

const SupportedLocales: Locale[] = ['zh-CN', 'en-US'];
type SupportedLocale = (typeof SupportedLocales)[number];

/**
 * 初始化页面的语言，需要获取 localStorage 中的语言设置，navigator.languages 上的语言偏好,
 * 如果 localStorage 中没有语言设置，说明要更随系统
 * 通过语言协商确定最终的语言
 */
export function initLocale() {
  const languagePrefer = navigator.languages;
  const storedLocale = getStoredLocale();
  let chosenLocale: Locale = 'zh-CN';
  if (storedLocale) {
    if (SupportedLocales.includes(storedLocale)) {
      chosenLocale = storedLocale;
    } else {
      chosenLocale = match(
        [storedLocale],
        SupportedLocales,
        match(languagePrefer, SupportedLocales, 'zh-CN'),
      ) as Locale;
    }
  } else {
    chosenLocale = match(languagePrefer, SupportedLocales, 'zh-CN') as Locale;
  }
  setCookieLocale(chosenLocale);
  setUmiLocale(chosenLocale, false);
}

export function setLocale(localeSetting: SupportedLocale | 'auto') {
  if (localeSetting === 'auto') {
    removeStoredLocale();
    initLocale();
  } else {
    setCookieLocale(localeSetting);
    setStoredLocale(localeSetting);
    setUmiLocale(localeSetting, false);
  }
}
