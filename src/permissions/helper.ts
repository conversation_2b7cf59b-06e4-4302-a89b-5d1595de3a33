import type { Me } from '@/model/Me';

export const isCreator = (user: Me): boolean => {
  return user.teamRole === 'creator';
};

export const isAdmin = (user: Me): boolean => {
  return user.teamRole === 'admin';
};

/**
 * 是过期版
 */
export function isExpiredEdition(me: Me | null): boolean {
  if (!me || !me.membership) {
    return false;
  }
  const {
    membership: { isExpired },
  } = me;
  return isExpired;
}
