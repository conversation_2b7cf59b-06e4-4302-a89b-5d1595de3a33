<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55733)">
<g filter="url(#filter0_d_2040_55733)">
<rect x="1.5" y="4.5" width="21" height="15.75" fill="white"/>
<rect x="1" y="4" width="22" height="16.75" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<rect x="2" y="5" width="20" height="14.75" fill="url(#paint0_linear_2040_55733)" stroke="white"/>
<path opacity="0.6" fill-rule="evenodd" clip-rule="evenodd" d="M13.125 8.25H15.375V9H13.125C12.9179 9 12.75 9.16789 12.75 9.375V12H15V12.75H12.75V15.375C12.75 15.5821 12.9179 15.75 13.125 15.75H15V16.5H13.125C12.5037 16.5 12 15.9963 12 15.375V12.75H10.5V12H12V9.375C12 8.75368 12.5037 8.25 13.125 8.25Z" fill="#BDBEC4"/>
<rect x="4.5" y="10.5" width="6" height="3.75" rx="1" fill="#8E8F9B"/>
<rect x="14.25" y="7.5" width="4.5" height="2.25" rx="0.5" fill="#C0C0C8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.25 11.75C14.25 11.4739 14.4739 11.25 14.75 11.25H18.25C18.5261 11.25 18.75 11.4739 18.75 11.75V13C18.75 13.2761 18.5261 13.5 18.25 13.5H14.75C14.4739 13.5 14.25 13.2761 14.25 13V11.75Z" fill="#C0C0C8"/>
<rect x="14.25" y="15" width="4.5" height="2.25" rx="0.5" fill="#C0C0C8"/>
</g>
<defs>
<filter id="filter0_d_2040_55733" x="-3.5" y="1.5" width="31" height="25.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55733"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55733" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55733" x1="22.5" y1="20.25" x2="22.5" y2="4.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_2040_55733">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
