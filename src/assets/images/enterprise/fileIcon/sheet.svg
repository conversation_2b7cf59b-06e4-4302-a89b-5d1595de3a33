<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55727)">
<g filter="url(#filter0_d_2040_55727)">
<rect x="1.5" y="4.5" width="21" height="15.75" fill="white"/>
<rect x="1" y="4" width="22" height="16.75" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<rect x="2" y="5" width="20" height="14.75" fill="url(#paint0_linear_2040_55727)" stroke="white"/>
<rect x="1.5" y="9" width="6.75" height="11.25" fill="black" fill-opacity="0.04"/>
<rect x="1.5" y="4.5" width="21" height="4.5" fill="#BCDDC3" fill-opacity="0.8"/>
<rect x="8.25" y="12" width="13.5" height="0.75" fill="#41464B" fill-opacity="0.1"/>
<rect x="8.25" y="15.75" width="13.5" height="0.75" fill="#41464B" fill-opacity="0.1"/>
<rect x="1.5" y="8.25" width="21" height="0.75" fill="#41464B" fill-opacity="0.1"/>
<rect x="7.5" y="4.5" width="0.75" height="15.75" fill="#41464B" fill-opacity="0.1"/>
<rect x="15" y="4.5" width="0.75" height="15" fill="#41464B" fill-opacity="0.1"/>
</g>
<defs>
<filter id="filter0_d_2040_55727" x="-3.5" y="1.5" width="31" height="25.75" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55727"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55727" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55727" x1="22.5" y1="20.25" x2="22.5" y2="4.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_2040_55727">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
