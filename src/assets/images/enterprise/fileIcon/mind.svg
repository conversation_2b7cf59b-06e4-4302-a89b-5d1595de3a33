<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_2040_55747)">
<g filter="url(#filter0_d_2040_55747)">
<path d="M14.9944 1.5H3.75V22.5H20.25V6.75558L14.9944 1.5Z" fill="white"/>
<path d="M15.348 1.14645L15.2015 1H14.9944H3.75H3.25V1.5V22.5V23H3.75H20.25H20.75V22.5V6.75558V6.54848L20.6036 6.40203L15.348 1.14645Z" stroke="#41464B" stroke-opacity="0.15"/>
</g>
<path d="M4.25 22V2H14.7873L19.75 6.96269V22H4.25Z" fill="url(#paint0_linear_2040_55747)" stroke="white"/>
<g filter="url(#filter1_d_2040_55747)">
<path d="M15.0105 1.5H15V6.75H20.25V6.73953L15.0105 1.5Z" fill="white"/>
</g>
<path opacity="0.6" fill-rule="evenodd" clip-rule="evenodd" d="M14.25 10C14.25 9.44772 14.6977 9 15.25 9H17.75C18.3023 9 18.75 9.44772 18.75 10V11C18.75 11.5523 18.3023 12 17.75 12H15.25C14.6977 12 14.25 11.5523 14.25 11V10Z" fill="#7397D8"/>
<rect opacity="0.6" x="14.25" y="16.5" width="4.5" height="3" rx="1" fill="#7397D8"/>
<path opacity="0.6" fill-rule="evenodd" clip-rule="evenodd" d="M5.25 10C5.25 9.44772 5.69772 9 6.25 9H8.75C9.30229 9 9.75 9.44772 9.75 10V11C9.75 11.5523 9.30229 12 8.75 12H6.25C5.69772 12 5.25 11.5523 5.25 11V10Z" fill="#7397D8"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M9 13.75C9 13.1977 9.44772 12.75 10 12.75H14C14.5523 12.75 15 13.1977 15 13.75V14.75C15 15.3023 14.5523 15.75 14 15.75H10C9.44772 15.75 9 15.3023 9 14.75V13.75Z" fill="#7397D8"/>
<rect opacity="0.6" x="5.25" y="16.5" width="4.5" height="3" rx="1" fill="#7397D8"/>
<path d="M14.25 10.5H13.375C12.8227 10.5 12.375 10.9477 12.375 11.5V17C12.375 17.5523 12.8227 18 13.375 18H14.25" stroke="#7397D8" stroke-width="0.5"/>
<path d="M9.75 10.5H10.25C10.8023 10.5 11.25 10.9477 11.25 11.5V17C11.25 17.5523 10.8023 18 10.25 18H9.75" stroke="#7397D8" stroke-width="0.5"/>
</g>
<defs>
<filter id="filter0_d_2040_55747" x="-1.25" y="-1.5" width="26.5" height="31" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55747"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55747" result="shape"/>
</filter>
<filter id="filter1_d_2040_55747" x="13" y="0.5" width="9.25" height="9.25" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="1"/>
<feGaussianBlur stdDeviation="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_2040_55747"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_2040_55747" result="shape"/>
</filter>
<linearGradient id="paint0_linear_2040_55747" x1="20.25" y1="22.5" x2="20.25" y2="1.5" gradientUnits="userSpaceOnUse">
<stop offset="0.00038243" stop-color="#FCFCFC"/>
<stop offset="1" stop-color="#F5F5F5"/>
<stop offset="1" stop-color="#F5F5F5"/>
</linearGradient>
<clipPath id="clip0_2040_55747">
<rect width="24" height="24" fill="white"/>
</clipPath>
</defs>
</svg>
