:root .drive-dark {
  --brand-color: #c5c5c6;
  --red10: #5c1514;
  --red9: #661f1f;
  --red8: #7a2c2c;
  --red7: #853d3c;
  --red6: #a35252;
  --red5: #ad5e5e;
  --red4: #b87372;
  --red3: #c78685;
  --red2: #d69796;
  --red1: #ebaaa9;
  --orange10: #201008;
  --orange9: #29140a;
  --orange8: #31190d;
  --orange7: #3d2110;
  --orange6: #512c15;
  --orange5: #5d3822;
  --orange4: #6e4227;
  --orange3: #8f6650;
  --orange2: #b27e64;
  --orange1: #cc967a;
  --yellow10: #5f4d2b;
  --yellow9: #756132;
  --yellow8: #856e35;
  --yellow7: #948134;
  --yellow6: #a39836;
  --yellow5: #bfbc40;
  --yellow4: #ccc966;
  --yellow3: #d3d07e;
  --yellow2: #dbda99;
  --yellow1: #e4e4b4;
  --green10: #1e421e;
  --green9: #2d542c;
  --green8: #386237;
  --green7: #427141;
  --green6: #4e804d;
  --green5: #5a855f;
  --green4: #668f69;
  --green3: #6f9974;
  --green2: #7ca381;
  --green1: #96b89b;
  --cyan10: #0d1f21;
  --cyan9: #102528;
  --cyan8: #153237;
  --cyan7: #1a3b42;
  --cyan6: #1e454d;
  --cyan5: #29555a;
  --cyan4: #385f66;
  --cyan3: #3e6a72;
  --cyan2: #60878f;
  --cyan1: #6e969e;
  --blue10: #1d284c;
  --blue9: #233556;
  --blue8: #2c4468;
  --blue7: #2d4f7b;
  --blue6: #4b6c96;
  --blue5: #608abf;
  --blue4: #558fbe;
  --blue3: #72a4cb;
  --blue2: #95bcda;
  --blue1: #bdd5e5;
  --purple10: #1d1b24;
  --purple9: #1d1a29;
  --purple8: #201c2e;
  --purple7: #241f33;
  --purple6: #292338;
  --purple5: #332d3d;
  --purple4: #3b3647;
  --purple3: #4f475d;
  --purple2: #6c5e80;
  --purple1: #a296b2;
  --gray120: #fff;
  --gray110: #f5f6f7;
  --gray100: #f0f0f0;
  --gray90: #e7e7e8;
  --gray80: #d0d1d1;
  --gray70: #b9b9ba;
  --gray60: #8d9093;
  --gray50: #8b8c8d;
  --gray40: #737476;
  --gray30: #5b5d5e;
  --gray20: #454648;
  --gray10: #2d2f30;
  --gray9: #2b2d2f;
  --gray8: #282a2c;
  --gray7: #26282a;
  --gray6: #242627;
  --gray5: #222426;
  --gray4: #1f2123;
  --gray3: #1d1f21;
  --gray2: #1b1d1e;
  --gray1: #191b1d;
  --gray0: #16181a;
  --transparency120: #fff;
  --transparency110: #f5f6f7;
  --transparency100: rgba(255, 255, 255, 100%);
  --transparency90: rgba(255, 255, 255, 90%);
  --transparency80: rgba(255, 255, 255, 80%);
  --transparency70: rgba(255, 255, 255, 70%);
  --transparency75: rgba(255, 255, 255, 75%);
  --transparency60: rgba(255, 255, 255, 60%);
  --transparency50: rgba(255, 255, 255, 50%);
  --transparency40: rgba(255, 255, 255, 40%);
  --transparency30: rgba(255, 255, 255, 30%);
  --transparency20: rgba(255, 255, 255, 20%);
  --transparency15: rgba(255, 255, 255, 15%);
  --transparency10: rgba(255, 255, 255, 10%);
  --transparency8: rgba(255, 255, 255, 8%);
  --transparency5: rgba(255, 255, 255, 5%);
  --transparency6: rgba(255, 255, 255, 6%);
  --transparency4: rgba(255, 255, 255, 4%);
  --transparency2: rgba(255, 255, 255, 2%);
  --theme-brand-color: var(--brand-color);
  --input-border-shadow: 0 1px 3px 0 rgba(41, 42, 44, 40%) inset;

  // 语义化
  // 基本配色
  --theme-basic-color-primary: var(--brand-color);
  --theme-basic-color-notice: var(--blue5);
  --theme-basic-color-success: var(--green6);
  --theme-basic-color-warning: var(--yellow7);
  --theme-basic-color-alert: var(--red6);
  --theme-basic-color-guidance: var(--blue6);
  --theme-basic-color-lighter: var(--transparency10);
  --theme-basic-color-light: var(--transparency20);
  --theme-basic-color-black: var(--transparency30);
  --theme-basic-color-bg-default: var(--gray0);

  // decorated
  --theme-decorated-color-february: var(--green5);
  --theme-decorated-color-april: var(--purple3);
  --theme-decorated-color-june: var(--red4);
  --theme-decorated-color-july: var(--orange4);
  --theme-decorated-color-october: #c3ab7c;
  --theme-decorated-color-november: var(--cyan3);
  --theme-decorated-color-december: var(--blue9);

  // 文本 icon
  --theme-text-color-header: var(--transparency120);
  --theme-text-color-default: var(--transparency100);
  --theme-text-color-medium: var(--transparency80);
  --theme-text-color-secondary: var(--transparency60);
  --theme-text-color-disabled: var(--transparency30);
  --theme-text-color-white: var(--gray0);
  --theme-text-color-alert: var(--red6);
  --theme-text-color-warn: var(--orange6);
  --theme-text-color-succeed: var(--green6);
  --theme-text-color-guidance: var(--blue6);
  --theme-text-color-deep: var(--gray120);
  --theme-text-color-highlight-bg: var(--blue2);
  --theme-icon-info-color: var(--transparency30);
  --theme-text-color-arrow: var(--transparency30);

  // 按钮
  --theme-button-color-primary: var(--blue6);
  --theme-button-color-primary-hover: var(--blue7);
  --theme-button-color-primary-active: var(--blue8);
  --theme-button-color-disabled: var(--gray5);
  --theme-button-color-default: var(--gray5);
  --theme-button-color-alert: var(--red6);
  --theme-button-color-alert-hover: var(--red7);
  --theme-button-color-alert-active: var(--red8);
  --theme-link-button-color: var(--blue5);
  --theme-link-button-color-hover: var(--blue7);
  --theme-link-button-color-active: var(--blue8);
  --theme-link-button-color-disabled: var(--transparency30);
  --theme-drive-button-color: var(--transparency100);
  --theme-drive-button-color-hover: var(--transparency80);
  --theme-drive-button-color-active: var(--transparency120);
  --theme-text-button-color-hover: var(--transparency5);
  --theme-text-button-color-active: var(--transparency10);

  // 分割线
  --theme-separator-color-black: var(--transparency30);
  --theme-separator-color-light: var(--transparency20);
  --theme-separator-color-lighter: var(--transparency10);
  --theme-separator-color-guidance: var(--blue6);

  // 遮罩
  --theme-mask-layer-color-light: var(--transparency50);
  --theme-mask-layer-color-dark: var(--transparency80);

  // 菜单 列表 类
  --theme-menu-color-bg-default: var(--gray0);
  --theme-menu-color-bg-hover: var(--transparency5);
  --theme-menu-color-bg-active: var(--transparency10);

  // 信息card
  --theme-card-info-bg-hover: var(--gray4);
  --theme-card-info-bg-active: var(--transparency10);
  --theme-card-info-bg-hover-border: var(--transparency10);
  --theme-card-info-bg-active-border: var(--transparency20);

  // 表格
  --theme-table-color-header-bg: var(--gray0);
  --theme-table-color-header-gray-bg: var(--gray8);
  --theme-table-color-body-bg: var(--gray0);

  // checkbox
  --theme-checkbox-color: var(--brand-color);
  --theme-checkbox-color-check: var(--gray0);

  // layout 布局容器背景
  --theme-layout-color-bg-white: var(--gray0);
  --theme-layout-color-bg-new-page: var(--gray3);
  --theme-layout-color-bg-editor: var(--gray4);
  --theme-layout-color-bg-black: #000;

  // date picker
  --theme-datepicker-color-cell-active-bg: var(--blue6);
  --theme-datepicker-color-cell-active-range-bg: var(--blue1);

  // box-shadow
  --theme-box-shadow-color-level10: var(--transparency10);
  --theme-box-shadow-color-level8: var(--transparency8);
  --theme-box-shadow-color-level6: var(--transparency6);
  --theme-box-shadow-color-level4: var(--transparency4);

  // status
  --theme-status-color-bg-disabled: var(--transparency10);
  --theme-status-color-bg-notice: var(--blue2);
  --theme-status-color-bg-success: var(--green2);
  --theme-status-color-bg-warning: var(--yellow2);
  --theme-status-color-bg-alert: var(--red2);
  --theme-status-color-bg-guidance: var(--blue2);

  // chart
  --theme-chart-color-blue: var(--blue3);
  --theme-chart-color-yellow: var(--yellow3);
  --theme-chart-color-green: var(--green2);
  --theme-chart-color-gray: var(--gray8);
  --theme-chart-color-cyan: var(--cyan2);
  --theme-chart-color-orange: var(--orange2);
  --theme-chart-color-red: var(--red3);
  --theme-chart-color-purple: var(--purple2);
  --theme-chart-color-bg: var(--gray4);
  --theme-chart-panel-color-bg-gray: var(--gray3);
  --theme-chart-panel-color-over-bg-red: var(--red1);
  --theme-chart-tip-color-bg: var(--gray80);
  --theme-chart-tip-color-text: var(--gray10);

  // select 组件
  --theme-select-color-selected-bg: var(--transparency10);
  --theme-select-color-active-bg: var(--transparency5);
  --theme-select-color-active-border: var(--transparency30);
  --theme-select-color-active-shadow: var(--input-border-shadow);

  // input 组件
  --theme-input-color-active-border: var(--transparency30);
  --theme-input-color-active-shadow: var(--input-border-shadow);
  --theme-input-color-error-active-border: var(--red6);
  --theme-input-color-error-active-shadow: var(--red1);

  // input number 组件
  --theme-input-number-color-active-border: var(--transparency30);
  --theme-input-number-color-active-shadow: var(--input-border-shadow);
  --theme-input-number-color-error-active-border: var(--red6);
  --theme-input-number-color-error-active-shadow: var(--red1);

  // radio
  --theme-radio-color: var(--gray100);

  // breadcrumb
  --theme-breadcrumb-color-active: var(--gray120);

  // tooltip
  --theme-tooltip-color-bg: var(--gray80);
  --theme-tooltip-color-text: var(--gray10);

  // avatar
  --theme-avatar-color-bg: var(--blue2);

  // notice
  --theme-notice-color-bg: var(--blue1);

  // progress
  --theme-progress-color-bg: var(--blue6);
  --theme-progress-color-warn: var(--red6);

  // card
  --theme-card-guidance: var(--blue1);
  --theme-card-default: var(--gray4);

  // tag
  --theme-red-tag-color-bg: var(--red1);
  --theme-red-tag-color-border: var(--red2);
  --theme-red-tag-color-text: var(--red6);

  // efficiency
  --theme-efficiency-card-text-color: var(--transparency60);
  --theme-efficiency-card-unit-color: var(--transparency80);
  --theme-efficiency-card-ratio-color: var(--gray100);
  --theme-efficiency-card-color-bg: linear-gradient(272deg, rgba(241, 245, 255, 85%) 0%, rgba(248, 250, 255, 85%) 100%);
  --theme-efficiency-button-color-bg: linear-gradient(0deg, var(--gray120) -0.09%, var(--gray90) 100%);
  --theme-efficiency-button-color-bg-primary: linear-gradient(
    180deg,
    var(--theme-basic-color-bg-default) 0%,
    var(--theme-button-color-default) 100%
  );

  // image
  --user-center-bg: url('../images/common/<EMAIL>');
  //渐变红 按钮
  --theme-button-color-dangerous: linear-gradient(0deg, var(var(--red8)) -0.09%, var(var(--red7)) 100%);
}
