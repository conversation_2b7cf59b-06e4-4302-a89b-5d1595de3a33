import { Button, Input, Select, Spin } from 'antd';
import VirtualList from 'rc-virtual-list';
import React, { useEffect, useMemo, useRef, useState } from 'react';

import * as createApi from '@/api/Create';
import * as fileApi from '@/api/File';
import * as spaceApi from '@/api/space';
import folderIcon from '@/assets/images/fileIcon/<EMAIL>';
import { ReactComponent as DesktopIcon } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as SpaceIcon } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as ArrowDownIcon } from '@/assets/images/svg/arrowDown-18.svg';
import { ReactComponent as ArrowLeftIcon } from '@/assets/images/svg/arrowLeft-18.svg';
import { ReactComponent as ArrowRightIcon } from '@/assets/images/svg/arrowRight-18.svg';
import { ReactComponent as CheckOutlined } from '@/assets/images/svg/check.svg';
import { ReactComponent as EmptyFolder } from '@/assets/images/svg/emptyFolder.svg';
import { ReactComponent as SolidArrowDownIcon } from '@/assets/images/svg/solidArrowDown.svg';
import { useFileIcon } from '@/hooks/FileIcon';
import { fm2 } from '@/modules/Locale';
import type { FileDetail } from '@/types/api';
import to from '@/utils/await-to';

import type { FileItem, FolderItem, LocationOption, ViewMode } from './FolderManager';
import { FolderManager } from './FolderManager';
import { Footer } from './Footer';
import { Search } from './Search';
import css from './style.less';
import { processFileList } from './utils';

interface ContentProps {
  filename?: string;
  locationGuid: string;
  onLocationChange: (locationGuid: string) => void;
  sideMenuId: string;
  onSideMenuChange?: (menuId: string) => void;
  onCancel: () => void;
  onOk: (folderName: string) => void;
  type?: 'create' | 'move';
  role?: string;
  isAdmin?: boolean;
}

export function Content({
  filename,
  locationGuid,
  onLocationChange,
  sideMenuId,
  onSideMenuChange,
  onCancel,
  onOk,
  type = 'create',
  role,
  isAdmin,
}: ContentProps) {
  // 创建FolderManager实例
  const managerRef = useRef<FolderManager | null>(null);
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [expandedFolders, setExpandedFolders] = useState<Record<string, boolean>>({});
  const [curGridFolder, setCurGridFolder] = useState<string | null>(null);
  const [curFolder, setCurFolder] = useState<FolderItem | null>(null);
  const [folders, setFolders] = useState<FolderItem[]>([]);
  const [files, setFiles] = useState<FileItem[]>([]);
  const [curLocationName, setCurLocationName] = useState<string>('');
  const { getFileIcon } = useFileIcon();
  const [locationBtnClicked, setLocationBtnClicked] = useState(false);
  // 是否来自搜索点击
  const [searchItemClicked, setSearchItemClicked] = useState(false);
  // 存储最近的文件夹列表
  const [recentFolders, setRecentFolders] = useState<FolderItem[]>([]);
  const [loading, setLoading] = useState(true);

  // 生成带有当前文件夹的位置选项
  const curLocationOptions = useMemo(() => {
    const locationOptions: LocationOption[] = [
      { value: 'desktop', label: fm2('FilePathPicker.desktop') },
      { value: 'space', label: fm2('FilePathPicker.space') },
    ];
    if (!managerRef.current) return locationOptions;

    // 添加最近的文件夹到下拉框选项中
    const recentOptions: LocationOption[] = recentFolders.map((folder) => ({
      ...folder,
      value: folder.id,
      label: folder.name,
    }));
    return [...recentOptions, ...locationOptions];
  }, [recentFolders]);

  // 获取FolderManager实例的安全方法
  function getFolderManager() {
    if (!managerRef.current) {
      throw new Error('FolderManager未初始化');
    }
    return managerRef.current;
  }

  function updateContent(result: { folders: FolderItem[]; files: FileItem[] }) {
    if (result) {
      setFolders([...result.folders]);
      setFiles([...result.files]);
    }
  }

  async function getFilesByGuid({ type, guid = 'Desktop' }: { type?: 'used' | 'shared'; guid?: string }) {
    let params = {};
    if (type) {
      params = { type };
    } else {
      params = { folder: guid };
    }
    const [, res] = await to(fileApi.files(params));
    if (res?.status === 200) {
      const list = res.data.list || [];
      const { files: fileItems, folders: folderItems } = processFileList(list);
      // 返回处理后的数据，以便在enterFolder中使用
      return {
        files: fileItems,
        folders: folderItems,
      };
    }
    return null;
  }

  async function getSpaceList() {
    const [, res] = await to(spaceApi.getSpaceList({}));
    if (res?.status === 200) {
      const spaces = res.data.spaces || [];
      const folderItems = spaces.map((item: FileDetail) => ({
        id: item.guid,
        name: item.name,
        type: 'folder',
        sourceMenuId: 'space',
        role: item.role,
      }));
      return {
        files: [],
        folders: folderItems,
      };
    }
  }

  // 获取最近位置
  async function getRecentList() {
    const [, res] = await to(fileApi.recentLocation());
    if (res?.status === 200) {
      const list = res.data.data?.items || [];
      const folderItems = list
        .sort((a: FileDetail, b: FileDetail) => b.updatedAt - a.updatedAt)
        .map((item: FileDetail) => ({
          id: item.guid,
          name: item.name,
          type: 'folder',
          sourceMenuId: item.isDesktop ? 'desktop' : 'space',
          role: item.role,
        }));
      return {
        files: [],
        folders: folderItems,
      };
    }
  }

  async function getStarredList() {
    const [, res] = await to(fileApi.getStarredFileList({ orderBy: 'updatedAt' }));
    if (res?.status === 200) {
      const list = res.data || [];
      const { files: fileItems, folders: folderItems } = processFileList(list);
      return {
        files: fileItems,
        folders: folderItems,
      };
    }
  }

  function initManagerState() {
    if (!managerRef.current) return;
    const initialState = managerRef.current.getState();
    setViewMode(initialState.viewMode);
    setExpandedFolders(initialState.expandedFolders);
    setCurGridFolder(initialState.curGridFolder);
    setCurFolder(initialState.curFolder);
  }

  // 通用的菜单切换处理函数  needUpdate 是否需要更新界面
  async function handleMenuSwitch(menuId: string, needUpdate = false) {
    const folderManager = getFolderManager();
    const folderMap = folderManager.getFolderMapByKey(menuId);

    if (!folderMap) {
      let result = null;

      // 根据不同的菜单类型调用相应的API
      switch (menuId) {
        case 'used':
          result = await getRecentList();
          break;
        case 'shared':
        case 'desktop': {
          const params = menuId === 'shared' ? { type: menuId } : {};
          result = await getFilesByGuid(params);
          break;
        }
        case 'favorites': {
          result = await getStarredList();
          break;
        }
        case 'space': {
          result = await getSpaceList();
          break;
        }
      }

      if (result) {
        if (needUpdate) {
          updateContent(result);
          folderManager.setState(result.folders, result.files);
        }
        folderManager.setFolderMapByKey(menuId, { folders: result.folders, files: result.files });
      }
    } else {
      folderManager.setState(folderMap.folders, folderMap.files);
      initManagerState();
      updateContent(folderMap);
    }
  }

  async function initManager() {
    const result = await getFilesByGuid({ guid: 'Desktop' });
    if (result) {
      updateContent(result);

      if (!managerRef.current) {
        managerRef.current = new FolderManager({
          eventHandlers: {
            onLocationChange: (locationGuid: string) => onLocationChange(locationGuid),
          },
          files: result?.files || [],
          folders: result?.folders || [],
          currentSideMenuId: sideMenuId || 'desktop',
        });

        getFolderManager().setFolderMapByKey('desktop', { folders: result.folders, files: result.files });

        // 获取初始状态
        initManagerState();
        setLoading(false);

        // 初始化时获取最近的文件夹列表
        const recentResult = await getRecentList();
        if (recentResult) {
          // 保存最近的5个文件夹用于下拉框显示
          setRecentFolders(recentResult.folders.slice(0, 5));
          getFolderManager().setFolderMapByKey('used', { folders: recentResult.folders, files: recentResult.files });
        }

        ['shared', 'favorites', 'space'].forEach((menuId) => handleMenuSwitch(menuId));
      }
    }
  }

  // 初始化FolderManager
  useEffect(() => {
    initManager();
  }, []);

  useEffect(() => {
    setCurLocationName(locationGuid);
  }, [locationGuid]);

  useEffect(() => {
    if (!curFolder) return setCurLocationName(sideMenuId === 'space' ? '' : 'desktop');
    setCurLocationName(curFolder.name);
  }, [curFolder]);

  // 处理侧边栏菜单切换
  useEffect(() => {
    async function getFiles() {
      if (!managerRef.current) return;

      const folderManager = getFolderManager();
      // 更新FolderManager中的当前侧边栏菜单ID
      folderManager.setCurrentSideMenuId(sideMenuId);

      // 如果是通过搜索项点击触发的菜单切换，则不需要额外加载文件
      // 因为已经在 searchItemClick 中加载了对应的文件
      if (searchItemClicked) return;

      // 将当前菜单状态添加到历史记录
      // 只有在手动切换菜单（非后退/前进按钮导致的切换）时才添加历史记录
      if (!locationBtnClicked) {
        folderManager.addToHistory(folderManager.getState().curFolder?.id ?? null);
      }

      const locationGuid = sideMenuId === 'space' ? 'space' : 'desktop';
      setCurLocationName(locationGuid);
      onLocationChange(locationGuid);

      await handleMenuSwitch(sideMenuId, true);
    }
    getFiles();
    setCurFolder(null);

    // 重置locationBtnClicked标记
    if (locationBtnClicked) {
      setLocationBtnClicked(false);
    }
  }, [sideMenuId]);

  // 处理视图模式切换 暂时注释，后续再放开
  // function handleViewModeChange(mode: ViewMode) {
  //   setViewMode(mode);
  //   getFolderManager().setViewMode(mode);
  //   // 切换视图时重置当前选中的文件夹
  //   if (mode === 'grid') {
  //     setCurGridFolder(null);
  //   }
  // }

  async function createFolder(name: string, guid: string) {
    const [, res] = await to(createApi.createFolder(name, guid));
    if (res?.status === 200) {
      const data = res.data;
      if (guid === 'Desktop') {
        const newFolder = {
          id: data.guid,
          name,
          type: 'folder',
          sourceMenuId: 'desktop',
          role: data.role,
        };

        // 在顶层桌面菜单时，更新文件夹，其他菜单不更新
        if (sideMenuId === 'desktop') {
          setFolders([newFolder, ...folders]);
        }
        const manager = getFolderManager();
        const folderMap = manager.getFolderMapByKey('desktop');
        // 桌面文件夹有新增，需要更新对应的 folderMap
        if (folderMap) {
          folderMap.folders = [newFolder, ...folderMap.folders];
          manager.setFolderMapByKey('desktop', folderMap);
          manager.setState(folderMap.folders, folderMap.files);
        }

        return;
      }

      if (!curFolder) return;
      const newFolder = {
        id: data.guid,
        name,
        type: 'folder',
        sourceMenuId: curFolder.sourceMenuId,
      };
      if (curFolder.children) {
        curFolder.children.unshift(newFolder);
      } else {
        curFolder.children = [newFolder];
      }
      setFolders([newFolder, ...folders]);
    }
  }

  // 加载文件夹子级内容
  async function loadFolderChildren(folder: FolderItem) {
    // 如果curFolder没有子级，则获取文件夹子级内容
    if (!folder?.children) {
      // 获取文件夹子级内容
      const result = await getFilesByGuid({ guid: folder.id });
      if (result) {
        const children = [...result.folders, ...result.files];
        getFolderManager().updateFolderChildren(folder, children);
      } else {
        getFolderManager().updateFolderChildren(folder, []);
      }
      return result;
    } else {
      const folders = folder.children.filter((item: FolderItem | FileItem) => item.type === 'folder') as FolderItem[];
      const files = folder.children.filter((item: FolderItem | FileItem) => item.type !== 'folder') as FileItem[];
      return { folders, files };
    }
  }

  // 切换文件夹展开/收起状态
  async function toggleFolder(folder: FolderItem, e: React.MouseEvent) {
    // 阻止事件冒泡，防止触发enterFolder
    e.stopPropagation();

    await loadFolderChildren(folder);

    getFolderManager().toggleFolder(folder.id);
    setExpandedFolders((prev) => ({
      ...prev,
      [folder.id]: !prev[folder.id],
    }));
  }

  // 进入文件夹
  async function enterFolder(folder: FolderItem) {
    setLoading(true);
    try {
      setCurLocationName(folder.name);

      // 点击的不是搜索项
      if (!searchItemClicked) {
        const result = await loadFolderChildren(folder);
        if (result) {
          updateContent(result);
        }
        getFolderManager().enterFolder(folder);
        setCurFolder(folder);
      } else {
        // 点击的是搜索项
        setSearchItemClicked(false);
        const result = await getFilesByGuid({ guid: folder.id });

        if (result) {
          updateContent(result);
        }
        setCurFolder(null);

        // 进入文件夹
        getFolderManager().enterFolder(folder);
      }
    } catch (error) {
      console.error('进入文件夹失败:', error);
    }
    setLoading(false);
  }

  async function getFileRole(folder: FolderItem) {
    const [err, res] = await to(fileApi.fileDetail(folder.id));
    if (res?.status !== 200) {
      console.error('获取文件夹权限失败:', err?.data);
      return 'none';
    }
    return res.data.role;
  }

  // 搜索结果点击
  async function searchItemClick(folder: FolderItem) {
    setLoading(true);
    const role = await getFileRole(folder);
    folder.role = role;

    setSearchItemClicked(true);
    const manager = getFolderManager();
    // 如果所属菜单与当前菜单不同，则切换菜单
    if (folder.sourceMenuId !== sideMenuId) {
      // 设置标记，避免添加到历史记录
      const locationGuid = folder.sourceMenuId === 'space' ? '' : 'desktop';
      setCurLocationName(locationGuid);
      onSideMenuChange?.(folder.sourceMenuId);
    }

    const result = await getFilesByGuid({ guid: folder.id });
    setLoading(false);
    if (result) {
      updateContent(result);
    }
    // 进入文件夹
    manager.enterFolder(folder);
    setSearchItemClicked(false);
    setCurFolder(folder);

    // 如果是网格视图，更新当前网格文件夹
    if (viewMode === 'grid') {
      setCurGridFolder(folder.id);
    }
  }

  // 处理导航（前进/后退）的公共逻辑
  async function handleNav(navigationType: 'back' | 'forward') {
    setSearchItemClicked(false);
    const manager = getFolderManager();

    // 传递侧边栏菜单变更回调
    const navMethod = navigationType === 'back' ? manager.goBack : manager.goForward;
    navMethod.call(manager, (menuId) => {
      // 如果侧边栏菜单发生变化，通知父组件更新
      if (menuId !== sideMenuId) {
        setLocationBtnClicked(true);
        onSideMenuChange?.(menuId);
      }
    });

    // 更新UI状态
    const state = manager.getState();

    setCurGridFolder(state.curGridFolder);
    setCurFolder(state.curFolder);
    setExpandedFolders(state.expandedFolders);

    if (state.curFolder?.id) {
      const result = await loadFolderChildren(state.curFolder);
      if (result) {
        updateContent(result);
      }
      return;
    }
    if (state.curFolder === null && state.currentSideMenuId === sideMenuId) {
      updateContent(state);
    }
  }

  // 返回上一级 - 基于浏览历史
  async function goBack() {
    await handleNav('back');
  }

  // 前进到下一级 - 基于浏览历史
  async function goForward() {
    await handleNav('forward');
  }

  // 处理网格视图中项目的点击事件
  async function handleGridItemClick(item: FolderItem) {
    try {
      if (!item || !item.id) return;
      const folderManager = getFolderManager();
      const curFolder = folderManager.findFolderById(item.id);
      // 如果curFolder没有子级，则获取文件夹子级内容
      if (!curFolder?.children) {
        // 获取文件夹子级内容
        const result = await getFilesByGuid({ guid: item.id });
        if (result) {
          const children = [...result.folders, ...result.files];
          folderManager.updateFolderChildren(item, children);
        } else {
          folderManager.updateFolderChildren(item, []);
        }
      }
      folderManager.handleGridItemClick(item);

      // 更新UI状态
      const state = folderManager.getState();
      setCurGridFolder(state.curGridFolder);
      setCurFolder(state.curFolder);
    } catch (error) {
      console.error('处理网格项点击失败:', error);
    }
  }

  // 处理位置选择框的变更事件
  async function handleLocationChange(option: LocationOption) {
    const { value, sourceMenuId } = option as LocationOption;

    // 检查是否选择的是最近的文件夹
    if (sourceMenuId) {
      // 如果是最近的文件夹，直接进入该文件夹
      const recentFolder = recentFolders.find((folder) => folder.id === value);

      if (recentFolder) {
        // 切换到对应的侧边栏菜单
        if (sourceMenuId !== sideMenuId) {
          onSideMenuChange?.(sourceMenuId);
        }
        await enterFolder(recentFolder);
        return;
      }
    }
    setCurFolder(null);
    onSideMenuChange?.(value);
    await handleMenuSwitch(value, true);

    const manager = getFolderManager();
    manager.handleLocationChange(value, curLocationOptions);

    // 更新UI状态
    const state = manager.getState();
    setCurGridFolder(state.curGridFolder);
  }

  // 获取按钮状态
  const canGoBack = managerRef.current?.canGoBack();
  const canGoForward = managerRef.current?.canGoForward();

  // 渲染文件
  function renderFileItem(file: FileItem, level: number) {
    const paddingLeft = level * 20;

    return (
      <div key={file.id} className={css.fileTreeItem} style={{ paddingLeft }}>
        <div className={css.fileItem}>
          <img className={css.fileIcon} src={getFileIcon(file.type)} />
          <span className={css.itemName}>{file.name}</span>
        </div>
      </div>
    );
  }

  const emptyTextMap = {
    used: {
      title: fm2('FilePathPicker.emptyUsedTitle'),
      subTitle: fm2('FilePathPicker.emptyUsedSubTitle'),
    },
    shared: {
      title: fm2('FilePathPicker.emptySharedTitle'),
      subTitle: fm2('FilePathPicker.emptySharedSubTitle'),
    },
    favorites: {
      title: fm2('FilePathPicker.emptyFavoritesTitle'),
      subTitle: fm2('FilePathPicker.emptyFavoritesSubTitle'),
    },
    space: {
      title: fm2('FilePathPicker.emptySpaceTitle'),
      subTitle: fm2('FilePathPicker.emptySpaceSubTitle'),
    },
  };

  // 根据当前菜单类型返回对应的空状态文案
  function getEmptyText(menuId: string) {
    return emptyTextMap[menuId as keyof typeof emptyTextMap];
  }

  // 渲染空文件夹提示
  function renderEmptyFolder(level: number) {
    const paddingLeft = level * 20;

    return (
      <div className={css.emptyFolder} style={{ paddingLeft }}>
        <span className={css.emptyFolderText}>空空如也</span>
      </div>
    );
  }

  // 渲染统一的空状态组件
  function renderEmptyState() {
    if (curFolder?.id) {
      return renderEmptyFolder(1);
    }
    const emptyText = getEmptyText(sideMenuId);
    return (
      <div className={css.emptyList}>
        <EmptyFolder className={css.icon} />
        <div className={css.emptyTitle}>{emptyText?.title}</div>
        <div className={css.emptySubTitle}>{emptyText?.subTitle}</div>
      </div>
    );
  }

  // 递归渲染文件夹
  function renderFolderItem(folder: FolderItem, level = 0) {
    const isExpanded = expandedFolders[folder.id];
    const paddingLeft = level * 20;
    const hasChildren = folder.children && folder.children.length > 0;

    return (
      <div key={folder.id} className={css.fileTreeItem} style={{ paddingLeft }}>
        <div className={css.folderItem} onClick={() => enterFolder(folder)}>
          <span className={css.expandIcon} onClick={(e) => toggleFolder(folder, e)}>
            {isExpanded ? <ArrowDownIcon /> : <ArrowRightIcon />}
          </span>
          {folder.sourceMenuId === 'space' && !curFolder ? (
            <SpaceIcon className={css.spaceIcon} />
          ) : (
            <img className={css.folderIcon} src={folderIcon} />
          )}
          <span className={css.itemName}>{folder.name}</span>
        </div>

        {isExpanded && (
          <div className={css.subItems}>
            {hasChildren
              ? folder.children!.map((child: FolderItem | FileItem) =>
                  child.type === 'folder'
                    ? renderFolderItem(child as FolderItem, level + 1)
                    : renderFileItem(child, level + 1),
                )
              : renderEmptyFolder(level + 1)}
          </div>
        )}
      </div>
    );
  }

  // 渲染网格视图中的项目
  function renderGridItem(item: FileItem | FolderItem) {
    const isFolder = item.type === 'folder';

    return (
      <div
        key={item.id}
        className={css.gridItem}
        onClick={() => (isFolder ? handleGridItemClick(item as FolderItem) : undefined)}
      >
        {isFolder ? (
          <img className={css.folderIcon} src={folderIcon} />
        ) : (
          <img className={css.fileIcon} src={getFileIcon(item.type)} />
        )}
        <span className={css.itemName}>{item.name}</span>
      </div>
    );
  }

  // 渲染当前浏览文件夹的内容
  function renderCurFolderContent() {
    // 如果没有FolderManager实例，返回空内容
    if (!managerRef.current) return null;

    // 合并文件夹和文件为一个数组，用于虚拟列表
    const allItems = [...folders, ...files];

    if (allItems.length === 0) {
      return renderEmptyState();
    }

    return (
      <VirtualList
        data={allItems}
        height={414} // 设置虚拟列表高度
        itemHeight={42} // 每个项目的高度
        itemKey="id"
      >
        {(item: FolderItem | FileItem) => {
          if (item.type === 'folder') return renderFolderItem(item as FolderItem);

          return (
            <div key={item.id} className={css.fileTreeItem}>
              {renderFileItem(item, 0)}
            </div>
          );
        }}
      </VirtualList>
    );
  }

  // 为网格视图渲染当前文件夹内容
  function renderGridContent() {
    // 如果没有FolderManager实例，返回空内容
    if (!managerRef.current) return null;

    let allItems: (FolderItem | FileItem)[] = [];

    // 如果没有选中文件夹，显示根目录内容
    if (!curGridFolder) {
      allItems = [...folders, ...files];
    } else {
      // 找到当前选中的文件夹
      const curFolder = getFolderManager().findFolderById(curGridFolder);
      allItems = curFolder?.children || [];
    }

    // 如果文件夹为空
    if (allItems.length === 0) {
      return renderEmptyState();
    }

    // 将网格项目按行分组
    const itemsPerRow = 5; // 每行显示的项目数
    const rowHeight = 116; // 每行的高度
    const rows: (FolderItem | FileItem)[][] = [];

    // 将项目按行分组
    for (let i = 0; i < allItems.length; i += itemsPerRow) {
      rows.push(allItems.slice(i, i + itemsPerRow));
    }

    return (
      <VirtualList
        data={rows}
        height={414}
        itemHeight={rowHeight}
        itemKey={(row: (FolderItem | FileItem)[]) => `row-${row[0]?.id || 'empty'}`}
      >
        {(row: (FolderItem | FileItem)[]) => (
          <div
            key={`row-${row[0]?.id || 'empty'}`}
            style={{
              display: 'grid',
              gridTemplateColumns: `repeat(${itemsPerRow}, 1fr)`,
              gap: '16px',
              padding: '0 16px',
              height: rowHeight,
            }}
          >
            {row.map((item: FolderItem | FileItem) => renderGridItem(item))}
          </div>
        )}
      </VirtualList>
    );
  }

  function renderSelectPrefix() {
    if (curFolder?.sourceMenuId === 'space') {
      return <SpaceIcon />;
    }
    if (curLocationName === 'desktop') {
      return <DesktopIcon />;
    }
    if (curFolder) {
      return <img src={folderIcon} style={{ width: 18 }} />;
    }
    return null;
  }

  function renderSelectOption(option: LocationOption) {
    const { value, sourceMenuId } = option;
    if (sourceMenuId === 'space' || value === 'space') {
      return <SpaceIcon style={{ color: 'var(--theme-text-color-default)' }} />;
    }
    if (value === 'desktop') {
      return <DesktopIcon />;
    }
    return <img src={folderIcon} style={{ width: 18 }} />;
  }

  return (
    <div className={css.mainContent}>
      {/* 头部 */}
      <div className={css.header}>
        {/* 位置选择 */}
        <div className={css.locationBreadcrumb}>
          {/* 后退按钮 */}
          <Button className={css.backBtn} disabled={!canGoBack} icon={<ArrowLeftIcon />} type="text" onClick={goBack} />
          {/* 前进按钮 */}
          <Button
            className={css.forwardBtn}
            disabled={!canGoForward}
            icon={<ArrowRightIcon />}
            type="text"
            onClick={goForward}
          />
        </div>
        {/* 文件名 */}
        <div className={css.fileInfo}>
          {type === 'create' && (
            <div className={css.fileName}>
              <span className={css.fileNameText}>{fm2('FilePathPicker.fileName')}</span>
              <Input disabled value={filename} />
            </div>
          )}
          {/* 文件操作 */}
          <div className={css.fileNav}>
            <span className={css.fileNavText}>
              {type === 'create' ? fm2('FilePathPicker.createIn') : fm2('FilePathPicker.moveTo')}
            </span>
            <Select
              choiceTransitionName="" // 移除选项选择的过渡动画
              optionRender={(option) => {
                const { value, label } = option.data;
                return (
                  <div
                    style={{ display: 'flex', alignItems: 'center' }}
                    onClick={() => handleLocationChange(option.data)}
                  >
                    {renderSelectOption(option.data)}
                    <span style={{ marginLeft: 8 }}>{label}</span>
                    {((value === sideMenuId && !curFolder) || value === curFolder?.id) && (
                      <CheckOutlined style={{ color: 'var(--theme-basic-color-primary)', marginLeft: 'auto' }} />
                    )}
                  </div>
                );
              }}
              options={curLocationOptions}
              placeholder={fm2('FilePathPicker.selectPlaceholder')}
              popupClassName="locationSelectDropdown"
              prefix={renderSelectPrefix()}
              suffixIcon={<SolidArrowDownIcon />}
              transitionName="" // 移除下拉框打开/关闭的过渡动画，实现立即关闭
              value={curLocationName === 'space' ? undefined : curLocationName || undefined}
            />
          </div>
        </div>
        {/* 视图选择 暂时注释，后续再放开 */}
        <div className={css.viewControls}>
          {/* <Radio.Group
            value={viewMode}
            onChange={(e) => handleViewModeChange(e.target.value as ViewMode)}
            buttonStyle="solid"
          >
            <Radio.Button value="list">
              <UnorderedListOutlined />
            </Radio.Button>
            <Radio.Button value="grid">
              <AppstoreOutlined />
            </Radio.Button>
          </Radio.Group> */}
          <Search onItemClick={searchItemClick} />
        </div>
      </div>

      {/* 文件列表 */}
      <div className={css.fileList}>
        <Spin spinning={loading}>
          {viewMode === 'list' ? (
            <div className={css.fileTree}>{renderCurFolderContent()}</div>
          ) : (
            <div className={css.gridView}>{renderGridContent()}</div>
          )}
        </Spin>
      </div>

      <Footer
        createFolder={createFolder}
        curFolder={curFolder}
        curLocationName={curLocationName}
        isAdmin={isAdmin}
        role={role}
        sideMenuId={sideMenuId}
        type={type}
        onCancel={onCancel}
        onOk={onOk}
      />
    </div>
  );
}
