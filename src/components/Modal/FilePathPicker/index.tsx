import { message, Modal } from 'antd';
import { useState } from 'react';

import * as fileApi from '@/api/File';
import { fm2 } from '@/modules/Locale';
import to from '@/utils/await-to';

import { Content } from './Content';
import { SideMenu } from './SideMenu';
import css from './style.less';

export interface Options {
  filename?: string;
  locationGuid?: string;
  title?: string;
  onOk?: () => void;
  onCancel?: () => void;
  type?: 'create' | 'move';
  isAdmin?: boolean;
  role?: string;
  fileGuid: string;
}

export const FilePathPicker = (options: Options) => {
  const { fileGuid, filename, locationGuid = 'desktop', onOk, type = 'create', isAdmin, role } = options;
  const _filename = `${filename} ${fm2('FilePathPicker.copy')}`;

  let modalInstance: any;

  function ModalContent() {
    const [curLocationGuid, setCurLocationGuid] = useState(locationGuid);
    const [menuId, setMenuId] = useState<string>('desktop');

    // 创建副本
    async function createDuplicate(locationGuid: string, folderName: string, name: string) {
      const params = {
        files: [{ guid: fileGuid, name }],
        folder: locationGuid,
      };
      const [, res] = await to(fileApi.duplicate(fileGuid, params));
      if (res?.status !== 200) return message.error(res?.data?.msg);

      message.success(fm2('FilePathPicker.createSuccess', { folderName }));
      onOk?.();
    }

    // 创建副本
    async function moveFile(locationGuid: string, folderName: string) {
      const params = {
        entries: [
          {
            to: locationGuid,
            fileGuid: fileGuid,
          },
        ],
      };
      const [, res] = await to(fileApi.move(params));
      if (res?.status !== 204) return message.error(res?.data?.msg);

      message.success(fm2('FilePathPicker.moveSuccess', { folderName }));
      onOk?.();
    }

    return (
      <div className={css.modalContent}>
        <SideMenu curMenuId={menuId} type={type} onSideMenuChange={setMenuId} />
        <Content
          filename={_filename}
          isAdmin={isAdmin}
          locationGuid={locationGuid}
          role={role}
          sideMenuId={menuId}
          type={type}
          onCancel={() => {
            modalInstance.destroy();
          }}
          onLocationChange={(locationGuid) => {
            setCurLocationGuid(locationGuid);
          }}
          onOk={(folderName) => {
            const _locationGuid = curLocationGuid === 'desktop' ? 'Desktop' : curLocationGuid;
            const _folderName = curLocationGuid === 'desktop' ? fm2('FilePathPicker.desktop') : folderName;
            if (type === 'create') {
              createDuplicate(_locationGuid, _folderName, _filename as string);
            } else {
              moveFile(_locationGuid, _folderName);
            }
            modalInstance.destroy();
          }}
          onSideMenuChange={setMenuId}
        />
      </div>
    );
  }

  modalInstance = Modal.info({
    width: 893,
    icon: null,
    footer: null,
    centered: true,
    closable: false,
    className: css.filePathPicker,
    content: <ModalContent />,
  });
};
