import { But<PERSON>, Modal, Tooltip } from 'antd';
import { useMemo } from 'react';

import { ReactComponent as AddFolderIcon } from '@/assets/images/svg/addFolder-18.svg';
import { CreateFolder } from '@/components/Modal/CreateFolder';
import { fm2 } from '@/modules/Locale';

import type { FolderItem } from './FolderManager';
import css from './style.less';

interface FooterProps {
  role?: string;
  curFolder?: FolderItem | null;
  isAdmin?: boolean;
  type: 'create' | 'move';
  sideMenuId: string;
  curLocationName: string;
  onCancel: () => void;
  onOk: (locationGuid: string) => void;
  createFolder: (name: string, guid: string) => void;
}
export function Footer(props: FooterProps) {
  const { role, curFolder, isAdmin, type, sideMenuId, curLocationName, onCancel, onOk, createFolder } = props;

  function checkCreateAuth() {
    let tip = '';
    let auth = true;
    // // 有操作权限
    if (role === 'editor') {
      if (curFolder) {
        if (curFolder.role !== 'editor') {
          tip = fm2('FilePathPicker.noPermissionTip', { type: fm2('FilePathPicker.folders') });
          auth = false;
        }
      }
    } else {
      // 没有操作权限
      tip = '';
      auth = false;
    }
    return { tip, auth };
  }

  function checkMoveAuth() {
    let tip = '';
    let auth = true;
    // 管理员
    if (isAdmin) {
      if (curFolder) {
        // 当前位置不可编辑
        if (curFolder.role !== 'editor') {
          tip = fm2('FilePathPicker.noMoveToTargetLocationTip');
          auth = false;
        }
      }
    }
    // 普通协作者
    else if (role !== 'none') {
      // 有操作权限
      if (role === 'editor') {
        if (curFolder) {
          // 当前位置可编辑
          if (curFolder.role === 'editor') {
            tip = fm2('FilePathPicker.noMoveFilePermissionTip');
            auth = false;
          } else {
            tip = fm2('FilePathPicker.noMoveToTargetLocationTip');
            auth = false;
          }
        }
      } else {
        // 没有操作权限
        tip = '';
        auth = false;
      }
    }
    return { tip, auth };
  }

  // 新建文件夹按钮提示
  const createBtnDisableTip = useMemo(() => {
    if (sideMenuId === 'space' && !curFolder) return fm2('FilePathPicker.noSupportTip');
    if (curLocationName === 'desktop' && !curFolder) return '';
    if (type === 'move') {
      const { auth } = checkMoveAuth();
      if (!auth) return fm2('FilePathPicker.noPermissionTip', { type: fm2('FilePathPicker.folders') });
    } else if (type === 'create') {
      const { auth } = checkCreateAuth();
      if (!auth) return fm2('FilePathPicker.noPermissionTip', { type: fm2('FilePathPicker.folders') });
    }
    return '';
  }, [curFolder, curLocationName, sideMenuId, type]);

  const okBtnDisableTip = useMemo(() => {
    // 移动按钮不提示
    if (type === 'move') return '';
    // 以下为创建副本按钮的提示语
    if (sideMenuId === 'space' && !curFolder) return '';
    if (curLocationName === 'desktop' && !curFolder) return '';
    if (role === 'none') return '';
    if (curFolder?.role !== 'editor') {
      return fm2('FilePathPicker.noPermissionTip', { type: fm2('FilePathPicker.files') });
    }
    return '';
  }, [curFolder, curLocationName, sideMenuId, role, type]);

  // 确认按钮是否禁用
  const okBtnDisable = useMemo(() => {
    if (sideMenuId === 'space' && !curFolder) return true;
    if (!!createBtnDisableTip && type === 'create') return true;
    if (type === 'move') {
      // 普通协作者且没有操作权限
      if (role !== 'none' && role !== 'editor') return true;
    }

    return false;
  }, [sideMenuId, curFolder, createBtnDisableTip, type, role]);

  // 打开创建文件夹弹窗
  function openCreateFolder() {
    const placeholder = fm2('FilePathPicker.createPlaceHolder', {
      folderName: curFolder?.name || fm2('FilePathPicker.desktop'),
    });
    CreateFolder({
      placeholder,
      onOk: (name) => {
        // 桌面参数需要大写
        createFolder(name, curFolder?.id || 'Desktop');
      },
    });
  }
  function handleOk() {
    const { tip, auth } = checkMoveAuth();
    if (tip) {
      Modal.error({ title: fm2('FilePathPicker.moveFailed'), content: tip });
      return;
    }
    if (auth) onOk(curLocationName);
  }

  return (
    <div className={css.modalActions}>
      {/* 鼠标悬停提示 */}
      <Tooltip placement="top" title={createBtnDisableTip}>
        {/* 创建文件夹按钮 */}
        <Button
          className={css.createFolderBtn}
          disabled={!!createBtnDisableTip}
          icon={<AddFolderIcon />}
          type="text"
          onClick={openCreateFolder}
        >
          {fm2('FilePathPicker.createFolder')}
        </Button>
      </Tooltip>
      <Tooltip placement="top" title={okBtnDisableTip}>
        <Button
          className={css.confirmBtn}
          color="default"
          disabled={okBtnDisable}
          variant="solid"
          onClick={() => handleOk()}
        >
          {type === 'create' ? fm2('FilePathPicker.createCopy') : fm2('FilePathPicker.move')}
        </Button>
      </Tooltip>
      <Button onClick={onCancel}>{fm2('FilePathPicker.cancel')}</Button>
    </div>
  );
}
