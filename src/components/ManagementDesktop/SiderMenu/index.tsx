import type { MenuProps } from 'antd';
import { Divider, Menu, Tooltip } from 'antd';
import Sider from 'antd/es/layout/Sider';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { history, useLocation } from 'umi';

import { ReactComponent as CompanyIconSVG } from '@/assets/images/management/sidebar/company-icon.svg';
import { ReactComponent as JumpIcon } from '@/assets/images/management/sidebar/jump-icon.svg';
import { ReactComponent as SilderLfSvg } from '@/assets/images/sidepanel/silderLf.svg';
import useDebounce from '@/hooks/useDebounce';
import useMenus from '@/hooks/useMenus';
import type { MenuItem } from '@/model/Menus';
import { MenuKey } from '@/model/Menus';
import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

import styles from './index.less';

const getFirstPath = (pathname: string) => {
  const parts = pathname.split('/');
  if (parts.length > 1) {
    const result = parts[1];
    return result;
  } else {
    return '';
  }
};
const SiderMenu: React.FC = () => {
  //国际化
  const siderMenuBusinessText = fm('Management.backDesktop');

  const { menus, setSelectedMenuItem, findMenuItemByKey } = useMenus();
  const me = useMeStore((state) => state.me);
  const [collapsed, setCollapsed] = useState(false);

  const silderRef = useRef<HTMLDivElement>(null);
  const [showButton, setShowButton] = useState(false);
  const corporateName = me?.team?.name || '';

  // 鼠标移动事件处理函数
  const handleMouseMove = (event: MouseEvent) => {
    const topHeaderHeight: number = 48;
    silderRef.current?.style.setProperty('--silder--y', `${event.clientY - topHeaderHeight}px`);
  };

  /**
   * 控制收缩侧边栏的按钮
   */
  let timerId: NodeJS.Timeout | null = null;
  const handleMouseenter = () => {
    timerId = setTimeout(() => {
      setShowButton(true);
    }, 500);
  };
  const handleMouseout = () => {
    if (timerId) {
      clearTimeout(timerId);
    }
    setShowButton(false);
  };

  // 使用 useDebounce 对 handleMouseenter 进行防抖处理，延迟时间为50 毫秒
  const debouncedMouseenter = useDebounce(handleMouseenter, 50);
  const debouncedMouseout = useDebounce(handleMouseout, 50);
  useEffect(() => {
    const element = silderRef.current;
    if (element) {
      // 为 ref 对应的 DOM 元素添加鼠标移动监听事件
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseenter', debouncedMouseenter);
      element.addEventListener('mouseleave', debouncedMouseout);
    }
    return () => {
      if (element) {
        // 组件卸载时，移除鼠标移动监听事件，避免内存泄漏
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseenter', debouncedMouseenter);
        element.removeEventListener('mouseleave', debouncedMouseout);
      }
    };
  }, [debouncedMouseout, debouncedMouseenter]);

  // 菜单改变事件
  const setMenuItem = useCallback(
    (key: string) => {
      const currentMenus = findMenuItemByKey(key);
      setSelectedMenuItem((currentMenus as Partial<MenuItem>) ?? []);
    },
    [findMenuItemByKey, setSelectedMenuItem],
  );
  // 初始化获取当前地址
  const location = useLocation();
  const pathName = location.pathname;
  const selectedKeys = getFirstPath(pathName);
  useEffect(() => {
    setMenuItem(selectedKeys);
  }, [setMenuItem, selectedKeys]);

  //侧边栏点击事件
  const onClick: MenuProps['onClick'] = (e) => {
    setMenuItem(e.key);
    history.push(`/${e.key}` || '/');
  };

  const goBack = (url: string = '/desktop') => {
    window.location.href = `${window.location.origin}${url}`;
  };
  return (
    <Sider className={styles.siderManagementStyle} width={collapsed ? 83 : 240}>
      <div className={styles.menus}>
        <Menu
          defaultOpenKeys={[MenuKey.settings]}
          inlineCollapsed={collapsed}
          inlineIndent={16}
          items={menus}
          mode="inline"
          selectedKeys={[selectedKeys]}
          onClick={onClick}
        />
      </div>
      <div className={styles.footer}>
        <div className={styles.gmName}>
          {collapsed ? (
            <Tooltip placement="right" title={corporateName}>
              <CompanyIconSVG />
            </Tooltip>
          ) : (
            corporateName
          )}
        </div>
        <Divider />
        {collapsed ? (
          <div className={styles.footerDiv}>
            <Tooltip placement="right" title={siderMenuBusinessText}>
              <div className={styles.businessOnlyIcon}>
                <JumpIcon />
              </div>
            </Tooltip>
          </div>
        ) : (
          <div className={styles.footerDiv}>
            <div className={styles.business} onClick={() => goBack()}>
              <JumpIcon />
              <span className={styles.title}>{siderMenuBusinessText}</span>
            </div>
          </div>
        )}
      </div>
      <div ref={silderRef} className={styles.rightSilder} />
      {showButton && silderRef.current
        ? ReactDOM.createPortal(
            <button
              className={`${styles.silderButton}  ${collapsed ? styles.rightSvg : ''}`}
              type="button"
              onClick={() => setCollapsed(!collapsed)}
            >
              <SilderLfSvg />
            </button>,
            silderRef.current,
          )
        : null}
    </Sider>
  );
};
export const SideBar = React.memo(SiderMenu);
