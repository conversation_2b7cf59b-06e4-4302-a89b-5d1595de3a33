.managementHeaderContent {
  display: flex;
  box-shadow:
    rgba(0, 0, 0, 6%) 0 1px 1px 0,
    rgba(0, 0, 0, 10%) 0 2px 4px 0;
  z-index: 2;
  padding: 0 20px;
  align-items: center;
  justify-content: space-between;

  .headerName {
    width: 88px;
    height: 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--theme-text-color-default);
    font-size: 20px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: left center;
    cursor: pointer;

    &:hover {
      color: unset;
    }
  }

  .headerSubName {
    font-size: 13px;
    font-style: normal;
    font-weight: 500;
  }
}
