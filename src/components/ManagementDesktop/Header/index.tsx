import { Button, Space } from 'antd';
import { Header as LeftHeader } from 'antd/es/layout/layout';
import { useContext } from 'react';
import { history } from 'umi';

import { ReactComponent as DownIconSVG } from '@/assets/header/down-icon.svg';
import logoSrc from '@/assets/images/management/header/sm_logo.svg';
import { AdminModeContext, AdminModeDispatchContext } from '@/contexts/AdminMode';
import { PreferencesContext } from '@/contexts/preferences';
import { fm } from '@/modules/Locale';

import styles from './index.less';
export const Header = () => {
  const state = useContext(PreferencesContext);
  const { authSuccess } = useContext(AdminModeContext);
  const { quitAdmin } = useContext(AdminModeDispatchContext);
  const toMain = () => {
    history.push(`${location.origin}/${state?.homePage || 'recent'}`);
  };

  const handleQuitButton = async () => {
    try {
      await quitAdmin?.();
      location.reload();
    } catch (error) {
      console.error('handleQuitButton', error);
    }
  };
  return (
    <LeftHeader className={styles.managementHeaderContent}>
      <Space align="center" size={20}>
        {!authSuccess && logoSrc ? (
          <div className={styles.headerName} style={{ backgroundImage: `url(${logoSrc})` }} onClick={toMain} />
        ) : null}
        {authSuccess && (
          <Button icon={<DownIconSVG />} onClick={handleQuitButton}>
            {fm('Management.exitAdminMode')}
          </Button>
        )}
      </Space>
    </LeftHeader>
  );
};
