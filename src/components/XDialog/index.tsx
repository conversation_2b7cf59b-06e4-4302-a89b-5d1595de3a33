import { Modal } from 'antd';
import { omit } from 'lodash';
import React, { memo } from 'react';

import type { CustomModalProps } from '@/model/Dialog';
// 使用方式
// 全局使用见 contexts 文件夹中的XDialog
// 局部使用方式：跟平常组件使用一样，可以直接调用，最好结合hooks中的XDialog的hook 文件统一使用方式，变量名

// 自定义比较函数
function arePropsEqual(prevProps: CustomModalProps, nextProps: CustomModalProps) {
  // 比较基本属性
  const basicPropsEqual =
    prevProps.open === nextProps.open &&
    prevProps.width === nextProps.width &&
    prevProps.height === nextProps.height &&
    prevProps.title === nextProps.title &&
    prevProps.footer === nextProps.footer &&
    prevProps.onOk === nextProps.onOk &&
    prevProps.onCancel === nextProps.onCancel;

  if (!basicPropsEqual) return false;

  if (prevProps.children === nextProps.children) return true;

  if (typeof prevProps.children === 'string' || typeof prevProps.children === 'number') {
    return prevProps.children === nextProps.children;
  }

  const prevKeys = Object.keys(prevProps);
  const nextKeys = Object.keys(nextProps);

  if (prevKeys.length !== nextKeys.length) return false;

  return prevKeys.every((key) => {
    if (key === 'children') return true; // children 已经比较过了
    return prevProps[key as keyof CustomModalProps] === nextProps[key as keyof CustomModalProps];
  });
}

const CustomModal: React.FC<CustomModalProps> = memo((props) => {
  const { children, width = 420, modalStyles, className = 'x-dialog' } = props;
  const modalProps = omit(props, ['children', 'openDialog', 'width', 'modalStyles']);
  return (
    <Modal
      {...modalProps}
      centered
      destroyOnClose
      className={className}
      maskClosable={false}
      styles={modalStyles}
      width={width}
    >
      {children}
    </Modal>
  );
}, arePropsEqual);

CustomModal.displayName = 'CustomModal';

export default CustomModal;
