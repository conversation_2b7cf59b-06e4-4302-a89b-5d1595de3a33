/**
 * 登录表单功能，为登录页面或登录弹框提供登录的业务功能
 */
import { PlusOutlined } from '@ant-design/icons';
import type { MenuProps } from 'antd';
import { Button, Divider, Menu, message, Popover, Tooltip } from 'antd';
import Sider from 'antd/es/layout/Sider';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import ReactDOM from 'react-dom';
import { history, useLocation, useParams } from 'umi';

import type { ResponseData } from '@/api/Create';
import * as createApi from '@/api/Create';
import docSrc from '@/assets/images/fileMenu/<EMAIL>';
import folderSrc from '@/assets/images/fileMenu/<EMAIL>';
import form2Src from '@/assets/images/fileMenu/<EMAIL>';
import formSrc from '@/assets/images/fileMenu/<EMAIL>';
import modocSrc from '@/assets/images/fileMenu/<EMAIL>';
import moTablesSrc from '@/assets/images/fileMenu/<EMAIL>';
import pptSrc from '@/assets/images/fileMenu/<EMAIL>';
import spaceSrc from '@/assets/images/fileMenu/<EMAIL>';
import tableSrc from '@/assets/images/fileMenu/<EMAIL>';
import tableFormSrc from '@/assets/images/fileMenu/<EMAIL>';
import testFormSrc from '@/assets/images/fileMenu/<EMAIL>';
import uploadFileSrc from '@/assets/images/fileMenu/<EMAIL>';
import { ReactComponent as DesktopSvg } from '@/assets/images/sidepanel/desktop.svg';
import { ReactComponent as FavoritesSvg } from '@/assets/images/sidepanel/favorites.svg';
import { ReactComponent as RecentSvg } from '@/assets/images/sidepanel/recent.svg';
import { ReactComponent as ShareSvg } from '@/assets/images/sidepanel/share.svg';
import { ReactComponent as SilderLfSvg } from '@/assets/images/sidepanel/silderLf.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as TrashSvg } from '@/assets/images/sidepanel/trash.svg';
import { ReactComponent as UnionSvg } from '@/assets/images/sidepanel/Union.svg';
import { CreateWithNamePop } from '@/components/CreateWithNamePop';
import type { fileItem } from '@/components/Desktop/CreateFileMenu/index';
import CreateFileMenu from '@/components/Desktop/CreateFileMenu/index';
import { TemplateModal } from '@/components/TemplateModal';
import useDebounce from '@/hooks/useDebounce';
import type { SiderMenuItem } from '@/model/SiderMenuItem';
import { fm } from '@/modules/Locale';
import { useSelectedMenuItemStore } from '@/store/Sider';

import styles from './index.less';
export type MenuItem = Required<MenuProps>['items'][number];
const getFirstPath = (pathname: string) => {
  const parts = pathname.split('/');
  if (parts.length > 1) {
    const result = parts[1];
    if (['folder', 'desktop'].includes(result)) return 'desktop';
    return result;
  } else {
    return '';
  }
};
const newFiles: string[] = ['newdoc', 'modoc', 'mosheet', 'table', 'presentation', 'form'];
const formTypeFiles: string[] = ['table-form', 'test-form'];
const newFolders: string[] = ['folder', 'space'];
function iskeyandlabel(value: MenuItem): value is SiderMenuItem {
  return (value as SiderMenuItem).label !== undefined && (value as SiderMenuItem).key !== undefined;
}

const SiderMenu: React.FC = () => {
  //国际化
  const siderMenuCreactText = fm('SiderMenu.siderMenuCreactText');
  const siderMenuRecentText = fm('SiderMenu.siderMenuRecentText');
  const siderMenuShareText = fm('SiderMenu.siderMenuShareText');
  const siderMenuFavoritesText = fm('SiderMenu.siderMenuFavoritesText');
  const siderMenuDesktopText = fm('SiderMenu.siderMenuDesktopText');
  const siderMenuSpaceText = fm('SiderMenu.siderMenuSpaceText');
  const siderMenuTrashText = fm('SiderMenu.siderMenuTrashText');
  const siderMenuBusinessText = fm('SiderMenu.siderMenuBusinessText');

  const siderMenuCreateDocText = fm('SiderMenu.siderMenuCreateDocText');
  const siderMenuCreateMoDocText = fm('SiderMenu.siderMenuCreateMoDocText');
  const siderMenuCreateTableText = fm('SiderMenu.siderMenuCreateTableText');
  const siderMenuCreateMoTableText = fm('SiderMenu.siderMenuCreateMoTableText');
  const siderMenuCreatePptText = fm('SiderMenu.siderMenuCreatePptText');
  const siderMenuCreateFormText = fm('SiderMenu.siderMenuCreateFormText');
  const siderMenuCreateOrdinaryFormText = fm('SiderMenu.siderMenuCreateOrdinaryFormText');
  const siderMenuCreateTableFormText = fm('SiderMenu.siderMenuCreateTableFormText');
  const siderMenuCreateTestFormText = fm('SiderMenu.siderMenuCreateTestFormText');
  const siderMenuCreateFolderText = fm('SiderMenu.siderMenuCreateFolderText');
  const siderMenuCreateSpaceText = fm('SiderMenu.siderMenuCreateSpaceText');
  const siderMenuCreateUploadFileText = fm('SiderMenu.siderMenuCreateUploadFileText');

  // 获取当前的Guid
  const params = useParams<{ guid: string }>();
  const guid = params?.guid || 'Desktop';

  const items: MenuItem[] = [
    {
      key: 'recent',
      icon: <RecentSvg />,
      label: siderMenuRecentText,
    },
    {
      key: 'share',
      icon: <ShareSvg />,
      label: siderMenuShareText,
    },
    {
      key: 'favorites',
      icon: <FavoritesSvg />,
      label: siderMenuFavoritesText,
    },
    {
      type: 'divider',
    },
    { key: 'desktop', icon: <DesktopSvg />, label: siderMenuDesktopText },
    { key: 'space', icon: <SpaceSvg />, label: siderMenuSpaceText },
    {
      type: 'divider',
    },

    { key: 'trash', icon: <TrashSvg />, label: siderMenuTrashText },
  ];
  const fileList: fileItem[][] = [
    [
      {
        src: docSrc,
        title: siderMenuCreateDocText,
        value: 'newdoc',
      },
      {
        src: modocSrc,
        title: siderMenuCreateMoDocText,
        value: 'modoc',
      },
      {
        src: tableSrc,
        title: siderMenuCreateTableText,
        value: 'mosheet',
      },
      {
        src: moTablesSrc,
        title: siderMenuCreateMoTableText,
        value: 'table',
      },
      {
        src: pptSrc,
        title: siderMenuCreatePptText,
        value: 'presentation',
      },
      {
        src: formSrc,
        title: siderMenuCreateFormText,
        value: 'form',
        children: [
          {
            src: form2Src,
            title: siderMenuCreateOrdinaryFormText,
            value: 'form',
          },
          {
            src: tableFormSrc,
            title: siderMenuCreateTableFormText,
            value: 'table-form',
          },
          {
            src: testFormSrc,
            title: siderMenuCreateTestFormText,
            value: 'test-form',
          },
        ],
      },
    ],
    [
      {
        src: folderSrc,
        title: siderMenuCreateFolderText,
        value: 'folder',
      },
      {
        src: spaceSrc,
        title: siderMenuCreateSpaceText,
        value: 'space',
      },
      {
        src: uploadFileSrc,
        title: siderMenuCreateUploadFileText,
        value: 'upload-file',
      },
    ],
  ];
  const [collapsed, setCollapsed] = useState(false);

  const sideMenuDataList: SiderMenuItem[] = items.filter(iskeyandlabel);
  const [isOpenTypePop, setIsOpenTypePop] = useState(false);
  const [isOpenTemplateModel, setIsOpenTemplateModel] = useState(false);
  const [isOpenCreatePop, setIsOpenCreatePop] = useState(false);
  const [selectCreateType, setSelectCreateType] = useState('folder');
  const [confirmLoading, setConfirmLoading] = useState(false);

  // 获取当前地址
  const location = useLocation();
  const pathName = location.pathname;
  const selectedKeys = getFirstPath(pathName);
  const setSelectedMenuItem = useSelectedMenuItemStore((state) => state.setSelectedMenuItem);
  const silderRef = useRef<HTMLDivElement>(null);
  const [showButton, setShowButton] = useState(false);

  const createFile = (fileType: string) => {
    setSelectCreateType(fileType);
    if (newFiles.includes(fileType)) {
      const parentGuid = guid;
      window.open(`/api/v1/files/create/${fileType}?parentGuid=${parentGuid}`, '_blank');
    } else if (formTypeFiles.includes(fileType)) {
      const parentGuid = guid;
      const formType = fileType === 'table-form' ? 'table' : 'quiz';
      window.open(`/api/v1/files/create/form?parentGuid=${parentGuid}&formType=${formType}`, '_blank');
    } else if (newFolders.includes(fileType)) {
      setIsOpenCreatePop(true);
    }
  };

  const showTemplateModel = () => {
    setIsOpenTypePop(false);
    setIsOpenTemplateModel(true);
  };

  const content = (
    <CreateFileMenu
      fileMenuList={fileList}
      showTemplateModel={showTemplateModel}
      onClose={(item: fileItem) => {
        if (item?.disabled || item?.children?.length) {
          return;
        }
        setIsOpenTypePop(false);
        //开始创建文件
        createFile(item?.value || 'newdoc');
      }}
    />
  );

  setSelectedMenuItem(
    sideMenuDataList.find((item) => item.key === selectedKeys) || {
      key: 'recent',
      label: siderMenuRecentText,
    },
  );

  // 鼠标移动事件处理函数
  const handleMouseMove = (event: MouseEvent) => {
    const topHeaderHeight: number = 48;
    silderRef.current?.style.setProperty('--silder--y', `${event.clientY - topHeaderHeight}px`);
  };

  /**
   * 控制收缩侧边栏的按钮
   */
  let timerId: any = null;
  const handleMouseenter = () => {
    timerId = setTimeout(() => {
      setShowButton(true);
    }, 500);
  };
  const handleMouseout = () => {
    if (timerId) {
      clearTimeout(timerId);
    }
    setShowButton(false);
  };

  // 使用 useDebounce 对 handleMouseenter 进行防抖处理，延迟时间为50 毫秒
  const debouncedMouseenter = useDebounce(handleMouseenter, 50);
  const debouncedMouseout = useDebounce(handleMouseout, 50);
  useEffect(() => {
    const element = silderRef.current;
    if (element) {
      // 为 ref 对应的 DOM 元素添加鼠标移动监听事件
      element.addEventListener('mousemove', handleMouseMove);
      element.addEventListener('mouseenter', debouncedMouseenter);
      element.addEventListener('mouseleave', debouncedMouseout);
    }
    return () => {
      if (element) {
        // 组件卸载时，移除鼠标移动监听事件，避免内存泄漏
        element.removeEventListener('mousemove', handleMouseMove);
        element.removeEventListener('mouseenter', debouncedMouseenter);
        element.removeEventListener('mouseleave', debouncedMouseout);
      }
    };
    //请求接口
  }, []);
  //侧边栏点击事件
  const onClick: MenuProps['onClick'] = (e: MenuItem) => {
    const path: any = e?.key;
    history.push(`/${path}` || '/');
  };

  const handleOpenChange = (newOpen: boolean) => {
    setIsOpenTypePop(newOpen);
  };

  const createWithNamehandleCancel = () => {
    setIsOpenCreatePop(false);
  };
  const goManagement = () => {
    window.open('/enterprise/audit');
  };

  const createWithNamehandleOk = useCallback(
    (name: string) => {
      setConfirmLoading(true);
      if (selectCreateType === 'folder') {
        //创建文件夹
        createApi
          .createFolder(name, guid)
          .then((res: ResponseData) => {
            if (res.status === 200) {
              setConfirmLoading(false);
              window.open(`/folder/${res.data.guid}`, '_self');
              createWithNamehandleCancel();
            } else {
              message.error(res.statusText);
            }
          })
          .catch((err: any) => {
            message.error(err);
          });
      } else {
        // 创建团队空间
        createApi
          .createSpace(name)
          .then((res: ResponseData) => {
            if (res.status === 200) {
              setConfirmLoading(false);
              window.open(`/space/${res.data.guid}`, '_self');
              createWithNamehandleCancel();
            } else {
              message.error(res.statusText);
            }
          })
          .catch((err) => {
            message.error(err);
          });
      }
    },
    [selectCreateType, guid],
  );

  const handleCloseTemplate = () => {
    setIsOpenTemplateModel(false);
  };

  return (
    <Sider className={styles.siderStyle} width={collapsed ? 83 : 240}>
      <div className={styles.creatDiv}>
        <Popover
          arrow={false}
          content={content}
          open={isOpenTypePop}
          placement="bottomLeft"
          trigger="click"
          onOpenChange={handleOpenChange}
        >
          <Button className={styles.createBtn} type="primary" onClick={() => setIsOpenTypePop(!isOpenTypePop)}>
            {collapsed ? <PlusOutlined className={styles.plusIcon} /> : siderMenuCreactText}
          </Button>
        </Popover>
      </div>
      <div className={styles.menus}>
        <Menu
          defaultOpenKeys={['dashboard']}
          inlineCollapsed={collapsed}
          items={items}
          mode="inline"
          selectedKeys={[selectedKeys]}
          onClick={onClick}
        />
      </div>
      <div className={styles.footer}>
        <Divider />
        {collapsed ? (
          <div className={styles.footerDiv}>
            <Tooltip placement="right" title={siderMenuBusinessText}>
              <div className={styles.businessOnlyIcon}>
                <UnionSvg />
              </div>
            </Tooltip>
          </div>
        ) : (
          <div className={styles.footerDiv}>
            <div className={styles.business} onClick={goManagement}>
              <UnionSvg />
              <span className={styles.title}>{siderMenuBusinessText}</span>
            </div>
          </div>
        )}
      </div>
      <div ref={silderRef} className={styles.rightSilder} />
      {showButton && silderRef.current
        ? ReactDOM.createPortal(
            <button
              className={`${styles.silderButton}  ${collapsed ? styles.rightSvg : ''}`}
              type="button"
              onClick={() => setCollapsed(!collapsed)}
            >
              <SilderLfSvg />
            </button>,
            silderRef.current,
          )
        : null}
      <CreateWithNamePop
        confirmLoading={confirmLoading}
        type={selectCreateType}
        visible={isOpenCreatePop}
        onCancel={createWithNamehandleCancel}
        onOk={createWithNamehandleOk}
      />
      {isOpenTemplateModel && <TemplateModal closeTemplate={handleCloseTemplate} visible={isOpenTemplateModel} />}
    </Sider>
  );
};
export default React.memo(SiderMenu);
