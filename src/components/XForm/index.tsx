import { Form } from 'antd';
import React, { memo } from 'react';

import { useXForm } from '@/hooks/useXForm';
import type { XFormProps } from '@/model/XForm';

// 表单组件使用方式, 参数同 Antd Form 组件
// <XForm />
const XForm: React.FC<XFormProps> = memo((props) => {
  const { formItems, handleReset, handleFinish, formInstance, getLayoutProps } = useXForm(props);

  const { initialValues } = props;
  return (
    <Form
      form={formInstance}
      {...getLayoutProps}
      initialValues={initialValues}
      preserve={false}
      onFinish={handleFinish}
      onReset={handleReset}
    >
      {formItems}
    </Form>
  );
});

XForm.displayName = 'XForm';

export default XForm;
