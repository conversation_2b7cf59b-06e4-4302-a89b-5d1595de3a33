import './style.css';

import type { DeviceMode } from 'shimo-js-sdk-shared';
import { ContainerMethod, FileType, ReadyState } from 'shimo-js-sdk-shared';

import { getBrandConfig } from '@/utils/Brand';
import { handleSaveStatus, handleSyncStatus } from '@/utils/controllers/collaboration';
import { renderEditorBanner } from '@/utils/EditorBanner/renderEditorBanner';
import { getBoolean, getString } from '@/utils/env';
import { renderUserCard } from '@/utils/RenderUserCard';
import type { EventCallback, ShimoSDK } from '@/utils/ShimoSDK';
import { fetchContent } from '@/utils/ShimoSDK';
import { createSaveStatus, createSyncStatus } from '@/utils/ShimoSDK/collaboration';
import type { DocxEditor } from '@/utils/ShimoSDK/types';

import { createCheckPermission } from '../../CheckPermission';
import { ShimoFileType } from '../../CheckPermission/scripts';
import { setSocket } from '../utils/socketManager';

const loadSdk = async () => {
  return new Promise<any>(function handler(resolve, reject) {
    if (window.SDKDocx) {
      resolve(window.SDKDocx);
    } else {
      setTimeout(() => {
        handler(resolve, reject);
      }, 50);
    }
  });
};

export async function initializerDocx() {
  let sdk: ShimoSDK | undefined;

  // isSMPicBrowser 传统文档在截图模式下不显示目录，判断是否为截图浏览器产生的请求
  function isSMPicBrowser() {
    return !!/(shimo-backend-pic)/i.test(navigator.userAgent);
  }

  await (async () => {
    const elm = document.createElement('div');
    elm.style.height = '100%';
    const rootElm = document.querySelector('#editor-content')!;
    rootElm.appendChild(elm);

    const { file, user } = window;

    const deviceMode = getString('DEVICE_MODE');
    file.permissions.isTocEnable = !isSMPicBrowser();
    sdk = await window.createSDK({
      fileType: FileType.DocumentPro,
      file,
    });

    const saveStatus = createSaveStatus(sdk);
    const syncStatus = createSyncStatus(sdk);

    handleSaveStatus(saveStatus, sdk);
    handleSyncStatus(syncStatus);

    sdk.setReadyState(ReadyState.LoadingEditor).catch((e) => {
      console.error(e);
    });

    const sdkOptions = sdk.sdkOptions;

    const socketResponse = window.initSocket(sdk, { fileGuid: file.guid });
    const socket = socketResponse.socket;
    sdk.addRuntimeUpdater(socketResponse.configUpdater);

    file.permissions.isPreview = false;
    file.permissions.isCollaborate = file.permissions.isCollaborate ?? true;

    let editor: DocxEditor;
    try {
      const { head, content } = await fetchContent(file);

      const checkPermission = await createCheckPermission(ShimoFileType.modoc);

      const renderNotificationBar = (props: { dom: HTMLDivElement }) => {
        const { dom } = props;
        renderEditorBanner({
          socket,
          dom,
        });
      };

      // 确保SDKDocx已经加载完成再createSDK2
      await loadSdk();

      editor = await window.SDKDocx.createSDK2({
        content: {
          data: content,
        },
        collaboration: {
          head,
          offlineEditable: getBoolean('PRIVATE_DEPLOY') ? getBoolean('CUSTOMIZED_OFFLINE') : true,
          saveStatus: {
            onChangeState: saveStatus.onChangeState,
            onError: saveStatus.onError,
          },
          syncStatus: {
            onChangeState: syncStatus.onChangeState,
            onError: syncStatus.onError,
          },
        },
        file,
        user,
        socket,
        container: elm,

        ...sdk.getDefaultCreateEditorOptions({ preview: false }),

        plugins: sdkOptions.plugins,

        forceDeviceMode: (['pc', 'mobile', 'pad'].find((i) => i === deviceMode) as DeviceMode) ?? undefined,

        disableSignatureComponent: sdkOptions.disableSignatureComponent,

        renderUserCard,
        checkPermission,
        getBrandConfig,
        renderNotificationBar,
      });
    } catch (error: unknown) {
      window.rendered = false;
      throw error;
    }

    sdk.editor = editor;

    editor.on('showSignatureComponent', (payload: { aspectRatio: number }) => {
      if (sdk) {
        sdk.invokeChannelMethod(ContainerMethod.ShowSignatureComponent, payload).catch((e) => console.error(e));
      }
    });

    rootElm.classList.add('rendered');
    window.rendered = true;

    window.__SM__ = {
      ...window.__SM__,
      editor,
    };

    setSocket(socket);

    sdk.onAddEventListener = (event: string, callback: EventCallback) => {
      let evt = event;
      // 套件改了事件，导致订阅不上，需要做兼容
      if (['SelectionEnded', 'DocumentChanged', 'CommentClicked'].includes(event)) {
        evt = event[0].toLowerCase() + event.slice(1);
      } else if (event === 'Recalculated') {
        evt = 'FullRecalculated';
      }

      // 传统文档 on 和 attach 有别，优先 on
      if (typeof editor.on === 'function') {
        editor.on(evt, callback);
      } else {
        editor.attachEvent!(evt, callback);
      }
    };

    sdk.setEditorMethodInvoker(editor);

    sdk.setReadyState(ReadyState.Ready).catch((e) => {
      console.error(e);
    });

    sdk.markPerformanceEntry('editor_render_end');
  })().catch((e) => {
    if (sdk) {
      sdk.setReadyState(ReadyState.Failed, e).catch((e) => {
        console.error(e);
      });
    }
    console.error(e);
  });
}
