// socket 监听器管理工具
export let socket: SocketIOClient.Socket | null = null;

// 定义 socket 事件名称
const SOCKET_READY_EVENT = 'socketReady';

// 添加 socket 监听器
export function onSocketReady(callback: (socket: SocketIOClient.Socket) => void) {
  if (socket) {
    // 如果 socket 已经准备好，立即执行回调
    callback(socket);
  } else {
    // 否则添加事件监听器
    function eventHandler(event: Event) {
      const customEvent = event as CustomEvent;
      callback(customEvent.detail.socket);
      // 自动移除监听器，避免重复触发
      window.removeEventListener(SOCKET_READY_EVENT, eventHandler);
    }

    window.addEventListener(SOCKET_READY_EVENT, eventHandler);
  }
}

// 设置 socket 实例并通知所有监听器
export function setSocket(socketInstance: SocketIOClient.Socket) {
  socket = socketInstance;
  // 派发自定义事件通知所有监听器
  window.dispatchEvent(
    new CustomEvent(SOCKET_READY_EVENT, {
      detail: { socket: socketInstance },
    }),
  );
}

export function resetSocket() {
  socket = null;
}
