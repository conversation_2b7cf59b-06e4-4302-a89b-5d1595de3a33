import './style.css';

import { FileType, ReadyState } from 'shimo-js-sdk-shared';

import { getBrandConfig } from '@/utils/Brand';
import { handleSaveStatus, handleSyncStatus } from '@/utils/controllers/collaboration';
import { renderEditorBanner } from '@/utils/EditorBanner/renderEditorBanner';
import { renderUserCard } from '@/utils/RenderUserCard';
import type { EventCallback, ShimoSDK } from '@/utils/ShimoSDK';
import { createSaveStatus, createSyncStatus } from '@/utils/ShimoSDK/collaboration';

import { createCheckPermission } from '../../CheckPermission';
import { ShimoFileType } from '../../CheckPermission/scripts';
import { setSocket } from '../utils/socketManager';

const loadSdk = async () => {
  return new Promise<any>(function handler(resolve, reject) {
    if (window.SmTable) {
      resolve(window.SmTable);
    } else {
      setTimeout(() => {
        handler(resolve, reject);
      }, 50);
    }
  });
};

export const initializerTable = async () => {
  let sdk: ShimoSDK | undefined;
  await (async () => {
    const elm = document.createElement('div');
    elm.style.height = '100%';
    elm.className = 'sm-table-container';
    const rootElm = document.querySelector('#editor-content')!;
    rootElm.appendChild(elm);

    const { file, user } = window;

    sdk = await window.createSDK({
      fileType: FileType.Table,
      file,
    });

    sdk.setReadyState(ReadyState.LoadingEditor).catch((e: unknown) => {
      console.error(e);
    });

    const saveStatus = createSaveStatus(sdk);
    const syncStatus = createSyncStatus(sdk);

    handleSaveStatus(saveStatus, sdk);
    handleSyncStatus(syncStatus);

    const socketResponse = window.initSocket(sdk, { fileGuid: file.guid });
    const socket = socketResponse.socket;
    sdk.addRuntimeUpdater(socketResponse.configUpdater);

    const checkPermission = await createCheckPermission(ShimoFileType.table);
    const renderNotificationBar = (props: { dom: HTMLDivElement }) => {
      const { dom } = props;
      renderEditorBanner({
        socket,
        dom,
      });
    };

    // 确保SmTable已经加载完成再createSDK2
    await loadSdk();

    const editor = await window.SmTable.createSDK2({
      file,
      user,
      socket,
      container: elm,
      ...sdk.getDefaultCreateEditorOptions({ preview: false }),
      renderUserCard,
      checkPermission,
      renderNotificationBar,
      getBrandConfig,
    });

    window.rendered = true;
    rootElm.classList.add('rendered');

    window.__SM__ = {
      ...window.__SM__,
      editor,
    };

    setSocket(socket);

    if (typeof editor.updateRuntimeEnv === 'function') {
      sdk.addRuntimeUpdater(editor.updateRuntimeEnv.bind(editor));
    }

    sdk.editor = editor;

    sdk.setEditorMethodInvoker(editor, async (method: string, args: unknown[]) => {
      if (method === 'addRangeLock' || method === 'addSheetLock') {
        const item: { userPermissions?: Record<string, number> } =
          typeof args[0] === 'object' && args[0] !== null ? args[0] : {};
        const userPermissions = item.userPermissions;
        if (userPermissions !== undefined) {
          const providerUserIds: string[] = Object.keys(userPermissions);
          if (providerUserIds.length !== 0) {
            const mappedUserIds = await sdk?.getUserIds(providerUserIds);

            const newPermissions: Record<string, number> = {};
            if (mappedUserIds) {
              for (const [providerUserId, userId] of Object.entries(mappedUserIds)) {
                newPermissions[userId] = userPermissions[providerUserId];
              }
            }

            item.userPermissions = newPermissions;
          }
        }
      }
    });

    sdk.onAddEventListener = (event: string, callback: EventCallback) => {
      editor.on(event, callback);
    };

    sdk.setReadyState(ReadyState.Ready).catch((e) => {
      console.error(e);
    });

    sdk.markPerformanceEntry('editor_render_end');
  })().catch((e) => {
    if (sdk) {
      sdk.setReadyState(ReadyState.Failed, e).catch((e: unknown) => {
        console.error(e);
      });
    }
    console.error(e);
  });
};
