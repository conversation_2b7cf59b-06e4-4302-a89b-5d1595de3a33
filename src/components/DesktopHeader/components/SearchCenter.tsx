import { SearchOutlined } from '@ant-design/icons';
import { Empty, Select, Typography } from 'antd';
import { debounce } from 'lodash';
import { useMemo, useRef, useState } from 'react';
import { history } from 'umi';

import { files, searchFile } from '@/api/File';
import { ReactComponent as SearchEmptyIcon } from '@/assets/images/empty/search.svg';
import { ReactComponent as SpaceSvg } from '@/assets/images/sidepanel/space.svg';
import { ReactComponent as FolderSvg } from '@/assets/images/svg/folder.svg';
import { useViewportHeight } from '@/hooks/useViewportHeight';
import { fm } from '@/modules/Locale';
import { getFileIcon, useFormatTime } from '@/utils/file';

import styles from '../index.less';
import { HybridHighlighter } from './TextHeightLight';

type IOption = {
  value: string;
  name: string;
  user: string;
  guid: string;
  url: string;
  target: {
    name: string;
    url: string;
    isSpace: boolean;
  };
  updatedUser: string;
  highlight: string;
  content: string;
  updatedAt: number;
  type: string;
  isSpace: boolean;
  lastAction: 'edit' | 'open';
};

export const SearchCenter = () => {
  const i18nText = {
    space: fm('SiderMenu.siderMenuSpaceText'),
    desktop: fm('SiderMenu.siderMenuDesktopText'),
  };
  const selectRef = useRef(null);

  const { formatTime } = useFormatTime();

  const [width, setWidth] = useState<number>(220);

  const [options, setOptions] = useState<IOption[]>([]);

  const [value, setValue] = useState<string>();

  const [loading, setLoading] = useState(false);

  const init = () => {
    setLoading(true);
    files({ type: 'used', lastAction: 'open', limit: 200 })
      .then((res: any) => {
        if (res.status === 200) {
          const { list = [] } = res.data;
          setOptions(
            list.map((item: any) => {
              return {
                value: item.guid,
                name: item.name,
                guid: item.guid,
                user: item.user?.name,
                url: item.url,
                updatedAt: item.updatedAt,
                type: item.type,
                isSpace: item.isSpace,
                lastAction: item.lastAction,
              };
            }),
          );
        }
      })
      .finally(() => {
        setLoading(false);
      });
  };

  const height = useViewportHeight();

  const handleChange = debounce((value: string | undefined) => {
    setValue(value);
    if (!value) {
      init();
      return;
    }
    searchFile({ keyword: value, size: 999 }).then((res) => {
      if (res.status === 200) {
        const { results } = res.data;
        setOptions(
          results.map((item: any) => {
            return {
              highlight: item.highlight.name, // 文件名
              content: item.highlight.content, // 文件内容
              guid: item.source.guid,
              user: item.source.user?.name,
              updatedUser: item.source.updatedUser?.name,
              url: item.url,
              updatedAt: new Date(item.source.updatedAt).getTime(),
              type: item.source.type,
              isSpace: item.source?.isSpace || false,
              target: {
                name: item.source.ancestors?.length
                  ? item.source.ancestors.at(-1).name
                  : item.source?.isSpace
                    ? i18nText.space
                    : i18nText.desktop,
                isSpace: item.source.ancestors?.length
                  ? item.source.ancestors.at(-1).isSpace
                  : item.source?.isSpace
                    ? item.source?.isSpace
                    : false,
                url: item.source.ancestors?.length ? item.source.ancestors.at(-1).url : item.url,
              },
            };
          }),
        );
      }
    });
  }, 200);

  const onFocus = () => {
    setWidth(560);
    init();
  };

  const onBlur = () => {
    setWidth(220);
    handleChange('');
  };

  const gotoFolder = (url: string, type: string) => {
    handleChange('');
    if (!url) return;

    if (type === 'folder') {
      history.push(url);
    } else {
      window.open(url);
    }
  };

  const sortOptions: any = useMemo(() => {
    if (options.length === 0) return [];
    const spaceData = options
      .filter((item: IOption) => item.type === 'folder' && item.isSpace)
      .sort((a: IOption, b: IOption) => b['updatedAt'] - a['updatedAt']);
    const folderData = options
      .filter((item: IOption) => item.type === 'folder' && !item.isSpace)
      .sort((a: IOption, b: IOption) => b['updatedAt'] - a['updatedAt']);
    const other = options
      .filter((item: IOption) => item.type !== 'folder')
      .sort((a: IOption, b: IOption) => b['updatedAt'] - a['updatedAt']);
    return [
      {
        label: (
          <div className={styles['searchContentTitle']}>
            {value ? fm('SearchCenter.search') : fm('SearchCenter.used')}
          </div>
        ),
        options: [...spaceData, ...folderData, ...other],
      },
    ];
  }, [options]);

  return (
    <div className={styles.headerSearch}>
      <Select
        ref={selectRef}
        showSearch
        allowClear={true}
        filterOption={false}
        listHeight={height - 60}
        loading={loading}
        notFoundContent={
          <div className={styles['searchNotFoundContent']}>
            <Empty description={fm('SearchCenter.noData')} image={<SearchEmptyIcon />} />
          </div>
        }
        optionFilterProp="name"
        optionRender={({ data }) => {
          return value ? (
            <div className={styles['searchContent']}>
              <div className={styles['searchSelectList']} onClick={() => gotoFolder(data.url, data.type)}>
                <div className={styles['searchSelectLeft']}>
                  <img height={40} src={getFileIcon({ type: data.type, isSpace: data.isSpace })} width={40} />
                </div>
                <div className={styles['searchSelectRight']}>
                  <div className={styles['searchSelectHighlight']}>
                    <Typography.Text ellipsis={{ tooltip: data.highlight }} style={{ maxWidth: 300 }}>
                      <HybridHighlighter searchTerm={value} text={data.highlight} />
                    </Typography.Text>
                  </div>
                  <div className={styles['searchSelectContent']}>
                    <HybridHighlighter searchTerm={value} text={data.content} />
                  </div>
                  <div className={styles['searchSelectLast']}>
                    <div className={styles['searchSelectLastLeft']}>
                      <Typography.Text ellipsis={{ tooltip: data.user }} style={{ maxWidth: 60 }}>
                        {data.user}
                      </Typography.Text>
                      <span className={styles['searchSelectLastLegend']}>·</span>
                      <span
                        className={styles['searchSelectTarget']}
                        onClick={() => {
                          gotoFolder(data.target?.url, data.type);
                        }}
                      >
                        {data.target?.isSpace ? <SpaceSvg /> : <FolderSvg />}
                        <Typography.Text ellipsis={{ tooltip: data.target?.name }} style={{ maxWidth: 100 }}>
                          {data.target?.name}
                        </Typography.Text>
                      </span>
                    </div>
                    <div className={styles['searchSelectLastRight']}>
                      <span>{formatTime(data.updatedAt)}</span>
                      <Typography.Text
                        className={styles['searchSelectLastUpdatedUser']}
                        ellipsis={{ tooltip: data.updatedUser }}
                      >
                        {data.updatedUser}
                      </Typography.Text>
                      <span>{fm('SearchCenter.update')}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className={styles['searchContent']}>
              <div className={styles['usedSelectList']}>
                <div className={styles['usedSelectLeft']}>
                  <img height={20} src={getFileIcon({ type: data.type, isSpace: data.isSpace })} width={20} />
                  <Typography.Text ellipsis={{ tooltip: data.name }} style={{ maxWidth: 200 }}>
                    {data.name}
                  </Typography.Text>
                </div>
                <div className={styles['usedSelectRight']}>
                  <span className={styles['usedSelectDate']}>{formatTime(data.updatedAt * 1000)}</span>
                  <span className={styles['usedSelectUser']}>{fm('SearchCenter.me')}</span>
                  <span className={styles['usedSelectAction']}>
                    {data.lastAction === 'edit' ? fm('File.edit') : fm('SearchCenter.open')}
                  </span>
                </div>
              </div>
            </div>
          );
        }}
        options={sortOptions}
        placeholder={fm('SearchCenter.searchFile')}
        popupClassName={styles.headerSearchDropdown}
        style={{ transition: 'width 0.1s', width: `${width}px` }}
        suffixIcon={<SearchOutlined />}
        value={value}
        onBlur={onBlur}
        onClear={() => {
          handleChange('');
        }}
        onFocus={onFocus}
        onSearch={handleChange}
        onSelect={(value, option) => {
          gotoFolder(option.url, option.type);
        }}
      />
    </div>
  );
};
