import { BellOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, But<PERSON>, Card, Empty, List, Popover } from 'antd';
import { useEffect, useState } from 'react';

import { getNotifications, getUnpeekedCount, readAll } from '@/api/Message';
import { ReactComponent as CheckSvg } from '@/assets/images/svg/check.svg';
import { useFormatMessage } from '@/modules/Locale';
import { useFormatTime } from '@/utils/file';

import styles from '../index.less';
import type { GroupedNotification, Notification } from '../type';
import { MessageCard } from './MessageCard';

function groupNotifications(notifications: Notification[]): GroupedNotification[] {
  // 1. 创建临时分组字典
  const dateMap: Record<string, Notification[]> = {};

  // 2. 遍历处理每个通知
  notifications.forEach((notification) => {
    try {
      const date = new Date(notification.notification.createdAt);
      if (isNaN(date.getTime())) return;

      // 生成标准化日期键
      const dateKey = date.toISOString().split('T')[0];

      if (!dateMap[dateKey]) {
        dateMap[dateKey] = [];
      }

      dateMap[dateKey].push(notification);
    } catch (error) {
      // console.error(`处理通知 ${notification?.id} 时出错:`, error);
    }
  });

  // 3. 转换为目标结构并排序
  return Object.entries(dateMap)
    .map(([dateKey, items]) => ({
      createdAt: dateKey,
      children: items.sort(
        (a, b) => new Date(b.notification.createdAt).getTime() - new Date(a.notification.createdAt).getTime(),
      ),
    }))
    .sort((a, b) => b.createdAt.localeCompare(a.createdAt)); // 日期倒序
}

export const MessageCenter = () => {
  const i18nText = {
    filterRead: useFormatMessage('MessageCenter.onlyUnreadButtonText'),
    readAll: useFormatMessage('MessageCenter.allMarkReadButtonText'),
  };
  const [data, setData] = useState<GroupedNotification[]>([]);

  const [count, setCount] = useState<number>(0);

  const { formatTime } = useFormatTime();

  const [status, setStatus] = useState<'all' | 'unread'>('all');

  /** 查看未读计数 */
  const getUnReadCount = () => {
    getUnpeekedCount().then((res) => {
      if (res.status === 200) {
        setCount(res.data);
      }
    });
  };

  const getMessageList = () => {
    getNotifications({ status, limit: 20 }).then((res) => {
      if (res.status === 200) {
        const groupData = groupNotifications(res.data || []);
        setData(groupData);
      }
    });
  };

  const changeStatus = () => {
    setStatus(status === 'all' ? 'unread' : 'all');
  };

  const readAllMessage = () => {
    readAll()
      .then(() => {
        getUnReadCount();
        getMessageList();
      })
      .catch(() => {});
  };

  const cb = () => {
    getUnReadCount();
    getMessageList();
  };

  useEffect(() => {
    getUnReadCount();
  }, []);

  useEffect(() => {
    getMessageList();
  }, [status]);

  return (
    <Popover
      content={
        <Card
          className={styles.messageContent}
          extra={
            <Button
              className={styles['messageTopButton']}
              disabled={!count}
              icon={<CheckSvg />}
              size="small"
              type="text"
              onClick={readAllMessage}
            >
              {i18nText.readAll}
            </Button>
          }
          title={
            <Button
              className={styles['messageTopButton']}
              size="small"
              type={status === 'all' ? 'text' : 'default'}
              onClick={changeStatus}
            >
              {i18nText.filterRead}
            </Button>
          }
        >
          {data.length ? (
            <List
              dataSource={data}
              renderItem={({ children, createdAt }, groupIndex) => (
                <List
                  key={groupIndex}
                  dataSource={children}
                  header={<span className={styles.messageHeaderClassName}>{formatTime(createdAt, 'message')}</span>}
                  itemLayout="horizontal"
                  renderItem={(item, index) => (
                    <List.Item
                      key={index}
                      onClick={() => {
                        window.open(item.notification?.file?.url);
                      }}
                    >
                      <MessageCard callback={cb} item={item} />
                    </List.Item>
                  )}
                />
              )}
            />
          ) : (
            <Empty />
          )}
        </Card>
      }
      placement="bottom"
      styles={{
        body: {
          width: '300px',
          marginRight: '8px',
        },
      }}
      trigger={['click']}
    >
      <Badge color="#6DA0E3" dot={!!count} offset={[-8, 18]} size="small">
        <Button icon={<BellOutlined />} type="text" />
      </Badge>
    </Popover>
  );
};
