import { Button } from 'antd';
import classNames from 'classnames';
import React from 'react';

import styles from './ErrorPageBase.less';

interface ErrorPageBaseProps {
  imgSrc: string;
  title: string;
  subTitle: string;
  buttons?: {
    label: string;
    onClick: () => void;
    type?: 'primary' | 'plain' | 'link';
  }[];
}

const ErrorPageBase: React.FC<ErrorPageBaseProps> = (props) => {
  const { imgSrc, title, subTitle, buttons } = props;
  return (
    <div className={styles.errorPageLayouts}>
      <img src={imgSrc} />
      <h1>{title}</h1>
      <h2>{subTitle}</h2>
      {buttons?.map((button, index) => (
        <Button
          key={index}
          className={classNames({
            [styles.plainButton]: button.type === 'plain',
            [styles.linkButton]: button.type === 'link',
          })}
          onClick={button.onClick}
        >
          {button.label}
        </Button>
      ))}
    </div>
  );
};

export default ErrorPageBase;
