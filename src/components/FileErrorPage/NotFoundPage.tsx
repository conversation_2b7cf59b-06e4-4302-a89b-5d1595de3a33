import { history } from 'umi';

import img from '@/assets/images/svg/error-page-404.svg';
import { useFormatMessage } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NotFoundPage = () => {
  const title = useFormatMessage('Error.notFound');
  const subtitle = useFormatMessage('Error.notFoundDes');
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');

  return (
    <ErrorPageBase
      buttons={[
        {
          label: goBackHomePage,
          onClick: () => {
            history.push('/');
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NotFoundPage;
