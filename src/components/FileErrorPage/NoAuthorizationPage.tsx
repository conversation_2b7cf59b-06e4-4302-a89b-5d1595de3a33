import { history } from 'umi';

import img from '@/assets/images/svg/error-page-no-authorization.svg';
import { useFormatMessage } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NoAuthorizationPage = () => {
  const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const switchAccount = useFormatMessage('Error.switchAccount');
  const title = useFormatMessage('Error.noPermission');
  const subtitle = useFormatMessage('Error.noPermissionDes');
  const applyForPermissionBtn = useFormatMessage('Error.applyForPermission');

  return (
    <ErrorPageBase
      buttons={[
        {
          label: applyForPermissionBtn,
          onClick: () => {
            // todo
          },
        },
        {
          label: goBackHomePage,
          type: 'plain',
          onClick: () => {
            history.push('/');
          },
        },
        {
          label: switchAccount,
          type: 'link',
          onClick: () => {
            history.push('/logout');
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NoAuthorizationPage;
