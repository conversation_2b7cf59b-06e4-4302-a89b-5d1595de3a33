html body .errorPageLayouts {
  @text-color-default: var(--theme-text-color-default);
  @bg-color: var(--theme-basic-color-bg-default);

  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  img {
    width: 320px;
    height: 260px;
    margin-top: -10%;
  }

  h1 {
    font-size: 28px;
    font-weight: 500;
    line-height: 48px;
    color: @text-color-default;
    margin: 0;
  }

  h2 {
    font-size: 14px;
    color: @text-color-default;
    margin: 10px 0 32px;
    /* stylelint-disable-next-line font-weight-notation */
    font-weight: normal;
  }

  button {
    font-size: 16px;
    font-weight: 500;
    height: 40px;
    padding: 0 32px;
    margin-bottom: 8px;
    color: @bg-color;
    border-radius: 2px;
    border: 1px solid var(--theme-text-color-header);
    background: var(--theme-efficiency-button-color-bg);

    &.plainButton {
      background: none;
      background-color: @bg-color;
      color: @text-color-default;
      border-color: var(--theme-basic-color-light);
    }

    &.linkButton {
      font-size: 12px;
      color: var(--theme-button-color-primary-hover);
      /* stylelint-disable-next-line font-weight-notation */
      font-weight: normal;
      background: @bg-color;
      border: none;
      margin-top: -8px;
    }
  }
}
