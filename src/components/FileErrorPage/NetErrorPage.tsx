import { history } from 'umi';

import img from '@/assets/images/svg/error-page-404.svg';
import { fm } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NetErrorPage = () => {
  const title = fm('Error.netErr');
  const subtitle = fm('Error.netErrDes');
  const btnContent = fm('Error.refresh');

  return (
    <ErrorPageBase
      buttons={[
        {
          label: btnContent,
          type: 'plain',
          onClick: () => {
            history.back();
          },
        },
      ]}
      imgSrc={img}
      subTitle={subtitle}
      title={title}
    />
  );
};

export default NetErrorPage;
