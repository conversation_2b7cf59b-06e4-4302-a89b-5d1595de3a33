import { history } from 'umi';

import img from '@/assets/images/svg/error-page-404.svg';
import { useFormatMessage } from '@/modules/Locale';

import ErrorPageBase from './ErrorPageBase';

const NoSeatsPage = () => {
  // const goBackHomePage = useFormatMessage('Error.goBackHomePage');
  const noSeatsTitle = useFormatMessage('Error.noSeatsTitle');
  const noSeatsTitleDes = useFormatMessage('Error.noSeatsTitleDes');
  const contactAdmin = useFormatMessage('Error.contactAdmin');
  const purchaseSeats = useFormatMessage('Error.purchaseSeats');

  return (
    <ErrorPageBase
      buttons={[
        // {
        //   label: goBackHomePage,
        //   onClick: () => {
        //     history.push('/');
        //   },
        // },
        {
          label: contactAdmin,
          onClick: () => {
            history.push('/');
          },
        },
        {
          label: purchaseSeats,
          type: 'plain',
          onClick: () => {
            // history.push('/');
          },
        },
      ]}
      imgSrc={img}
      subTitle={noSeatsTitleDes}
      title={noSeatsTitle}
    />
  );
};

export default NoSeatsPage;
