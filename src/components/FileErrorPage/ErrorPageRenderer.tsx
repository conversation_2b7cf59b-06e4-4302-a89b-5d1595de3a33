import type { ErrorCodes } from '@/utils/request/ErrorCodeMap';

import FileDeletePage from './FileDeletePage';
import NetErrorPage from './NetErrorPage';
import NoAuthorizationPage from './NoAuthorizationPage';
import NoAuthPage from './NoAuthPage';
import NoSeatsPage from './NoSeatsPage';
import NotFoundPage from './NotFoundPage';

interface ErrorPageRendererProps {
  errorCode?: ErrorCodes;
}

const ErrorPageRenderer = (props: ErrorPageRendererProps) => {
  const { errorCode } = props;

  switch (errorCode) {
    case 60093:
      return <NoAuthPage />;
    case 404:
    case 60001:
      return <NotFoundPage />;
    case 60002:
      return <FileDeletePage />;
    case 20025:
    case 403:
      return <NoAuthorizationPage />;
    case 1:
      return <NetErrorPage />;
    case 31000:
      return <NoSeatsPage />;

    default:
      return <NotFoundPage />;
  }
};

export default ErrorPageRenderer;
