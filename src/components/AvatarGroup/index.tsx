import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';

import defaultAvatar from '@/assets/images/editor/default-avatar-moke.webp';
import UserCenter from '@/components/DesktopHeader/components/UserCenter';
import { fm } from '@/modules/Locale';

import styles from './style.less';

export interface AvatarItem {
  id: string | number;
  name?: string;
  avatar?: string;
  color?: string;
}

interface AvatarGroupProps {
  avatars: AvatarItem[]; // 用户头像列表
  maxCount?: number; // 最多显示头像数量
  size?: number; // 头像大小
  className?: string; // 类名
  style?: React.CSSProperties; // 样式
  narrowBreakpoint?: number; // 窄屏模式断点宽度（默认为800px），低于此宽度时只显示当前用户和气泡
}

/**
 * 头像组组件
 * 当参与人数超过maxCount时，只显示自己和最早进入的maxCount-1个人的头像
 * 其余人员的头像聚合在带数字的石墨色气泡中
 * 当页面宽度小于narrowBreakpoint时，只显示当前用户头像和气泡
 * 当前用户始终显示在最右侧，其他用户从右到左排列
 */
export function AvatarGroup({
  avatars = [],
  maxCount = 7,
  size = 24,
  className,
  style,
  narrowBreakpoint = 800,
}: AvatarGroupProps) {
  const anonymousName = fm('service.Me.anonymousName');

  const [isNarrowMode, setIsNarrowMode] = useState(false);

  // 监听窗口大小变化，判断是否启用窄屏模式
  useEffect(() => {
    function checkWidth() {
      setIsNarrowMode(window.innerWidth < narrowBreakpoint);
    }

    // 初始检查
    checkWidth();

    window.addEventListener('resize', checkWidth);

    // 清理监听
    return () => window.removeEventListener('resize', checkWidth);
  }, [narrowBreakpoint]);

  // 渲染用户头像
  function renderUserAvatar(user: AvatarItem) {
    return (
      <Tooltip key={user.id} placement="bottom" title={user.name || anonymousName}>
        <div className={styles.avatarWrapper}>
          <div
            className={styles.avatar}
            style={{
              width: size,
              height: size,
              fontSize: size * 0.5,
            }}
          >
            {<img src={user.avatar || defaultAvatar} />}
          </div>
          {<div className={styles.statusDot} style={{ backgroundColor: user.color }} />}
        </div>
      </Tooltip>
    );
  }

  // 渲染剩余用户头像气泡以及弹框
  function renderRestCountBubble(restUsers: AvatarItem[], count: number) {
    return (
      <Popover
        arrow={false}
        content={<div className={styles.avatarPopoverContent}>{restUsers.map((user) => renderUserAvatar(user))}</div>}
        placement="bottom"
      >
        <span className={styles.restCount} style={{ height: size }}>
          +{count}
        </span>
      </Popover>
    );
  }

  // 窄屏模式：只显示当前用户和其他所有人的气泡
  if (isNarrowMode && avatars.length > 1) {
    const otherUsersCount = avatars.length;

    return (
      <div className={classNames(styles.avatarGroup, className)} style={style}>
        {otherUsersCount > 0 && renderRestCountBubble(avatars, otherUsersCount)}

        <div className={styles.avatarWrapper}>
          <UserCenter size={size} trigger={['hover']} />
        </div>
      </div>
    );
  }

  // 正常模式：显示最多maxCount个头像，反转数据排列顺序，从右到左
  const visibleUsers = avatars.reverse().slice(0, maxCount - 1);
  // 剩余用户头像列表
  const hiddenUsers = avatars.length > maxCount ? avatars.slice(maxCount - 1) : [];
  // 剩余用户数量
  const restCount = hiddenUsers.length;

  return (
    <div className={classNames(styles.avatarGroup, className)} style={style}>
      {/* 剩余数量气泡*/}
      {restCount > 0 && renderRestCountBubble(hiddenUsers, restCount)}

      {/* 显示可见的头像 */}
      {visibleUsers.map((user) => renderUserAvatar(user))}

      {/* 当前用户头像 */}
      <div className={styles.avatarWrapper}>
        <UserCenter size={size} trigger={['click']} />
      </div>
    </div>
  );
}
