# AvatarGroup 头像组组件

用于显示多个用户头像的组件，特别适用于协作场景。当协作者人数超过指定数量时，会聚合显示剩余人数。

## 功能特点

- 可配置最大显示头像数量（默认为 7）
- 超过最大数量的头像会聚合成带数字的石墨色气泡，显示在最左侧
- 点击数字气泡，会弹出包含所有被隐藏用户的头像网格
- 响应式设计：当屏幕宽度低于断点值时，只显示当前用户头像和所有其他协作者的气泡
- 当前用户头像始终显示在最右侧，其他协作者按照进入顺序从右到左排列
- 每个头像都有悬停提示信息，显示用户名称
- 支持自定义头像大小

## 使用方法

```tsx
import { AvatarGroup, AvatarItem } from '@/components/AvatarGroup';

// 准备用户数据
const users: AvatarItem[] = [
  { id: 'user1', name: '用户1', avatar: 'https://example.com/avatar1.png' },
  { id: 'user2', name: '用户2', avatar: 'https://example.com/avatar2.png' },
  // 更多用户...
];

// 基础用法
<AvatarGroup avatars={users} />

// 自定义最大显示数量
<AvatarGroup avatars={users} maxCount={5} />

// 自定义头像大小
<AvatarGroup avatars={users} size="32" />

// 自定义窄屏模式断点宽度（默认为800px）
<AvatarGroup avatars={users} narrowBreakpoint={700} />
```
