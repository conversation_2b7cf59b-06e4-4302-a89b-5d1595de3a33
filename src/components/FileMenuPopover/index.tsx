import { Popover, Tooltip } from 'antd';
import classNames from 'classnames';
import React, { useEffect, useState } from 'react';

import arrowRight from '@/assets/images/common/<EMAIL>';
import checkOutlined from '@/assets/images/common/<EMAIL>';
import { fm } from '@/modules/Locale';

import css from './style.less';

interface PopoverProps {
  onItemClick?: (item: { key: string; exportType?: string; fileGuid?: string; noSupport?: boolean }) => void;
  onOpenInNewTab?: (key: string) => void;
  subItems?: MenuItem[];
  fileType?: string;
  isFavorite?: boolean;
  isAdmin?: boolean;
  role?: string;
}

export interface MenuItem {
  key: string;
  title: string;
  icon?: React.ReactNode;
  subItems?: MenuItem[];
  showDivider?: boolean;
  exportType?: string;
  disabled?: boolean;
  noSupport?: boolean;
  hide?: boolean;
}

// 除了 收藏 下载  删除 功能外，其他的 item 都禁止点击，类似添加了 disabled 的效果

export function FileMenuPopover({ onItemClick, fileType = '', isFavorite = false, isAdmin, role }: PopoverProps) {
  // 新增子菜单弹出状态管理
  const [subMenuVisible, setSubMenuVisible] = useState<Record<string, boolean>>({});

  // 当组件重新渲染时，重置所有子菜单的显示状态
  useEffect(() => {
    setSubMenuVisible({});
  }, [fileType, isFavorite]);

  const i18n = {
    modoc: fm('useFileDetail.modocTitle'),
    newdoc: fm('useFileDetail.modocTitle'),
    mosheet: fm('useFileDetail.mosheetTitle'),
    table: fm('useFileDetail.tableTitle'),
    presentation: fm('useFileDetail.pptTitle'),
    form: fm('useFileDetail.formTitle'),
  };

  const SUPPORT_VIEW_CONFIG = [
    {
      key: 'move', // 移动
      title: fm('FileMenuPopover.move'),
      noSupport: true,
    },
    {
      key: 'createCopy', // 创建副本
      title: fm('FileMenuPopover.createCopy'),
      showDivider: true,
      noSupport: true,
    },
    {
      key: 'fileInfo', // 文档信息
      title: fm('FileMenuPopover.documentInfo'),
      showDivider: true,
    },
    {
      key: 'delete', // 删除
      title: fm('FileMenuPopover.delete'),
    },
  ] satisfies MenuItem[];

  function getItems(type: string): MenuItem[] {
    const commonItems: MenuItem[] = [
      {
        key: 'favorite',
        title: fm('FileMenuPopover.favorite'),
        icon: isFavorite ? <img src={checkOutlined} width={12} /> : null,
        showDivider: true,
      },
      {
        key: 'move',
        title: fm('FileMenuPopover.move'),
        disabled: !isAdmin,
      },
      {
        key: 'createCopy',
        title: fm('FileMenuPopover.createCopy'),
        showDivider: type === 'table' || type === 'form',
        disabled: !role,
      },
    ];

    switch (type) {
      case 'newdoc':
      case 'modoc':
      case 'mosheet': {
        const downItemsMap = {
          newdoc: [
            { key: 'downImage', exportType: 'jpg', title: fm('FileMenuPopover.downImage') },
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
            { key: 'downMarkdown', exportType: 'md', title: fm('FileMenuPopover.downMarkdown') },
          ],
          modoc: [
            { key: 'downWord', exportType: 'docx', title: fm('FileMenuPopover.downWord') },
            { key: 'downWPS', exportType: 'wps', title: fm('FileMenuPopover.downWPS') },
            { key: 'downImagePDF', exportType: 'imagePdf', title: fm('FileMenuPopover.downImagePDF') },
            { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
          ],
          mosheet: [
            { key: 'downExcel', exportType: 'xlsx', title: fm('FileMenuPopover.downExcel') },
            {
              key: 'downPDF',
              exportType: 'pdf',
              title: fm('FileMenuPopover.downPDF'),
            },
            {
              key: 'downImage',
              exportType: 'image',
              title: fm('FileMenuPopover.downImage'),
            },
          ],
        };

        const helpItemsMap = {
          newdoc: [
            { key: 'docGuide', title: fm('FileMenuPopover.docGuide'), noSupport: true },
            { key: 'docShortcut', title: fm('FileMenuPopover.docShortcut'), noSupport: true },
          ],
          mosheet: [
            { key: 'mosheetGuide', title: fm('FileMenuPopover.mosheetGuide'), noSupport: true },
            { key: 'mosheetShortcut', title: fm('FileMenuPopover.mosheetShortcut'), noSupport: true },
          ],
        };

        const downItems = downItemsMap[type];
        const helpItems = type !== 'modoc' ? helpItemsMap[type] : undefined;

        return [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: downItems,
          },
          { key: 'print', title: fm('FileMenuPopover.print'), showDivider: true },
          { key: 'saveVersion', title: fm('FileMenuPopover.saveVersion'), showDivider: true },
          { key: 'viewHistory', title: fm('FileMenuPopover.viewHistory'), showDivider: true },
          ...(type !== 'modoc' ? [{ key: 'viewCommentList', title: fm('FileMenuPopover.viewCommentList') }] : []),
          { key: 'fileInfo', title: i18n[type], showDivider: type === 'modoc' },
          ...(type !== 'modoc'
            ? [
                {
                  key: 'help',
                  title: fm('FileMenuPopover.help'),
                  subItems: helpItems,
                  showDivider: true,
                  noSupport: true,
                  hide: true,
                },
              ]
            : []),
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'table': {
        return [
          ...commonItems,
          { key: 'convertToMoSheet', title: fm('FileMenuPopover.convertToMoSheet'), noSupport: true },
          { key: 'downToExcel', title: fm('FileMenuPopover.downToExcel'), showDivider: true },
          { key: 'fileInfo', title: i18n[type], showDivider: true },
          { key: 'tableHelp', title: fm('FileMenuPopover.tableHelp'), noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'presentation': {
        return [
          ...commonItems,
          {
            key: 'download',
            title: fm('FileMenuPopover.download'),
            subItems: [
              { key: 'downPPTX', exportType: 'pptx', title: fm('FileMenuPopover.downPPTX') },
              { key: 'downPDF', exportType: 'pdf', title: fm('FileMenuPopover.downPDF') },
              {
                key: 'downImagePDF',
                exportType: 'imagePdf',
                title: fm('FileMenuPopover.downImagePDF'),
              },
              { key: 'downImage', exportType: 'image', title: fm('FileMenuPopover.downImage') },
            ],
          },
          { key: 'print', title: fm('FileMenuPopover.print'), showDivider: true },
          { key: 'saveVersion', title: fm('FileMenuPopover.saveVersion'), showDivider: true, noSupport: true },
          { key: 'viewHistory', title: fm('FileMenuPopover.viewHistory') },
          { key: 'addComment', title: fm('FileMenuPopover.addComment'), noSupport: true },
          { key: 'viewComment', title: fm('FileMenuPopover.viewComment'), showDivider: true, noSupport: true },
          { key: 'fileInfo', title: i18n[type], showDivider: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }
      case 'form': {
        return [
          ...commonItems,
          { key: 'formHelp', title: fm('FileMenuPopover.formHelp'), showDivider: true, noSupport: true, hide: true },
          { key: 'delete', title: fm('FileMenuPopover.delete') },
        ];
      }

      case 'txt': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'doc': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'docx': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'ppt': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'img': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'mp3': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'mp4': {
        return SUPPORT_VIEW_CONFIG;
      }
      case 'pdf': {
        return SUPPORT_VIEW_CONFIG;
      }

      default:
        return commonItems;
    }
  }

  const items = getItems(fileType);

  function handleItemClick(item: MenuItem) {
    // 点击触发时同步关闭所有子菜单弹框
    setSubMenuVisible({});
    onItemClick?.({ key: item.key, exportType: item.exportType || '', noSupport: item.noSupport });
  }

  // 控制子菜单显示状态
  function subMenuVisibleChange(key: string, visible: boolean) {
    setSubMenuVisible((prev) => ({
      ...prev,
      [key]: visible,
    }));
  }

  // 通用菜单项渲染函数
  function renderMenuItem(item: MenuItem) {
    // 判断是否应该禁用该菜单项
    const isDisabled = item.disabled;

    // 创建菜单项内容
    const menuItemContent = (
      <Tooltip placement="left" title={isDisabled ? fm('FileMenuPopover.noPermissionTip') : ''}>
        <div
          key={item.key}
          className={classNames(css.item, {
            [css.danger]: item.key === 'delete',
            [css.disabled]: isDisabled,
            [css.favorite]: item.key === 'favorite' && isFavorite,
          })}
          onClick={() => !isDisabled && handleItemClick(item)}
        >
          <div className={css.itemContent}>
            <span>{item.title}</span>
            {item.icon && <span className={css.itemIcon}>{item.icon}</span>}
          </div>
          {item.subItems && <img src={arrowRight} width={5.5} />}
        </div>
      </Tooltip>
    );

    // 如果有子菜单，则渲染弹出菜单
    if (item.subItems && item.subItems.length) {
      return (
        <React.Fragment key={item.key}>
          <Popover
            key={item.key}
            arrow={false}
            content={
              <div className={css.submenuContent}>
                {item.subItems.map((subItem) => (
                  <React.Fragment key={subItem.key}>
                    {renderMenuItem({ ...subItem, key: `${item.key}-${subItem.key}` })}
                  </React.Fragment>
                ))}
              </div>
            }
            open={subMenuVisible[item.key]}
            placement="rightTop"
            trigger={isDisabled ? [] : ['hover']}
            onOpenChange={(visible) => subMenuVisibleChange(item.key, visible)}
          >
            {!item.hide && menuItemContent}
          </Popover>
          {!item.hide && item.showDivider && <div className={css.divider} />}
        </React.Fragment>
      );
    }

    // 无子菜单，直接返回菜单项
    return (
      <React.Fragment key={item.key}>
        {!item.hide && menuItemContent}
        {!item.hide && item.showDivider && <div className={css.divider} />}
      </React.Fragment>
    );
  }

  return (
    <div className={css.container}>
      <div className={css.section}>{items.map(renderMenuItem)}</div>
    </div>
  );
}
