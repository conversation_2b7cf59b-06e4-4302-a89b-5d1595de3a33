import { Button, Form, Input, message } from 'antd';
import React, { useEffect } from 'react';
import { useLocation } from 'umi';

import { verifyFilePassword } from '@/api/File';
import { catchApiResult } from '@/api/Request';
import { useFormatMessage } from '@/modules/Locale';

import styles from './PasswordInput.less';

const fm = useFormatMessage;

interface PasswordInputProps {
  shareUserName: string;
  autoFillPassword?: boolean; // 是否自动填充密码，默认为 false，不自动填充
}

const PasswordInput: React.FC<PasswordInputProps> = ({ autoFillPassword = false, shareUserName }) => {
  const [form] = Form.useForm();
  const location = useLocation();

  useEffect(() => {
    if (!autoFillPassword) return;

    const pathname = location.pathname;
    const lastPathSegment = decodeURIComponent(pathname.split('/').pop() || '');
    const extractedPassword = lastPathSegment.slice(-6);

    if (extractedPassword && extractedPassword.length === 6) {
      form.setFieldsValue({ password: extractedPassword });
      form.submit();
    }
  }, [form, location, autoFillPassword]);

  const handleSubmit = async (values: { password: string }) => {
    if (!values.password) return;

    const pathSegments = location.pathname.split('/').filter((seg) => seg);
    const fileGuid = pathSegments[1] || '';
    const fileType = pathSegments[0] || '';

    if (!fileGuid || !fileType) {
      console.error(fm('FilePasswordInput.invalidGuidOrType'));
      return;
    }

    const [err, res] = await catchApiResult(verifyFilePassword(fileGuid, values.password));
    if (err) {
      console.error(err);
      message.error(err?.data?.msg);
      return;
    }
    if (res?.status === 200 && res.data.verified) {
      window.location.reload();
    }
  };

  return (
    <div className={styles.passwordInputLayouts}>
      <div className={styles.encryptedFileAccess}>
        <p className={styles.userShare}>{fm('FilePasswordInput.encryptedFileShared', { name: shareUserName })}</p>
        <div className={styles.passwordContainer}>
          <div className={styles.horizontalLine} />
          <p className={styles.passwordTips}>{fm('FilePasswordInput.encryptedFilePasswordTip')}</p>
          <div className={styles.horizontalLine} />
        </div>
        <Form form={form} layout="inline" style={{ height: 84 }} onFinish={handleSubmit}>
          <Form.Item
            name="password"
            rules={[{ required: true, message: fm('FilePasswordInput.PasswordRequired') }]}
            style={{ margin: '0 10px 0 16px' }}
          >
            <Input.Password
              placeholder={fm('FilePasswordInput.encryptedFilePasswordPlaceholder')}
              size="large"
              style={{ width: 220 }}
              visibilityToggle={false}
            />
          </Form.Item>

          <Form.Item>
            <Button htmlType="submit" size="large" style={{ width: 96 }} type="primary">
              {fm('FilePasswordInput.confirm')}
            </Button>
          </Form.Item>
        </Form>
      </div>
    </div>
  );
};

export default PasswordInput;
