html body .passwordInputLayouts {
  @text-color-default: var(--theme-text-color-default);
  @bg-color: var(--theme-basic-color-bg-default);

  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--theme-layout-color-bg-black);

  button {
    font-size: 16px;
    font-weight: 500;
    height: 40px;
    padding: 0 32px;
    margin-bottom: 8px;
    color: @bg-color;
    border-radius: 2px;
    border: 1px solid var(--theme-text-color-header);
    background: var(--theme-efficiency-button-color-bg);

    &.plainButton {
      background: none;
      background-color: @bg-color;
      color: @text-color-default;
      border-color: var(--theme-basic-color-light);
    }

    &.linkButton {
      font-size: 12px;
      color: var(--theme-button-color-primary-hover);
      font-weight: 400;
      background: @bg-color;
      border: none;
      margin-top: -8px;
    }
  }

  .encryptedFileAccess {
    width: 368px;
    height: 232px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex-direction: column;
    margin-bottom: 450px;
    color: var(--theme-chart-tip-color-text);
    line-height: 32px;
    font-weight: 500;

    button {
      margin: 0;
    }

    .userShare {
      font-size: 20px;
    }

    .passwordTips {
      font-size: 32px;
      margin: 0 20px;
    }

    .passwordContainer {
      display: flex;
      align-items: center;
    }

    .horizontalLine {
      width: 20px;
      height: 1px;
      background-color: var(--theme-basic-color-bg-default);
    }
  }
}
