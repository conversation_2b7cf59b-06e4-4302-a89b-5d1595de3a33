import 'overlayscrollbars/overlayscrollbars.css';

import { <PERSON><PERSON>, Drawer } from 'antd';
import React from 'react';

import { ReactComponent as DownSvg } from '@/assets/images/upload/down.svg';
import type { TemplateDataItem } from '@/model/Template';
import { fm } from '@/modules/Locale';

import styles from './index.less';
interface TemplateReviewProp {
  open: boolean;
  itemData: TemplateDataItem | undefined;
  onClose: () => void;
}

export const TemplateReview = React.memo(function TemplateReview({ open, itemData, onClose }: TemplateReviewProp) {
  const i18nText = {
    back: fm('TemplateReview.back'),
    useTemp: fm('TemplateReview.useTemp'),
  };
  const closeDrawer = () => {
    onClose();
  };

  const titleView = (
    <div className={styles.titleView}>
      <Button className={styles.back} icon={<DownSvg className={styles.backIcon} />} type="text" onClick={closeDrawer}>
        {i18nText.back}
      </Button>
      <div className={styles.header}>
        <div className={styles.headerImage}>
          <img src={itemData?.img} />
        </div>
        <div className={styles.headerRight}>
          <div className={styles.title}>{itemData?.name}</div>
          <Button type="primary">{i18nText.useTemp}</Button>
        </div>
      </div>
    </div>
  );

  return (
    <Drawer
      className={styles.drawerView}
      closable={false}
      getContainer={false}
      open={open}
      placement="right"
      title={titleView}
      width="100%"
    >
      <iframe src={itemData?.desc_url} />
    </Drawer>
  );
});
