import { CaretDownOutlined, CheckOutlined, UserOutlined } from '@ant-design/icons';
import type { TabsProps } from 'antd';
import { Avatar, Button, Collapse, Dropdown, Input, message, Space, Switch, Tabs, Tooltip } from 'antd';
import { debounce } from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';

import {
  addCollaborationDepartment,
  deleteAdmin,
  deleteCollaboration,
  deleteCollaborationDepartment,
  deleteDepAdmin,
  getCollaborationList,
  getOrgDepartment,
  getOrgDepartmentUser,
  getRecentContact,
  getSearchUser,
  setAdmin,
  setDepAdmin,
  updateCollaboration,
} from '@/api/Collaboration';
import { ReactComponent as ArrowTop } from '@/assets/images/svg/arrowTop.svg';
import { ReactComponent as DarkArrowRight } from '@/assets/images/svg/darkArrowRight.svg';
import { ReactComponent as DarkPlusSign } from '@/assets/images/svg/darkPlusSign.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { ReactComponent as TopLevel } from '@/assets/images/svg/topLevel.svg';
import { fm2 } from '@/modules/Locale';

interface CollaborationAddProps {
  data: any;
  addAdminsOrRoles: string;
  guid: string;
}
export const CollaborationAdd: React.FC<CollaborationAddProps> = ({ data, addAdminsOrRoles, guid }) => {
  const itemsAdmin: any = [
    { key: 'merger', label: fm2('ShareCollaboration.admin') },
    { key: 'remove', label: fm2('ShareCollaboration.removeManager'), danger: true },
  ];
  const itemsRoles: any = [
    { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
    { key: 'commentator', label: fm2('ShareCollaboration.comment') },
    { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
    { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
    { key: 'remove', label: fm2('ShareCollaboration.removePermission'), danger: true },
  ];

  const [recentData, setRecentData] = useState<any>([]);
  const [currentDepartment, setCurrentDepartment] = useState<any>([]);
  const [subdepartments, setSubdepartments] = useState<any>([]);
  const [departmentDataMap, setDepartmentDataMap] = useState<Record<string, any[]>>({});
  const [inputValue, setInputValue] = useState<string>('');
  const [switchCheckedValid, setSwitchCheckedValid] = useState<boolean>(true);
  const [clickDepId, setClickDepId] = useState<number | string>(1);
  interface SearchResultUser {
    user: {
      id: string;
      name: string;
      avatar?: string;
      email?: string;
    };
    department?: never;
  }
  interface SearchResultDepartment {
    department: {
      id: string;
      name: string;
    };
    user?: never;
  }
  type SearchResultItem = SearchResultUser | SearchResultDepartment;
  const [searchResult, setSearchResult] = useState<SearchResultItem[]>([]);

  // 添加管理者，回显 管理者的信息
  const getMergerAccount = async (Arr: any[], callback?: (updatedArr: any[]) => void) => {
    const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
    Arr?.forEach((el: any) => {
      el.isAdmin = false;
      getUserData?.data?.admins?.forEach((e: { id: any; isInherited: any }) => {
        if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
          el.isAdmin = true;
          el.isInherited = e.isInherited;
        }
      });
    });
    if (callback) {
      callback([...Arr]);
    }
  };
  // 添加协作者 回显
  const getMergerRoles = async (Arr: any[], callback?: (updatedArr: any[]) => void) => {
    const getUserData = await getCollaborationList(guid, { includeInherited: false, includeAdmin: true });
    Arr.forEach((el: any) => {
      getUserData?.data?.admins?.forEach((e: { id: any; isInherited: any }) => {
        if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
          el.isAdmin = true;
          el.isInherited = e.isInherited;
        }
      });
      el.role = null;
      getUserData?.data?.roles?.forEach((e: { id: any; role: any }) => {
        if (el.id === e.id || el.user?.id === e.id || el.department?.id === e.id) {
          el.role = e.role;
        }
      });
    });
    if (callback) {
      callback([...Arr]);
    }
  };
  const getRecent = useCallback(() => {
    getRecentContact().then((res: any) => {
      if (addAdminsOrRoles === 'admins') {
        getMergerAccount(res.data.results, setRecentData);
      } else {
        getMergerRoles(res.data.results, setRecentData);
      }
    });
  }, [addAdminsOrRoles]);
  const getDepartment = (id: string) => {
    if (departmentDataMap[id]) return;
    getOrgDepartment(id).then((res: any) => {
      const currentDept = res.data.currentDepartment;
      const subDepts = res.data.subdepartments;
      getOrgDepartmentUser(currentDept.id, { perPage: 500, page: 1 }).then((userRes: any) => {
        const users = userRes.data.users || [];
        const updatedList = [...subDepts, ...users];
        if (id === '1') {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount([currentDept], setCurrentDepartment);
            getMergerAccount(updatedList, setSubdepartments);
          } else {
            getMergerRoles([currentDept], setCurrentDepartment);
            getMergerRoles(updatedList, setSubdepartments);
          }
        } else {
          if (addAdminsOrRoles === 'admins') {
            setClickDepId(id);
            getMergerAccount(updatedList, (data) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [id]: data,
              }));
            });
          } else {
            getMergerRoles(updatedList, (data) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [id]: data,
              }));
            });
          }
        }
      });
    });
  };
  const onChangeTab = (key: string) => {
    if (key === 'recently') {
      getRecent();
    }
    if (key === 'department') {
      getDepartment('1');
    }
  };
  const handleCollapseTwo = (id: string) => {
    getDepartment(id);
  };

  const addUserAdmin = async (props: any) => {
    if (props.itemData.isAdmin) return;
    let res;
    if (props.type === 'department' || props.itemData.department) {
      res = await setDepAdmin(guid, props.itemData.id || props.itemData.department?.id, {
        needNotice: switchCheckedValid,
      });
    } else {
      res = await setAdmin(guid, props.itemData.id || props.itemData.user.id, { needNotice: switchCheckedValid });
    }
    if (res.status === 204) {
      if (props.itemData.department || props.itemData.user) {
        getMergerAccount(searchResult, setSearchResult);
      } else {
        getMergerAccount(recentData, setRecentData);
        if (props.itemData.id === 1) {
          getMergerAccount(currentDepartment, setCurrentDepartment);
        } else {
          getMergerAccount(subdepartments, setSubdepartments);
          const data = departmentDataMap[clickDepId];
          if (data) {
            getMergerAccount(data, (updatedData) => {
              setDepartmentDataMap((prev) => ({
                ...prev,
                [clickDepId]: updatedData,
              }));
            });
          }
        }
      }
      message.success(fm2('ShareCollaboration.addSuccess'));
    }
  };
  const dropdownChange = async (info: { key: string }, props: any) => {
    if (info.key === 'remove') {
      let res;
      if (props.type === 'department' || props.itemData.department) {
        res = await deleteDepAdmin(guid, props.itemData.id || props.itemData.department?.id);
      } else {
        res = await deleteAdmin(guid, props.itemData.id || props.itemData.user.id);
      }
      if (res.status === 204) {
        if (props.itemData.department || props.itemData.user) {
          getMergerAccount(searchResult, setSearchResult);
        } else {
          getMergerAccount(recentData, setRecentData);
          if (props.itemData.id === 1) {
            getMergerAccount(currentDepartment, setCurrentDepartment);
          } else {
            getMergerAccount(subdepartments, setSubdepartments);
            const data = departmentDataMap[clickDepId];
            if (data) {
              getMergerAccount(data, (updatedData) => {
                setDepartmentDataMap((prev) => ({
                  ...prev,
                  [clickDepId]: updatedData,
                }));
              });
            }
          }
        }
        message.success(fm2('ShareCollaboration.removeSuccess'));
      }
    }
  };

  //管理者操作权限
  const DropdownMenuItemAdmin = (props: any) => {
    return (
      <Dropdown
        menu={{
          items: itemsAdmin.map((item: any) => ({
            key: item.key,
            danger: item.danger,
            label: (
              <div className="dropdownItem">
                <div>{item.label}</div>
                {item.key === 'merger' && <CheckOutlined />}
              </div>
            ),
          })),
          onClick: (info) => dropdownChange(info, props),
          style: { width: 200 },
        }}
        placement="bottomRight"
        trigger={props.itemData?.isAdmin ? ['click'] : []}
      >
        <Space>
          <Button
            disabled={props.itemData?.isInherited || data?.userId === props.itemData?.id}
            icon={
              props.itemData?.isInherited || props.itemData?.isAdmin || props.itemData?.role ? (
                <CaretDownOutlined />
              ) : (
                <DarkPlusSign />
              )
            }
            iconPosition="end"
            size="small"
            type="text"
            onClick={() => {
              addUserAdmin(props);
            }}
          >
            {props.itemData?.isAdmin ? fm2('ShareCollaboration.admin') : fm2('ShareCollaboration.setManager')}
          </Button>
        </Space>
      </Dropdown>
    );
  };
  const getItemName = (data: any): string => {
    const matchedItem = itemsRoles.find((item: { key: any; label: any }) => item.key === data?.role);
    if (matchedItem) {
      return matchedItem.label;
    }
    if (data?.isAdmin) {
      return fm2('ShareCollaboration.admin');
    }
    return fm2('ShareCollaboration.addPermission');
  };
  //协作者的操作
  function getTargetId(data: any): string {
    return data.id || data?.department?.id || data?.user?.id;
  }
  const dropdownChangeRole = async (info: { key: string }, props: any) => {
    const { data, type } = props;
    const isRemove = info.key === 'remove';
    const isDepartment = !data?.avatar || data?.departmentId || type === 'department';
    let res;
    if (isRemove) {
      res = isDepartment
        ? await deleteCollaborationDepartment(guid, getTargetId(data))
        : await deleteCollaboration(guid, getTargetId(data));

      if (res.status === 204) {
        message.success(
          isDepartment
            ? fm2('ShareCollaboration.deleteDepartmentSuccess')
            : fm2('ShareCollaboration.deleteUserSuccess'),
        );
      }
    } else {
      res = isDepartment
        ? await addCollaborationDepartment(guid, getTargetId(data), { role: info.key, needNotice: switchCheckedValid })
        : await updateCollaboration(guid, getTargetId(data), { role: info.key, needNotice: switchCheckedValid });

      if (res.status === 204 || res.status === 200) {
        message.success(fm2('ShareCollaboration.operationSuccess'));
      }
    }
    if (data.department || data.user) {
      getMergerRoles(searchResult, setSearchResult);
    } else {
      getMergerRoles(recentData, setRecentData);
      if (props.data.id === 1) {
        getMergerRoles(currentDepartment, setCurrentDepartment);
      } else {
        getMergerRoles(subdepartments, setSubdepartments);
      }
    }
  };

  // 协作者操作权限
  const DropdownMenuItemRole = (props: any) => {
    return (
      <Dropdown
        menu={{
          items: itemsRoles.map((roleItem: any) => ({
            key: roleItem.key,
            danger: roleItem.danger,
            label: props.data?.role ? (
              <div className="dropdownItem">
                <div>{roleItem.label}</div>
                {props.data?.role === roleItem.key && <CheckOutlined />}
              </div>
            ) : roleItem.key === 'remove' ? (
              <div className="removeDivNone" />
            ) : (
              <div className="dropdownItem">
                <div>{roleItem.label}</div>
              </div>
            ),
          })),
          onClick: (info) => dropdownChangeRole(info, props),
        }}
        placement="bottomRight"
        trigger={['click']}
      >
        <Space>
          <Button
            disabled={props.data?.isAdmin}
            icon={props.data?.isAdmin || props.data?.role ? <CaretDownOutlined /> : <DarkPlusSign />}
            iconPosition="end"
            size="small"
            type="text"
          >
            {getItemName(props.data)}
          </Button>
        </Space>
      </Dropdown>
    );
  };

  const debouncedSearch = useRef(
    debounce((value) => {
      if (value.trim() !== '') {
        getSearchUser({
          limit: 100,
          keyword: value,
          filter: {
            user: {
              includeRecentContact: true,
              includeTeamMember: true,
            },
            department: {},
            group: {},
          },
          fetchFileRoleByFileGuid: guid,
        }).then((res) => {
          if (addAdminsOrRoles === 'admins') {
            getMergerAccount(res.data?.results, setSearchResult);
          } else {
            getMergerRoles(res.data?.results, setSearchResult);
          }
        });
      }
    }, 300),
  ).current;
  //组织结构第归树部门
  const renderCollapseItems = (subdepartments: any) => {
    return (
      <div style={{ maxHeight: '256px', overflowY: 'auto' }}>
        {subdepartments.map((item: any) =>
          item?.handoverMenu ? (
            <div key={item.id} className="listItem">
              <div className="itemLeft">
                <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                <div className="itemRight">
                  <div className="itemName">{item.name}</div>
                  <Tooltip placement="top" title={item.email}>
                    <div className="emailText">{item.email}</div>
                  </Tooltip>
                </div>
              </div>
              {addAdminsOrRoles === 'admins' ? (
                <DropdownMenuItemAdmin itemData={item} />
              ) : (
                <DropdownMenuItemRole data={item} />
              )}
            </div>
          ) : (
            <div key={item.id}>
              <Collapse
                accordion
                collapsible="icon"
                expandIcon={({ isActive }) => <span>{isActive ? <DarkArrowRight /> : <ArrowTop />}</span>}
                ghost={true}
                items={[
                  {
                    key: item.id,
                    label: (
                      <div className="listItem">
                        <div className="itemLeft">
                          <Avatar icon={<Organization />} size={28} />
                          <div className="itemRight">
                            <div className="itemNameDep">{item.name}</div>
                          </div>
                        </div>
                        {addAdminsOrRoles === 'admins' ? (
                          <DropdownMenuItemAdmin itemData={item} type={'department'} />
                        ) : (
                          <DropdownMenuItemRole data={item} type={'department'} />
                        )}
                      </div>
                    ),
                    children: departmentDataMap[item.id] ? renderCollapseItems(departmentDataMap[item.id]) : null,
                  },
                ]}
                onChange={(keys) => {
                  keys.forEach((key) => handleCollapseTwo(key));
                }}
              />
            </div>
          ),
        )}
      </div>
    );
  };
  const tabItems: TabsProps['items'] = [
    {
      key: 'recently',
      label: fm2('ShareCollaboration.recent'),
      children: (
        <div className="tabboxMaxH">
          {recentData.map((item: any) => {
            return (
              <div key={item.id} className="listItem">
                <div className="itemLeft">
                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                  <div className="itemRight">
                    <div className="itemName">{item.name}</div>
                    <Tooltip placement="top" title={item.email}>
                      <div className="emailText">{item.email}</div>
                    </Tooltip>
                  </div>
                </div>
                {addAdminsOrRoles === 'admins' ? (
                  <DropdownMenuItemAdmin itemData={item} />
                ) : (
                  <DropdownMenuItemRole data={item} />
                )}
              </div>
            );
          })}
        </div>
      ),
    },
    {
      key: 'department',
      label: fm2('ShareCollaboration.organization'),
      children: (
        <Collapse
          collapsible="icon"
          defaultActiveKey={['1']}
          expandIcon={({ isActive }) => (
            <span className="ant-collapse-arrow">
              <DarkArrowRight rotate={isActive ? 0 : 90} />
            </span>
          )}
          ghost={true}
          items={[
            {
              key: '1',
              label: (
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar icon={<TopLevel />} size={28} />
                    <div className="itemRight">
                      <div className="itemNameDep">{currentDepartment[0]?.name}</div>
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin itemData={currentDepartment[0]} type={'department'} />
                  ) : (
                    <DropdownMenuItemRole data={currentDepartment[0]} type={'department'} />
                  )}
                </div>
              ),
              children: renderCollapseItems(subdepartments),
            },
          ]}
        />
      ),
    },
  ];
  useEffect(() => {
    debouncedSearch(inputValue);
    return () => debouncedSearch.cancel();
  }, [inputValue, debouncedSearch]);
  useEffect(() => {
    getRecent();
  }, [getRecent]);
  return (
    <div className="collaborationAdd">
      <div className="modalBodyInput">
        <Input
          placeholder={`${fm2('ShareCollaboration.clickHereToSearchAndAdd')}${
            addAdminsOrRoles === 'admins' ? fm2('ShareCollaboration.admin') : fm2('ShareCollaboration.coauthor')
          }`}
          prefix={<Search />}
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
        />
      </div>
      {inputValue ? (
        <>
          <Tabs
            defaultActiveKey="1"
            items={[
              {
                key: '1',
                label: fm2('ShareCollaboration.searchResult'),
                children: null,
              },
            ]}
            size="small"
          />
          <div className="searchBox">
            {searchResult.map((item) => (
              <div key={item.user?.id || item.department?.id}>
                <div className="listItem">
                  <div className="itemLeft">
                    <Avatar
                      icon={item.department ? <Organization /> : <UserOutlined />}
                      size={28}
                      src={item.user?.avatar}
                    />
                    <div className="itemRight">
                      <div className="itemName">{item.user?.name || item.department?.name}</div>
                      {item.user?.email && (
                        <Tooltip placement="top" title={item.user?.email}>
                          <div>{item.user?.email}</div>
                        </Tooltip>
                      )}
                    </div>
                  </div>
                  {addAdminsOrRoles === 'admins' ? (
                    <DropdownMenuItemAdmin itemData={item} />
                  ) : (
                    <DropdownMenuItemRole data={item} />
                  )}
                </div>
              </div>
            ))}
          </div>
        </>
      ) : (
        <Tabs className="itemTabs" defaultActiveKey="recently" items={tabItems} size="small" onChange={onChangeTab} />
      )}
      <div className="addBottom">
        <Switch
          checked={switchCheckedValid}
          className="switchMr"
          size="small"
          onChange={(checked) => {
            setSwitchCheckedValid(checked);
          }}
        />
        <span>{fm2('ShareCollaboration.sendNotificationToTheOther')}</span>
      </div>
    </div>
  );
};
