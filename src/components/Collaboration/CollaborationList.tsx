import { CaretDownOutlined, CheckOutlined, PlusCircleOutlined, UserOutlined } from '@ant-design/icons';
import { Avatar, Button, Collapse, Dropdown, message, Space, Tooltip } from 'antd';
import { useEffect, useState } from 'react';

import {
  deleteAdmin,
  deleteCollaboration,
  deleteCollaborationDepartment,
  deleteDepAdmin,
  getCollaborationList,
  updateCollaboration,
  updateCollaborationDepartment,
  updateParentCollaboration,
} from '@/api/Collaboration';
import listEmpty from '@/assets/images/common/listEmpty.png';
import { ReactComponent as BlueBottom } from '@/assets/images/svg/blueBottom.svg';
import { ReactComponent as BlueRight } from '@/assets/images/svg/blueRight.svg';
import { ReactComponent as CollaborativeArrow } from '@/assets/images/svg/collaborativeArrow.svg';
import { ReactComponent as Organization } from '@/assets/images/svg/organization.svg';
import { ReactComponent as SuperiorOrganization } from '@/assets/images/svg/superiorOrganization.svg';
import { fm2 } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';

interface CollaborationListProps {
  data: any;
  guid: string;
  parentId?: number;
  parentRole: string;
  getUserList: () => void;
  CollaborationAdmins: any[];
  CollaborationRoles: any[];
  setAddOpen: (open: boolean) => void;
  setAddAdminsOrRoles: (type: string) => void;
}
export const CollaborationList: React.FC<CollaborationListProps> = ({
  data,
  guid,
  parentId,
  parentRole,
  getUserList,
  CollaborationAdmins,
  CollaborationRoles,
  setAddOpen,
  setAddAdminsOrRoles,
}) => {
  const items = [
    { key: 'inherited', label: fm2('ShareCollaboration.inheritPermission') },
    { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
    { key: 'commentator', label: fm2('ShareCollaboration.comment') },
    { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
    { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
  ];
  const items1 = [
    { key: 'reader', label: fm2('ShareCollaboration.readOnly') },
    { key: 'commentator', label: fm2('ShareCollaboration.comment') },
    { key: 'editor', label: fm2('ShareCollaboration.commentAndEdit') },
    { key: 'none', label: fm2('ShareCollaboration.forbidAccess') },
    { key: 'remove', label: fm2('ShareCollaboration.removePermission'), danger: true },
  ];

  const [parentRoleStatus, setParentRoleStatus] = useState(parentRole);
  const [isAdmin, setIsAdmin] = useState(false);
  const [collapseOpenList, setCollapseOpenList] = useState<string[]>([]);
  const [inheritedUserList, setInheritedUserList] = useState<any[]>([]);
  const meId = useMeStore((state) => state.me.id);
  const getItemName = (itemsObj: any[], item: any) => {
    const foundItem = itemsObj.find((e: any) => e.key === item);
    return foundItem ? foundItem.label : '';
  };
  const dropdownChangePerent = (info: { key: string }) => {
    updateParentCollaboration(guid, { parentRole: info.key }).then((res) => {
      if (res.status === 200) {
        setParentRoleStatus(res.data.parentRole);
        getCollaborationList(guid, { includeInherited: true, includeAdmin: false, includeSelf: false }).then((res) => {
          setInheritedUserList(res.data.roles);
          getUserList();
          message.success(fm2('ShareCollaboration.modifySuccess'));
        });
      }
    });
  };
  const dropdownChange = (info: { key: string }, item?: any) => {
    if (info.key === 'remove') {
      if (item.departmentId) {
        deleteCollaborationDepartment(guid, item.id).then((resdep) => {
          if (resdep.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.deleteSuccess'));
          }
        });
      } else {
        deleteCollaboration(guid, item.id).then((res) => {
          if (res.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      }
    } else {
      if (item.departmentId) {
        updateCollaborationDepartment(guid, item.id, { role: info.key, needNotice: true }).then((resdep) => {
          if (resdep.status === 204) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      } else {
        updateCollaboration(guid, item.id, { role: info.key }).then((res) => {
          if (res.status === 200) {
            getUserList();
            message.success(fm2('ShareCollaboration.modifySuccess'));
          }
        });
      }
    }
  };

  const handleCollapse = (key: string) => {
    if (!collapseOpenList.includes('superior')) {
      getCollaborationList(guid, { includeInherited: true, includeAdmin: false, includeSelf: false }).then((res) => {
        setInheritedUserList(res.data.roles);
      });
    }
    setCollapseOpenList(() => {
      if (collapseOpenList.includes(key)) {
        return collapseOpenList.filter((item) => item !== key);
      } else {
        return [...collapseOpenList, key];
      }
    });
  };
  const editCollaboration = (key: string, item?: any) => {
    if (meId === item.id) {
      return key !== 'remove';
    }
  };
  const handleEnterAddRoles = () => {
    if (isAdmin || data?.role === 'editor') {
      setAddOpen(true);
      setAddAdminsOrRoles('roles');
    } else {
      message.error(fm2('ShareCollaboration.onlyManagerCanAddCoauthor'));
    }
  };
  const handleEnterAddAdmins = () => {
    if (isAdmin) {
      setAddOpen(true);
      setAddAdminsOrRoles('admins');
    } else {
      message.error(fm2('ShareCollaboration.onlyManagerCanAddManager'));
    }
  };
  useEffect(() => {
    getUserList();
  }, []);
  useEffect(() => {
    setIsAdmin(data?.isAdmin || data?.isFileAdmin);
  }, [data]);
  return (
    <div className="collaborationList">
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.coauthor')}</div>
        <div className="listTitleRight" onClick={handleEnterAddRoles}>
          <PlusCircleOutlined />
          {fm2('ShareCollaboration.addCoauthor')}
        </div>
      </div>
      <div className="listBox marginBottom16">
        {CollaborationRoles.length > 0 || parentId ? (
          <>
            {parentId && (
              <Collapse
                activeKey={collapseOpenList}
                bordered={false}
                expandIcon={() => {
                  return '';
                }}
                items={[
                  {
                    key: 'superior',
                    label: (
                      <div className="listItem">
                        <div className="itemLeft">
                          <SuperiorOrganization />
                          <span className="superOrg">{fm2('ShareCollaboration.parentCoauthor')}</span>
                          <div className="collapseBtn" onClick={() => handleCollapse('superior')}>
                            {collapseOpenList.includes('superior') ? (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.collapse')}</span>
                                <BlueBottom />
                              </div>
                            ) : (
                              <div className="collapseClick">
                                <span>{fm2('ShareCollaboration.expand')}</span>
                                <BlueRight />
                              </div>
                            )}
                          </div>
                        </div>
                        <Dropdown
                          menu={{
                            items: items.map((roleItem) => ({
                              key: roleItem.key,
                              label: (
                                <div className="dropdownItem">
                                  <div>{roleItem.label}</div>
                                  {parentRoleStatus === roleItem.key && <CheckOutlined />}
                                </div>
                              ),
                            })),
                            onClick: (info) => dropdownChangePerent(info),
                          }}
                          placement="bottomRight"
                          trigger={['click']}
                        >
                          <Space className="spaceCollaboration">
                            <Button
                              disabled={!isAdmin && data?.role !== 'editor'}
                              icon={<CollaborativeArrow />}
                              iconPosition="end"
                              size="small"
                              type="text"
                            >
                              {getItemName(items, parentRoleStatus)}
                            </Button>
                          </Space>
                        </Dropdown>
                      </div>
                    ),
                    children: (
                      <div>
                        {inheritedUserList.map((item) => (
                          <div key={item.id} className="listItem">
                            <div className="itemLeft">
                              {item.departmentId ? (
                                <>
                                  <Organization />
                                  <div className="ellipsis">{item.name}</div>
                                </>
                              ) : (
                                <>
                                  <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                                  <div className="itemRight">
                                    <div className="itemName">{item.name}</div>
                                    <Tooltip placement="top" title={item.email}>
                                      <div className="emailText">{item.email}</div>
                                    </Tooltip>
                                  </div>
                                </>
                              )}
                            </div>
                            <Space className="spaceCollaboration">
                              <span className="noClick">{getItemName(items, item.role)}</span>
                            </Space>
                          </div>
                        ))}
                      </div>
                    ),
                  },
                ]}
              />
            )}
            {CollaborationRoles.map((item) => {
              return (
                <div key={item.id} className="listItem">
                  <div className="itemLeft">
                    {item.departmentId ? (
                      <>
                        <Organization />
                        <div className="ellipsis">{item.name}</div>
                      </>
                    ) : (
                      <>
                        <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                        <div className="itemRight">
                          <div className="itemName">{item.name}</div>
                          <Tooltip placement="top" title={item.email}>
                            <div className="emailText">{item.email}</div>
                          </Tooltip>
                        </div>
                      </>
                    )}
                  </div>
                  <Dropdown
                    menu={{
                      items: items1.map((roleItem) => ({
                        key: roleItem.key,
                        danger: roleItem.danger,
                        label: (
                          <div className="dropdownItem">
                            <div>{roleItem.label}</div>
                            {item.role === roleItem.key && <CheckOutlined />}
                          </div>
                        ),
                        disabled: editCollaboration(roleItem.key, item),
                      })),
                      onClick: (info) => dropdownChange(info, item),
                    }}
                    placement="bottomRight"
                    trigger={['click']}
                  >
                    <Space className="spaceCollaboration">
                      <Button
                        disabled={!isAdmin && data?.role !== 'editor'}
                        icon={<CaretDownOutlined />}
                        iconPosition="end"
                        size="small"
                        type="text"
                      >
                        {getItemName(items1, item.role)}
                      </Button>
                    </Space>
                  </Dropdown>
                </div>
              );
            })}
          </>
        ) : (
          <div className="listEmpty">
            <img alt="" src={listEmpty} />
          </div>
        )}
      </div>
      <div className="listTitle">
        <div>{fm2('ShareCollaboration.admin')}</div>
        <div className="listTitleRight" onClick={handleEnterAddAdmins}>
          <PlusCircleOutlined />
          {fm2('ShareCollaboration.addManager')}
        </div>
      </div>
      <div className="listBox">
        {CollaborationAdmins.map((item) => {
          return (
            <div key={item.id} className="listItem">
              <div className="itemLeft">
                {item.departmentId ? (
                  <>
                    <Organization />
                    <div className="ellipsis">{item.name}</div>
                  </>
                ) : (
                  <>
                    <Avatar icon={<UserOutlined />} size={28} src={item.avatar} />
                    <div className="itemRight">
                      <div className="itemName">{item.name}</div>
                      <Tooltip placement="top" title={item.email}>
                        <div className="emailText">{item.email}</div>
                      </Tooltip>
                    </div>
                  </>
                )}
              </div>
              <Dropdown
                menu={{
                  items: [
                    {
                      key: 'merger',
                      label: (
                        <div className="dropdownItem">
                          <div>{fm2('ShareCollaboration.admin')}</div>
                          <CheckOutlined />
                        </div>
                      ),
                    },
                    {
                      key: 'removeMerger',
                      danger: true,
                      label: <div>{fm2('ShareCollaboration.removeManager')}</div>,
                      onClick: () => {
                        if (item.departmentId) {
                          deleteDepAdmin(guid, item.id).then((res) => {
                            if (res.status === 204) {
                              getUserList();
                              message.success(fm2('ShareCollaboration.removeManagerSuccess'));
                            }
                          });
                        } else {
                          deleteAdmin(guid, item.id).then((res) => {
                            if (res.status === 204) {
                              getUserList();
                              message.success(fm2('ShareCollaboration.removeManagerSuccess2'));
                            }
                          });
                        }
                      },
                    },
                  ],
                }}
                placement="bottomRight"
                trigger={['click']}
              >
                <Space className="spaceCollaboration">
                  <Button
                    disabled={
                      !isAdmin || CollaborationAdmins.length === 1 || item.isInherited || data?.userId === item.id
                    }
                    icon={<CaretDownOutlined />}
                    iconPosition="end"
                    size="small"
                    type="text"
                  >
                    {fm2('ShareCollaboration.admin')}
                  </Button>
                </Space>
              </Dropdown>
            </div>
          );
        })}
      </div>
    </div>
  );
};
