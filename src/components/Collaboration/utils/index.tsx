import { CheckCircleOutlined } from '@ant-design/icons';
import { Toast } from 'antd-mobile';

import { fm2 } from '@/modules/Locale';

export const copyLinkUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      Toast.show({
        icon: <CheckCircleOutlined />,
        content: fm2('ShareCollaboration.copySuccess'),
      });
    })
    .catch(() => {
      Toast.show({
        icon: 'error',
        content: fm2('ShareCollaboration.copyFail'),
      });
    });
};

export const handleCopyLinkShare = (copyUrl: string) => {
  if (copyUrl) {
    copyLinkUrl(copyUrl);
  }
};

export const copyLink = (data?: { url: string; name: string }) => {
  if (data) {
    return `${new URL(data.url, window.location.href).href}/《${data.name}》`;
  } else {
    return '';
  }
};
