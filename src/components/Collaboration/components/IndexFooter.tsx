import type { MenuProps } from 'antd';
import { Button, Dropdown, QRCode, Tooltip } from 'antd';

import { ReactComponent as Link24 } from '@/assets/images/svg/link24.svg';
import { ReactComponent as Qrcode } from '@/assets/images/svg/qrcode.svg';
import { ReactComponent as SettingIcon } from '@/assets/images/svg/settingIcon.svg';
import { fm2 } from '@/modules/Locale';

import { copyLinkUrl } from '.';

interface DataProps {
  role?: string;
}
interface FooterProps {
  data?: DataProps | null;
  fullUrl?: string;
  addOpen: boolean;
  linkCopyBtn: boolean;
  listOpen: boolean;
}

export const IndexFooter: React.FC<FooterProps> = ({ data, fullUrl, addOpen, linkCopyBtn, listOpen }) => {
  const items: MenuProps['items'] = [
    {
      key: 'qrCode',
      label: <QRCode type="svg" value={fullUrl || ''} />,
    },
  ];

  const handleCopyLinkShare = () => {
    if (fullUrl) {
      copyLinkUrl(fullUrl);
    }
  };

  return (
    <div className="shareFooterWrapper">
      {(data?.role && data?.role === 'none') || (!listOpen && !addOpen) ? (
        <div className="shareFooter">
          <div key="qrCodeBox" className="qrCodeBox">
            <div className="qrLeft">
              <div className="qrTitle">{fm2('ShareCollaboration.shareMethod')}</div>
              <Tooltip title={fm2('ShareCollaboration.qrCodeShare')}>
                <Dropdown menu={{ items }} placement="top" trigger={['click']}>
                  <Button className="qrCode" icon={<Qrcode />} />
                </Dropdown>
              </Tooltip>
              {data?.role && data?.role !== 'none' && linkCopyBtn && (
                <Tooltip title={fm2('ShareCollaboration.copyLink')}>
                  <Button icon={<Link24 />} onClick={handleCopyLinkShare} />
                </Tooltip>
              )}
            </div>
            {data?.role && data?.role !== 'none' && (
              <div className="qrRight">
                <Button icon={<SettingIcon />} type="text">
                  {fm2('ShareCollaboration.setPermission')}
                </Button>
              </div>
            )}
          </div>
        </div>
      ) : null}
    </div>
  );
};
