import classNames from 'classnames';

import type { CollaborationInfo } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { useDisclosure } from '@/hooks/useDisclosure';

import { usePremission } from '../../hooks/usePremission';
import styles from './ShareAction.less';
import { ShareControll } from './ShareControll';

type ShareContentProps = {
  title: string;
  children?: React.ReactNode;
};

export function ShareContentDefault({ title, children }: ShareContentProps) {
  return (
    <div className={styles.shareActionContent}>
      <div className={styles.shareActionContentTitleDefault}>{title}</div>
      <div className={styles.shareActionContentText}>{children}</div>
    </div>
  );
}

export function ShareContentMedium({ title, children }: ShareContentProps) {
  return (
    <div className={styles.shareActionContent}>
      <div className={styles.shareActionTitle}>{title}</div>
      {children && <div className={styles.shareActionContentText}>{children}</div>}
    </div>
  );
}
export function ShareSettings({
  title,
  children,
  highlightText,
  disable,
}: {
  title: string;
  highlightText: string;
  children?: React.ReactNode;
  disable?: boolean;
}) {
  return (
    <div
      className={classNames(styles.shareSettings, {
        [styles.shareSettingsDisable]: disable,
      })}
    >
      <div className={styles.shareSettingsContent}>
        <div className={styles.shareSettingsTitle}>{title}</div>
        <span className={styles.shareSettingsTextSpan}>{highlightText}</span>
      </div>
      {children}
    </div>
  );
}

// TODO: 增加权限判断，显示不同文案
function ShareOpenContent({ data }: { data?: CollaborationInfo }) {
  console.log(data);

  return (
    <ShareContentMedium title="链接分享已开启">
      <span>
        企业内获得链接的人<span className={styles.shareActionTextSpan}>只能阅读</span>
      </span>
    </ShareContentMedium>
  );
}

type ShareActionProps = {
  data?: CollaborationInfo;
  toggleParentVisible: () => void;
};

export function ShareAction({ data, toggleParentVisible }: ShareActionProps) {
  const { isShareLinkOpen } = usePremission(data);
  const { isOpen, open, close, toggle } = useDisclosure(false);

  function handleClick() {
    toggleParentVisible();
    open();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        {isShareLinkOpen ? (
          <ShareOpenContent data={data} />
        ) : (
          <ShareContentMedium title="链接分享已关闭">文件的协作者和管理员仍可访问</ShareContentMedium>
        )}
        <ArrowRight />
      </div>
      <ShareControll toggle={toggle} visible={isOpen} onClose={handleClose} />
    </>
  );
}
