import type { SwitchProps } from 'antd-mobile';
import { Radio, Switch } from 'antd-mobile';
import { useState } from 'react';

import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as Refresh } from '@/assets/images/svg/refresh-no-color.svg';
import { useDisclosure } from '@/hooks/useDisclosure';

import { ShareContentDefault, ShareContentMedium } from './ShareAction';
import styles from './ShareAction.less';
import { ShareSettingsActionSheet } from './ShareSettingsActionSheet';

export function CustomSwitch(props: SwitchProps) {
  return (
    <Switch
      {...props}
      style={{
        '--checked-color': 'var(--theme-link-button-color)',
        '--height': '24px',
        '--width': '42px',
      }}
    />
  );
}

export function LinkSharingSwitch({ checked, onChange }: { checked: boolean; onChange: (open: boolean) => void }) {
  return (
    <div className={styles.shareAction}>
      {checked ? (
        <ShareContentMedium title="链接分享已开启" />
      ) : (
        <ShareContentDefault title="链接分享已关闭">文件的协作者和管理员仍可访问</ShareContentDefault>
      )}
      <CustomSwitch checked={checked} onChange={onChange} />
    </div>
  );
}

export function ShareSettings({ toggleParentVisible }: { toggleParentVisible: () => void }) {
  const { isOpen, open, close } = useDisclosure(false);

  function handleClick() {
    open();
    toggleParentVisible();
  }

  function handleClose() {
    close();
    toggleParentVisible();
  }

  return (
    <>
      <div className={styles.shareAction} onClick={handleClick}>
        <ShareContentMedium title="分享设置">
          企业内获得链接的人<span className={styles.shareActionTextSpan}>只能阅读</span>
        </ShareContentMedium>
        <ArrowRight />
      </div>
      <ShareSettingsActionSheet visible={isOpen} onClose={handleClose} />
    </>
  );
}

function SmallRadio({ value, label }: { value: string; label: string }) {
  return (
    <Radio
      style={{
        '--icon-size': '12px',
        '--font-size': '12px',
        '--gap': '9px',
      }}
      value={value}
    >
      {label}
    </Radio>
  );
}

export function LinkExpirySettings() {
  const [checked, setChecked] = useState(false);

  return (
    <div className={styles.linkExpirySettings}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接有效期">{checked ? '' : '已关闭，链接永久有效'}</ShareContentMedium>
        <CustomSwitch checked={checked} onChange={setChecked} />
      </div>
      {checked && (
        <div className={styles.linkExpirySettingsExtra}>
          <Radio.Group defaultValue="1">
            <SmallRadio label="1天" value="1" />
            <SmallRadio label="7天" value="7" />
            <SmallRadio label="30天" value="30" />
          </Radio.Group>
        </div>
      )}
    </div>
  );
}

export function LinkPasswordSettings() {
  const [checked, setChecked] = useState(false);

  return (
    <div className={styles.linkExpirySettings}>
      <div className={styles.linkExpirySettingsContent}>
        <ShareContentMedium title="链接密码" />
        <CustomSwitch checked={checked} onChange={setChecked} />
      </div>
      {checked && (
        <div className={styles.linkExpirySettingsExtra}>
          <div className={styles.linkPassword}>密码: 123456</div>
          <div className={styles.changePassword}>
            <Refresh className={styles.refreshIcon} />
            更换密码
          </div>
        </div>
      )}
    </div>
  );
}
