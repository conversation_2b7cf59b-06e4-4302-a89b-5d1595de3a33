.collaboratorManagement {
  display: flex;
  flex-direction: column;
}

.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 11.5px;
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  position: relative;
}

.headerLeft {
  width: 24px;
  display: flex;
  justify-content: center;
  color: var(--theme-text-color-default);
}

.backIcon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  text-align: center;
  justify-content: center;
}

.title {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-text-color-default);
  position: absolute;
  left: 90px;
  right: 90px;
  display: flex;
  justify-content: center;
}

.content {
  background: var(--theme-layout-color-bg-editor);
}

.searchSection {
  padding: 10px 17px;
  background-color: var(--theme-basic-color-bg-default);
  border-bottom: 1px solid var(--theme-separator-color-lighter);
  border-top: 1px solid var(--theme-separator-color-lighter);
  margin-top: 17px;
  display: flex;
  align-items: center;
  color: var(--theme-text-color-secondary);
}

.searchIcon {
  width: 20px;
  height: 20px;
  text-align: center;
  justify-content: center;
  display: flex;
  align-items: center;
  margin-right: 10px;
}

.addOptions {
  margin-top: 20px;
  background-color: var(--theme-basic-color-bg-default);
}

.addOption {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  cursor: pointer;
  border-bottom: 1px solid var(--theme-separator-color-lighter);

  &:last-child {
    border-bottom: none;
  }
}

.addOptionLeft {
  display: flex;
  align-items: center;
}

.addOptionIcon {
  width: 32px;
  height: 32px;
}

.addOptionText {
  margin-left: 12px;
  font-size: 14px;
  color: var(--theme-text-color-default);
}

.addOptionArrow {
  width: 16px;
  height: 16px;
}

.section {
  margin-top: 16px;
  background-color: #fff;
}

.sectionHeader {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border-bottom: 1px solid #f5f5f5;
}

.sectionTitle {
  font-size: 14px;
  color: #666;
  font-weight: 500;
}

.sectionCount {
  font-size: 14px;
  color: #999;
}

.userList {
  .userItem {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 16px;
    border-bottom: 1px solid #f5f5f5;

    &:last-child {
      border-bottom: none;
    }
  }
}

.userInfo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.userAvatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.userDetails {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.userName {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.userEmail {
  font-size: 14px;
  color: #666;
}

.userRole {
  font-size: 14px;
  color: #999;
}

.emptyState {
  padding: 40px 16px;
  text-align: center;
}

.emptyText {
  font-size: 14px;
  color: #999;
}

.questionIcon {
  color: var(--theme-text-color-disabled);
  font-size: 14px;
}
