import { LeftOutlined, QuestionCircleFilled } from '@ant-design/icons';
import { Avatar } from 'antd-mobile';

import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as AvatarBlueIcon } from '@/assets/images/svg/avatar-blue.svg';
import { ReactComponent as LinkShareIcon } from '@/assets/images/svg/link-share.svg';
import { ReactComponent as Search } from '@/assets/images/svg/search.svg';
import { fm } from '@/modules/Locale';

import styles from './CollaboratorManagement.less';

interface CollaboratorManagementProps {
  data?: CollaboratorsData;
  onClose: () => void;
}

export function CollaboratorManagement({ data, onClose }: CollaboratorManagementProps) {
  // 根据CollaboratorsData结构，admins和roles是分开的
  const administrators = data?.admins || [];
  const collaborators = data?.roles || [];

  const handleAddFromContacts = () => {
    // TODO: 实现从内部联系人添加逻辑
    console.log('从内部联系人添加');
  };

  const handleShareByLink = () => {
    // TODO: 实现通过链接邀请逻辑
    console.log('通过链接邀请');
  };

  const handleSearch = () => {
    console.log('search');
  };

  return (
    <div className={styles.collaboratorManagement}>
      {/* 标题栏 */}
      <div className={styles.header}>
        <div className={styles.headerLeft}>
          <LeftOutlined className={styles.backIcon} height={24} width={24} onClick={onClose} />
        </div>
        <div className={styles.title}>协作者</div>
      </div>

      <div className={styles.content}>
        {/* 搜索框 */}
        <div className={styles.searchSection} onClick={handleSearch}>
          <div className={styles.searchIcon}>
            <Search />
          </div>
          <span className={styles.searchPlaceholder}>输入姓名/邮箱/手机/部门，搜索协作者</span>
        </div>

        {/* 添加协作者选项 */}
        <div className={styles.addOptions}>
          <div className={styles.addOption} onClick={handleAddFromContacts}>
            <div className={styles.addOptionLeft}>
              <AvatarBlueIcon className={styles.addOptionIcon} />
              <span className={styles.addOptionText}>{fm('ShareCollaboration.addFromContacts')}</span>
            </div>
            <ArrowRight className={styles.addOptionArrow} />
          </div>

          <div className={styles.addOption} onClick={handleShareByLink}>
            <div className={styles.addOptionLeft}>
              <LinkShareIcon className={styles.addOptionIcon} />
              <span className={styles.addOptionText}>{fm('ShareCollaboration.shareByLink')}</span>
            </div>
            <ArrowRight className={styles.addOptionArrow} />
          </div>
        </div>

        <div>
          <span>管理者</span>
          <QuestionCircleFilled className={styles.questionIcon} />
        </div>

        {/* 管理者部分 */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <span className={styles.sectionTitle}>{fm('ShareCollaboration.administrators')}</span>
            <span className={styles.sectionCount}>{administrators.length}</span>
          </div>

          <div className={styles.userList}>
            {administrators.map((admin) => (
              <div key={admin.id} className={styles.userItem}>
                <div className={styles.userInfo}>
                  <Avatar className={styles.userAvatar} src={admin.avatar} />
                  <div className={styles.userDetails}>
                    <div className={styles.userName}>{admin.name || admin.email}</div>
                    {admin.name && <div className={styles.userEmail}>{admin.email}</div>}
                  </div>
                </div>
                <div className={styles.userRole}>{fm('ShareCollaboration.administrator')}</div>
              </div>
            ))}
          </div>
        </div>

        {/* 协作者部分 */}
        <div className={styles.section}>
          <div className={styles.sectionHeader}>
            <span className={styles.sectionTitle}>{fm('ShareCollaboration.collaborators')}</span>
            {collaborators.length > 0 && <span className={styles.sectionCount}>{collaborators.length}</span>}
          </div>

          {collaborators.length > 0 ? (
            <div className={styles.userList}>
              {collaborators.map((collaborator) => (
                <div key={collaborator.id} className={styles.userItem}>
                  <div className={styles.userInfo}>
                    <Avatar className={styles.userAvatar} src={collaborator.avatar} />
                    <div className={styles.userDetails}>
                      <div className={styles.userName}>{collaborator.name || collaborator.email}</div>
                      {collaborator.name && <div className={styles.userEmail}>{collaborator.email}</div>}
                    </div>
                  </div>
                  <div className={styles.userRole}>{fm('ShareCollaboration.collaborator')}</div>
                </div>
              ))}
            </div>
          ) : (
            <div className={styles.emptyState}>
              <div className={styles.emptyText}>{fm('ShareCollaboration.noCollaborators')}</div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
