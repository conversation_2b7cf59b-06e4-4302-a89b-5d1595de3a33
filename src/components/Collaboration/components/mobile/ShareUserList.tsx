import { Avatar, Popup } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationList } from '@/api/Collaboration';
import type { CollaboratorsData } from '@/api/Collaboration.type';
import { ReactComponent as ArrowRight } from '@/assets/images/svg/arrowRight.svg';
import { ReactComponent as NoDataIcon } from '@/assets/images/svg/noDataIcon.svg';
import { useDisclosure } from '@/hooks/useDisclosure';
import { fm } from '@/modules/Locale';

import { CollaboratorManagement } from './CollaboratorManagement';
import styles from './ShareUserList.less';

function AvatarList({ avatars }: { avatars: CollaboratorsData['roles'] }) {
  return (
    <div className={styles['shareUserList-avatars']}>
      {avatars.slice(0, 5).map((item) => (
        <Avatar key={item.id} src={item.avatar} />
      ))}
    </div>
  );
}

export function ShareUserList({ guid }: { guid: string }) {
  const [data, setData] = useState<CollaboratorsData>();
  const { isOpen, open, close } = useDisclosure(false);

  useEffect(() => {
    if (guid) {
      getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
        setData(res.data);
      });
    }
  }, [guid]);

  const hasCollaborators = useMemo(() => data?.roles && data.roles.length > 0, [data]);

  return (
    <>
      <div className={styles.shareUserList} onClick={open}>
        <div className={styles['shareUserList-content']}>
          {hasCollaborators ? (
            <AvatarList avatars={data!.roles} />
          ) : (
            <div className={styles['shareUserList-empty']}>
              <NoDataIcon className={styles['shareUserList-empty-icon']} />
              {fm('ShareCollaboration.noRoles')}
            </div>
          )}
        </div>
        <div className={styles['shareUserList-action']}>
          {fm('ShareCollaboration.addRoles')}
          <ArrowRight />
        </div>
      </div>
      <Popup bodyStyle={{ height: '100%' }} visible={isOpen} onClose={close} onMaskClick={close}>
        <CollaboratorManagement data={data} onClose={close} />
      </Popup>
    </>
  );
}
