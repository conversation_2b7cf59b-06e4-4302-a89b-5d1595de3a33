import { ActionSheet } from 'antd-mobile';
import { useMemo } from 'react';

import styles from '../../CollaborationShareMobile.less';
import { useShareOpen } from '../../hooks/useShareOpen';
import { LinkExpirySettings, LinkPasswordSettings, LinkSharingSwitch, ShareSettings } from './ShareControllActions';

interface IProps {
  visible: boolean;
  toggle: () => void;
  onClose: () => void;
}

export function ShareControll({ visible, toggle, onClose }: IProps) {
  const { open, setOpen } = useShareOpen();

  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: <LinkSharingSwitch checked={open} onChange={setOpen} />,
      },
      ...(open
        ? [
            {
              key: 'shareSettings',
              text: <ShareSettings toggleParentVisible={toggle} />,
            },
            {
              key: 'linkExpirySettings',
              text: <LinkExpirySettings />,
            },
            {
              key: 'linkPasswordSettings',
              text: <LinkPasswordSettings />,
            },
          ]
        : []),
    ];
  }, [open, setOpen, toggle]);

  return (
    <ActionSheet
      actions={actions}
      cancelText="关闭"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
