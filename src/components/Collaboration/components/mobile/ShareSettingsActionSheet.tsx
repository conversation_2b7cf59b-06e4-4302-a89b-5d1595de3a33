import { CheckOutlined } from '@ant-design/icons';
import { ActionSheet } from 'antd-mobile';
import { useMemo } from 'react';

import styles from '../../CollaborationShareMobile.less';
import { ShareSettings } from './ShareAction';

export function ShareSettingsActionSheet({ visible, onClose }: { visible: boolean; onClose: () => void }) {
  const actions = useMemo(() => {
    return [
      {
        key: 'linkSharingSwitch',
        text: (
          <ShareSettings highlightText="只能阅读" title="企业内获得链接的人">
            <CheckOutlined />
          </ShareSettings>
        ),
      },
      {
        key: 'linkSharingSwitch1',
        text: <ShareSettings disable highlightText="可以评论" title="企业内获得链接的人" />,
      },
    ];
  }, []);

  return (
    <ActionSheet
      actions={actions}
      cancelText="取消"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={onClose}
    />
  );
}
