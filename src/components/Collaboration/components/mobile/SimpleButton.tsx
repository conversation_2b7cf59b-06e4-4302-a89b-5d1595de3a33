import { Button } from 'antd-mobile';

import type { CollaborationInfo } from '@/api/Collaboration.type';

import { copyLink, handleCopyLinkShare } from '../../utils';
import styles from './ShareAction.less';

export function SimpleButton({
  onClick,
  children,
  ...rest
}: { onClick: () => void; children: React.ReactNode } & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={styles.simple} {...rest}>
      <Button className={styles.simpleButton} fill="none" onClick={onClick}>
        {children}
      </Button>
    </div>
  );
}

export function CopyLink({ data, onClick }: { data?: CollaborationInfo; onClick: () => void }) {
  function handleClick() {
    const url = copyLink(data);
    handleCopyLinkShare(url);
    onClick();
  }

  return <SimpleButton onClick={handleClick}>复制链接</SimpleButton>;
}

export function CloseAction({
  onClick,
  title,
  ...rest
}: {
  onClick: () => void;
  title?: string;
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <SimpleButton onClick={onClick} {...rest}>
      {title || '关闭'}
    </SimpleButton>
  );
}
