import { message } from 'antd';

import { fm2 } from '@/modules/Locale';

export const copyLinkUrl = (url: string) => {
  navigator.clipboard
    .writeText(url)
    .then(() => {
      message.success(fm2('ShareCollaboration.copySuccess'));
    })
    .catch(() => {
      message.error(fm2('ShareCollaboration.copyFail'));
    });
};

export const optionsDays = [
  { value: 1, label: `1${fm2('ShareCollaboration.day')}` },
  { value: 7, label: `7${fm2('ShareCollaboration.day')}` },
  { value: 30, label: `30${fm2('ShareCollaboration.day')}` },
];

export const startCountdown = (shareModeExpiredAt: number, setRemainingTime: (time: string) => void): number => {
  const intervalId = window.setInterval(() => {
    const now = Math.floor(Date.now() / 1000);
    const remainingSeconds = shareModeExpiredAt - now;

    if (remainingSeconds <= 0) {
      clearInterval(intervalId);
      setRemainingTime('00:00:00');
      return;
    }

    const days = Math.floor(remainingSeconds / 86400);
    const hours = Math.floor((remainingSeconds % 86400) / 3600);
    const minutes = Math.floor((remainingSeconds % 3600) / 60);
    const seconds = remainingSeconds % 60;

    let formattedTime: string;

    if (days === 0) {
      formattedTime = `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    } else {
      formattedTime = `${String(days).padStart(2, '0')}${fm2('ShareCollaboration.day')}${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(seconds).padStart(2, '0')}`;
    }

    setRemainingTime(formattedTime);
  }, 1000);

  return intervalId;
};

export const stopCountdown = (intervalId: number | null): void => {
  if (intervalId) {
    clearInterval(intervalId);
  }
};
