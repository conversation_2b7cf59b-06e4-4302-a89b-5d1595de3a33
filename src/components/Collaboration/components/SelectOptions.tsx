import { fm2 } from '@/modules/Locale';

type OptionType = {
  value: string;
  label: React.ReactNode;
};

const linkInCompanyText = fm2('ShareCollaboration.linkInCompany');
const linkInInternetText = fm2('ShareCollaboration.linkInInternet');

export const optionsClose: OptionType[] = [
  {
    value: 'enterprise_readonly',
    label: (
      <>
        {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
      </>
    ),
  },
  {
    value: 'enterprise_commentable',
    label: (
      <>
        {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
      </>
    ),
  },
  {
    value: 'enterprise_editable',
    label: (
      <>
        {linkInCompanyText} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
      </>
    ),
  },
  {
    value: 'readonly',
    label: (
      <>
        {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
      </>
    ),
  },
  {
    value: 'commentable',
    label: (
      <>
        {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
      </>
    ),
  },
  {
    value: 'editable',
    label: (
      <>
        {linkInInternetText} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
      </>
    ),
  },
];

const linkInCompanyWithPassword = fm2('ShareCollaboration.linkInCompanyWithPassword');
const linkInternetWithPassword = fm2('ShareCollaboration.linkInternetWithPassword');

export const optionsOpen: OptionType[] = [
  {
    value: 'enterprise_readonly',
    label: (
      <>
        {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
      </>
    ),
  },
  {
    value: 'enterprise_commentable',
    label: (
      <>
        {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
      </>
    ),
  },
  {
    value: 'enterprise_editable',
    label: (
      <>
        {linkInCompanyWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
      </>
    ),
  },
  {
    value: 'readonly',
    label: (
      <>
        {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.readOnly')}</span>
      </>
    ),
  },
  {
    value: 'commentable',
    label: (
      <>
        {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.comment')}</span>
      </>
    ),
  },
  {
    value: 'editable',
    label: (
      <>
        {linkInternetWithPassword} <span className="selectLabel">{fm2('ShareCollaboration.commentAndEdit')}</span>
      </>
    ),
  },
];
