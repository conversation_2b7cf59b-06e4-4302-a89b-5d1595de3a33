import { ActionSheet } from 'antd-mobile';
import { useEffect, useMemo, useState } from 'react';

import { getCollaborationDetail } from '@/api/Collaboration';
import type { CollaborationInfo } from '@/api/Collaboration.type';
import { catchApiResult } from '@/api/Request';

import styles from './CollaborationShareMobile.less';
import { ShareAction } from './components/mobile/ShareAction';
import { ShareUserList } from './components/mobile/ShareUserList';
import { CopyLink } from './components/mobile/SimpleButton';

interface CollaborationShareMobileProps {
  guid: string;
  visible: boolean;
  close: () => void;
  toggle: () => void;
}

export function CollaborationShareMobile({ guid, visible, toggle, close }: CollaborationShareMobileProps) {
  const [data, setData] = useState<CollaborationInfo>();

  const actions = useMemo(() => {
    return [
      {
        key: 'userList',
        text: <ShareUserList guid={guid} />,
      },
      {
        key: 'shareAction',
        text: <ShareAction data={data} toggleParentVisible={toggle} />,
      },
      {
        key: 'copyLink',
        text: <CopyLink data={data} onClick={close} />,
      },
    ];
  }, [data, guid, close, toggle]);

  useEffect(() => {
    if (guid) {
      catchApiResult(getCollaborationDetail(guid)).then(([_, res]) => {
        if (res) {
          setData(res.data);
        }
      });
    }
  }, [guid]);

  return (
    <ActionSheet
      actions={actions}
      cancelText="关闭"
      className={styles.collaborationShareMobile}
      visible={visible}
      onClose={close}
    />
  );
}
