import './index.less';

import { Modal } from 'antd';
import { useCallback, useEffect, useState } from 'react';

import { getCollaborationDetail, getCollaborationList } from '@/api/Collaboration';

import { Collaboration } from './Collaboration';
import { CollaborationAdd } from './CollaborationAdd';
import { CollaborationList } from './CollaborationList';
import { IndexFooter } from './components/IndexFooter';
import { IndexTitle } from './components/IndexTitle';
import { Share } from './Share';
/**
 * 操作者的身份角色。
 *
 * @remarks
 * `data.role` 表示当前用户在协作系统中的角色权限。
 *
 * | 角色         | 描述                         |
 * |--------------|------------------------------|
 * | inherited    | 继承自父级/上级权限          |
 * | none         | 无协作权限                   |
 * | reader       | 只读用户                     |
 * | commentator  | 可评论者                     |
 * | editor       | 可编辑者                     |
 * | owner        | 所有者                       |
 */

/**
 * 文件协作权限。
 *
 * @remarks
 * 判断当前用户是否具有管理协作者的权限。
 *
 * @defaultValue false - 如果未定义则默认不具备权限
 */

/**
 * 分享链接开启状态（仅文件有效）。
 *
 * @remarks
 * [shareMode] 表示分享链接的状态和权限级别。
 * 仅适用于文件，文件夹和团队空间没有该字段。
 *
 * | 值                    | 描述                         |
 * |-----------------------|------------------------------|
 * | private                | 不分享（未开启分享）         |
 * | readonly               | 只读                         |
 * | commentable            | 可以评论                     |
 * | editable               | 可以编辑                     |
 * | enterprise_readonly    | 企业成员只读                 |
 * | enterprise_commentable | 企业成员可以评论             |
 * | enterprise_editable    | 企业成员可以编辑             |
 */

/**
 * 是否是管理员。
 *
 * @remarks
 * 判断当前用户是否为企业管理员或文件管理员。
 *
 * @defaultValue false - 默认不是管理员
 *
 */

interface CollaborationShareProps {
  visible: boolean;
  guid: string;
  onCancel: () => void;
  enterType?: string;
}
interface Role {
  id: number;
  avatar?: string;
}
interface CollaborationData {
  role?: string;
  url?: string;
  guid?: string;
  parentId?: number;
  parentRole?: string;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  isFolder?: boolean;
  shareMode?: string;
  name?: string;
}
const CollaborationShare: React.FC<CollaborationShareProps> = ({ visible, guid, onCancel, enterType }) => {
  const [data, setData] = useState<CollaborationData | null>(null);
  const [shareStatus, setShareStatus] = useState('');
  const [shareDisabled, setShareDisabled] = useState(false);
  const [linkCopyBtn, setLinkCopyBtn] = useState(true);
  const [fullUrl, setFullUrl] = useState('');
  const [closable, setClosable] = useState(true);
  const [enterAdd, setEnterAdd] = useState(false);
  const [CollaborationAdmins, setCollaborationAdmins] = useState<Role[]>([]);
  const [CollaborationRoles, setCollaborationRoles] = useState<Role[]>([]);
  const [constUrl, setConstUrl] = useState('');

  const getDataInfo = useCallback(() => {
    getCollaborationDetail(guid).then((res) => {
      const data = res.data;
      if (!(data?.role && data?.role !== 'none')) {
        setClosable(false);
      }
      const copyUrl = `${new URL(data.url, window.location.href).href}/《${data.name}》`;
      setConstUrl(copyUrl);
      setFullUrl(copyUrl);
      setData(data);
      //分享按钮初始状态
      if (data?.shareMode) {
        setShareStatus(data.shareMode);
      } else {
        setShareStatus('');
      }
      //分享按钮可不要点击
      if (data?.isSpace || data?.isFolder) {
        setShareDisabled(true);
      } else {
        if (data?.role === 'editor' || data?.isFileAdmin) {
          setShareDisabled(false);
        } else {
          setShareDisabled(true);
        }
      }
    });
  }, [guid]);

  const getUserList = useCallback(() => {
    getCollaborationList(guid, { includeInherited: false, includeAdmin: true }).then((res) => {
      setCollaborationAdmins(res.data.admins);
      setCollaborationRoles(res.data.roles);
    });
  }, [guid]);
  const [listOpen, setListOpen] = useState(false);
  const [addOpen, setAddOpen] = useState(false);
  const [addAdminsOrRoles, setAddAdminsOrRoles] = useState('');
  const hanleBack = () => {
    if (addOpen) {
      if (enterAdd) {
        setEnterAdd(false);
        setListOpen(false);
      } else {
        setListOpen(true);
      }
      setAddOpen(false);
    } else {
      setListOpen(false);
      setEnterAdd(false);
      getDataInfo();
    }
  };
  useEffect(() => {
    if (visible) {
      getDataInfo();
      getUserList();
    }
    setListOpen(false);
    setAddOpen(false);
    if (enterType === 'setting') {
      setListOpen(true);
      setAddOpen(true);
      setAddAdminsOrRoles('roles');
    }
  }, [visible, enterType, getDataInfo, getUserList]);
  return (
    <Modal
      centered
      destroyOnClose
      className="collaborationModal"
      closable={closable}
      footer={[
        <IndexFooter
          key="footer"
          addOpen={addOpen}
          data={data}
          fullUrl={fullUrl}
          linkCopyBtn={linkCopyBtn}
          listOpen={listOpen}
        />,
      ]}
      open={visible}
      title={
        <IndexTitle
          addAdminsOrRoles={addAdminsOrRoles}
          addOpen={addOpen}
          data={data}
          enterType={enterType}
          hanleBack={hanleBack}
          listOpen={listOpen}
        />
      }
      width={480}
      onCancel={onCancel}
    >
      {data?.role && data?.role !== 'none' ? (
        !listOpen ? (
          <Collaboration
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            constUrl={constUrl}
            data={data}
            guid={guid}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
            setEnterAdd={setEnterAdd}
            setFullUrl={setFullUrl}
            setLinkCopyBtn={setLinkCopyBtn}
            setListOpen={setListOpen}
            shareDisabled={shareDisabled}
            shareStatus={shareStatus}
          />
        ) : !addOpen ? (
          <CollaborationList
            CollaborationAdmins={CollaborationAdmins}
            CollaborationRoles={CollaborationRoles}
            data={data}
            getUserList={getUserList}
            guid={guid}
            parentId={data?.parentId === 0 ? undefined : data?.parentId}
            parentRole={data?.parentRole || ''}
            setAddAdminsOrRoles={setAddAdminsOrRoles}
            setAddOpen={setAddOpen}
          />
        ) : (
          <CollaborationAdd addAdminsOrRoles={addAdminsOrRoles} data={data} guid={guid} />
        )
      ) : (
        <Share url={data?.url || ''} />
      )}
    </Modal>
  );
};

export default CollaborationShare;
