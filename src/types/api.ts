import type { AxiosError } from 'axios';

import type { ErrorCodes } from '@/utils/request/ErrorCodeMap';

/**
 * API响应类型定义
 */
export interface ApiResponse {
  status?: number;
  data?: Record<string, unknown> | string; // data 可能是对象或字符串
}

/**
 * 文件详情数据结构
 */
export interface FileDetail {
  guid: string;
  id: string;
  name: string;
  tags?: string[];
  // 根据实际情况添加其他需要的字段
  isFolder: boolean;
  isSpace: boolean;
  isDesktop: boolean;
  type: 'newdoc' | 'modoc' | 'mosheet' | 'table' | 'presentation' | 'form' | 'img';
  subType?: string;
  starred: boolean;
  updatedAt: number;
  isAdmin?: boolean;
  isFileAdmin?: boolean;
  role?: string;

  url: string;
  parent_guid: string;
  passwordProtected?: boolean;
  shareMode?:
    | 'enterprise_readonly'
    | 'enterprise_commentable'
    | 'enterprise_editable'
    | 'readonly'
    | 'commentable'
    | 'editable'
    | 'private';
  permissions: {
    readable: boolean;
    [key: string]: any; // 其他权限字段
  };
  user: {
    id: number;
    name: string;
    avatar: string;
  };
}
export interface FileAncestors {
  id: number;
  guid: string;
  name: string;
  isFolder: boolean;
  isSpace: boolean;
  isDesktop: boolean;
}

export interface ImportFileApiResponse {
  status: number;
  message: string;
  data: {
    taskId: string;
  };
}

export interface ErrorResponse {
  tid: string;
  msg: string;
  code: ErrorCodes;
}

export interface ErrorData {
  code: number;
  msg: string;
  tid: string; // 请求唯一标识
}

export interface ResponseError extends AxiosError {
  data: ErrorData;
}
