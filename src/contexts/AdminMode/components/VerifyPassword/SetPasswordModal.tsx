import { Form, Input, Modal } from 'antd';
import type { MessageInstance } from 'antd/lib/message/interface';
import React, { useState } from 'react';

import { setAdminPassword } from '@/api/adminMode';
import type { ErrorModel } from '@/model/Admin';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { getPasswordError } from '@/utils/validate';

import type { ModalType } from './type';

const { Item } = Form;
const { Password } = Input;
const PasswordName = 'password';
const NewPassword = 'newPassword';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  changeModalType: (v: ModalType) => void;
  messageApi?: MessageInstance;
}

export const SetPasswordModal: React.FC<Props> = ({ open, changeOpen, changeModalType, messageApi }) => {
  const [form] = Form.useForm();
  const state = useAdminStore((state) => state);
  const [loading, setLoading] = useState(false);
  const unknownError = fm('Management.unknownError');
  const handleOK = async () => {
    setLoading(true);
    try {
      const formResult = await form.validateFields();
      const password = formResult[NewPassword];
      // 1. 向后端发送密码
      await setAdminPassword({ password });
      state.setAdminPasswordIsSet(true);

      // 2. 改为进入管理模式对话框
      changeModalType('confirm');
    } catch (err) {
      if ((err as ErrorModel).errorCode) {
        const msg = (err as ErrorModel)?.error || unknownError;
        messageApi?.error(msg);
      }
    }

    setLoading(false);
  };

  const [passV, setPassV] = useState('');
  const [newPassV, setNewPassV] = useState('');
  const handlePasswordChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setPassV(e.target.value);
  };
  const handleNewPasswordChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setNewPassV(e.target.value);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      okButtonProps={{
        disabled: !passV,
        loading,
      }}
      open={open}
      title={fm('Management.managementModePasswordSuccess')}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={handleOK}
    >
      <Form form={form} layout="vertical" validateTrigger={['onBlur']}>
        <Item
          help={fm('Management.passwordLength')}
          label={fm('Management.enterPassword')}
          name={PasswordName}
          rules={[
            {
              validator: (_, value) => {
                const text = getPasswordError(value);
                if (!text) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(text);
                }
              },
            },
          ]}
        >
          <Password placeholder={fm('Management.pleaseEnterPassword')} value={passV} onChange={handlePasswordChange} />
        </Item>
        <Item
          label={fm('Management.newPassword')}
          name={NewPassword}
          rules={[
            {
              validator: (_, value) => {
                const text = getPasswordError(value);
                if (value !== passV) {
                  return Promise.reject(fm('Management.inconsistentPasswords'));
                }
                if (!text) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(text);
                }
              },
            },
          ]}
        >
          <Password
            placeholder={fm('Management.pleaseEnterPasswordAgain')}
            value={newPassV}
            onChange={handleNewPasswordChange}
          />
        </Item>
      </Form>
    </Modal>
  );
};
