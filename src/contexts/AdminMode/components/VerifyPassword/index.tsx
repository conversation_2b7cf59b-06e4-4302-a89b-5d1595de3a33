import './index.less';

import type { MessageInstance } from 'antd/lib/message/interface';
import classNames from 'classnames';
import React, { useContext, useState } from 'react';

import { fm } from '@/modules/Locale';

import { ModalContext } from '../../../ModalProvider';
import { AdminModeContext } from '../..';
import { InputPasswordModal } from './InputPasswordModal';
import { SetPasswordModal } from './SetPasswordModal';
import type { ModalType } from './type';
interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  messageApi?: MessageInstance;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export const VerifyPasswordModal: React.FC<Props> = ({ open, changeOpen, messageApi, onSuccess, onCancel }) => {
  const modal = useContext(ModalContext);
  const { adminPasswordIsSet } = useContext(AdminModeContext);
  const [modalType, setModalType] = useState<ModalType>(adminPasswordIsSet ? 'input' : 'set');

  if (modalType === 'input') {
    return (
      <InputPasswordModal
        changeOpen={(v: boolean) => {
          changeOpen(v);
        }}
        messageApi={messageApi}
        open={open}
        onCancel={onCancel}
        onSuccess={onSuccess}
      />
    );
  } else if (modalType === 'set') {
    return (
      <SetPasswordModal
        changeModalType={(v: ModalType) => {
          setModalType(v);
        }}
        changeOpen={(v: boolean) => {
          changeOpen(v);
        }}
        messageApi={messageApi}
        open={open}
      />
    );
  } else if (modalType === 'confirm') {
    modal?.success({
      title: fm('Management.passwordEffect'),
      width: 420,
      closable: true,
      centered: true,
      okText: fm('Management.enterAdminMode'),
      content: (
        <div className={classNames('verify-password-content')}>{fm('Management.managementModePasswordSuccess')}</div>
      ),
      onOk: () => {
        setModalType('input');
      },
    });
  }
  return null;
};
