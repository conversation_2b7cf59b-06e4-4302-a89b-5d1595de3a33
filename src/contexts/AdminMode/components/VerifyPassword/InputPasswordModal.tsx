import { Form, Input, Modal } from 'antd';
import type { MessageInstance } from 'antd/lib/message/interface';
import React, { useState } from 'react';

import { sendPassword } from '@/api/adminMode';
import type { ErrorModel } from '@/model/Admin';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { getPasswordError } from '@/utils/validate';

import { getVerifyErrorText } from '../utils';

const { Item } = Form;
const { Password } = Input;
const PasswordName = 'password';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  messageApi?: MessageInstance;
  onSuccess?: () => void;
  onCancel?: () => void;
  clearSuccessCallBack?: () => void;
  clearCancelCallBack?: () => void;
}

export const InputPasswordModal: React.FC<Props> = ({
  open,
  changeOpen,
  messageApi,
  onCancel,
  onSuccess,
  clearSuccessCallBack,
  clearCancelCallBack,
}) => {
  const [form] = Form.useForm();
  const state = useAdminStore((state) => state);
  const [loading, setLoading] = useState(false);
  const unknownError = fm('Management.unknownError');
  const handleOK = async () => {
    setLoading(true);
    try {
      const formResult = await form.validateFields();
      const password = formResult[PasswordName];
      await sendPassword({
        password,
      });
      state.setAuthSuccess(true);

      // 调用成功回调
      onSuccess?.();
      // 清空成功回调
      clearSuccessCallBack?.();
      changeOpen(false);
    } catch (err: unknown) {
      if ((err as ErrorModel).errorCode) {
        const msg =
          getVerifyErrorText((err as ErrorModel).errorCode as number) || (err as ErrorModel).error || unknownError;

        messageApi?.error(msg);
      }
    }
    setLoading(false);
  };

  const [passV, setPassV] = useState('');
  const handleInputChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setPassV(e.target.value);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      okButtonProps={{
        disabled: !passV,
        loading,
      }}
      open={open}
      title={fm('Management.enterAdminModePassword')}
      width={420}
      onCancel={() => {
        // 调用取消回调
        onCancel?.();
        // 清空取消回调
        clearCancelCallBack?.();
        changeOpen(false);
      }}
      onOk={handleOK}
    >
      <Form form={form} layout="vertical" validateTrigger={['onBlur']}>
        <Item
          label={fm('Management.enterPassword')}
          name={PasswordName}
          rules={[
            {
              validator: (_, value) => {
                const text = getPasswordError(value);
                if (!text) {
                  return Promise.resolve();
                } else {
                  return Promise.reject(text);
                }
              },
            },
          ]}
        >
          <Password placeholder={fm('Management.pleaseEnterPassword')} value={passV} onChange={handleInputChange} />
        </Item>
      </Form>
    </Modal>
  );
};
