import './NoMobile.less';

import { Modal } from 'antd';
import classNames from 'classnames';
import React, { useState } from 'react';

import { fm } from '@/modules/Locale';
import { convertModalFooter } from '@/utils/modal';
interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  setToVerifyEmail: () => Promise<void>;
}

export const NoMobile: React.FC<Props> = ({ changeOpen, open, setToVerifyEmail }) => {
  const [loading, setLoading] = useState(false);

  const bindMobile = () => {
    location.href = '/profile/bindingMobile';
    changeOpen(false);
  };

  const bindEmail = async () => {
    setLoading(true);
    await setToVerifyEmail();
    changeOpen(false);
    setLoading(false);
  };

  const textData = {
    mobileAuthentication: fm('Management.mobileAuthentication'),
    noMobile: fm('Management.noMobile'),
    linkMobileL: fm('Management.linkMobile'),
    emailVerification: fm('Management.emailVerification'),
  };

  return (
    <Modal
      centered
      closable
      cancelButtonProps={{
        onClick: bindMobile,
      }}
      cancelText={textData.linkMobileL}
      footer={convertModalFooter}
      maskClosable={false}
      okButtonProps={{
        loading,
      }}
      okText={textData.emailVerification}
      open={open}
      title={textData.noMobile}
      width={420}
      onCancel={() => {
        changeOpen(false);
      }}
      onOk={bindEmail}
    >
      <div className={classNames('no-mobile')}>{textData.mobileAuthentication}</div>
    </Modal>
  );
};
