import './index.less';

import { Button, Input, Modal, Space } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';
import classNames from 'classnames';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { fireCaptchaCode, sendCaptchaCode } from '@/api/adminMode';
import type { ErrorModel } from '@/model/Admin';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { useMeStore } from '@/store/Me';

import { getVerifyErrorText } from '../utils';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  messageApi?: MessageInstance;
  onSuccess?: () => void;
  onCancel?: () => void;
  clearSuccessCallBack?: () => void;
  clearCancelCallBack?: () => void;
}

const CountDownNum = 60;

export const VerifyMobileCodeModal: React.FC<Props> = ({
  open,
  changeOpen,
  messageApi,
  onSuccess,
  onCancel,
  clearSuccessCallBack,
  clearCancelCallBack,
}) => {
  const state = useAdminStore((state) => state);
  const me = useMeStore((state) => state.me);

  const mobile = me?.mobileAccount?.replace(/(\d?)\d{4}(\d{4})$/, '$1****$2') || '';
  const [voiceCountDownNum, setVoiceCountDownNum] = useState(0);
  const [messageCountDownNum, setMessageCountDownNum] = useState(0);

  const msgTimer = useRef<string | number | NodeJS.Timeout | undefined>(undefined);
  const voiceTimer = useRef<string | number | NodeJS.Timeout | undefined>(undefined);
  const timer = useRef<string | number | NodeJS.Timeout | undefined>(undefined);
  const [captcha, setCaptcha] = useState('');
  const [loading, setLoading] = useState(false);
  const [msgMode, setMsgMode] = useState<'msg' | 'voice'>('msg');
  const unknownError = fm('Management.unknownError');
  /**
   * 触发验证码
   */
  const getCaptcha = useCallback(() => {
    fireCaptchaCode({ voice: msgMode === 'voice' })
      .then(() => {
        if (msgMode === 'voice') {
          setVoiceCountDownNum(CountDownNum);
        } else {
          setMessageCountDownNum(CountDownNum);
        }
      })
      .catch((error) => {
        const errorMsg = error.error || unknownError;
        messageApi?.error(errorMsg);
      });
  }, [msgMode, messageApi, unknownError]);

  useEffect(() => {
    // 刚进modal 后 直接触发验证码发送
    getCaptcha();
  }, [getCaptcha]);

  /**
   * 获取普通验证码
   */
  const getCaptchaAgain = () => {
    if (voiceCountDownNum > 0 && msgMode === 'voice') {
      return;
    }
    if (messageCountDownNum > 0 && msgMode === 'msg') {
      return;
    }
    getCaptcha();
  };

  /**
   * 获取语音验证码 或 普通验证码
   */
  const getVoiceOrMsgCaptcha = () => {
    if (messageCountDownNum > 0 && msgMode === 'voice') {
      setMsgMode('msg');
      return;
    }

    if (voiceCountDownNum > 0 && msgMode === 'msg') {
      setMsgMode('voice');
      return;
    }

    if (msgMode === 'voice') {
      setMsgMode('msg');
      fireCaptchaCode({ voice: false }).then(() => {
        setMessageCountDownNum(CountDownNum);
      });
      return;
    }

    setMsgMode('voice');
    fireCaptchaCode({ voice: true }).then(() => {
      setVoiceCountDownNum(CountDownNum);
    });
  };

  useEffect(() => {
    // 语音验证码倒计时相关选项
    if (voiceCountDownNum > 0) {
      voiceTimer.current = setTimeout(() => {
        if (voiceCountDownNum === -1) {
          clearTimeout(voiceTimer.current);
          return;
        }
        setVoiceCountDownNum(voiceCountDownNum - 1);
      }, 1000);
    }

    // 普通验证码倒计时相关选项
    if (messageCountDownNum > 0) {
      msgTimer.current = setTimeout(() => {
        if (messageCountDownNum === -1) {
          clearTimeout(msgTimer.current);
          return;
        }
        setMessageCountDownNum(messageCountDownNum - 1);
      }, 1000);
    }
    const currentTimer = timer.current;
    return () => {
      clearInterval(currentTimer);
    };
  }, [voiceCountDownNum, messageCountDownNum]);

  const handleOk = async () => {
    setLoading(true);
    try {
      await sendCaptchaCode({ code: captcha });
      // 发送验证码给后端，获得成功结果后，设置为管理员模式
      state.setAuthSuccess(true);
      // 调用成功回调
      onSuccess?.();
      // 清空回调
      clearSuccessCallBack?.();
      changeOpen(false);
      if (msgMode === 'voice') {
        setVoiceCountDownNum(0);
      } else {
        setMessageCountDownNum(0);
      }
    } catch (err) {
      if ((err as ErrorModel).errorCode) {
        const msg =
          getVerifyErrorText((err as ErrorModel).errorCode as number) || (err as ErrorModel)?.error || unknownError;

        messageApi?.error(msg);
      }
    }

    setLoading(false);
  };

  const handelCancel = () => {
    // 调用取消回调
    onCancel?.();
    // 清空回调
    clearCancelCallBack?.();
    changeOpen(false);
  };

  const handleInput: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    setCaptcha(e.target.value);
  };

  const getCountTime = () => {
    if (msgMode === 'voice' && voiceCountDownNum > 0) {
      return (
        <div key={2} className={classNames('styled-count-text')}>
          {fm('Management.sendAgainInSeconds', { voiceCountDownNum })}
        </div>
      );
    }

    if (msgMode === 'msg' && messageCountDownNum > 0) {
      return (
        <div key={2} className={classNames('styled-count-text')}>
          {fm('Management.sendAgainInSeconds', { voiceCountDownNum: messageCountDownNum })}
        </div>
      );
    }

    return null;
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      okButtonProps={{
        disabled: !captcha,
        loading,
      }}
      open={open}
      title={fm('Management.verifyPhoneNumber')}
      width={420}
      onCancel={handelCancel}
      onOk={handleOk}
    >
      <div key={1} className={classNames('styled-content')}>
        {msgMode === 'msg' ? fm('Management.sMSVerificationCodeSent') : fm('Management.voiceVerificationCodeSent')}
        {mobile}， {fm('Management.minutesPleaseTry')}
        {
          <span
            key={1}
            className={classNames('styled-link-button', {
              disabled:
                (msgMode === 'voice' && voiceCountDownNum > 0) || (msgMode === 'msg' && messageCountDownNum > 0),
            })}
            onClick={getCaptchaAgain}
          >
            {fm('Management.resend')}
          </span>
        }
        或
        {
          <span key={2} className={classNames('styled-link-button')} onClick={getVoiceOrMsgCaptcha}>
            {msgMode === 'msg' ? fm('Management.getVoiceCode') : fm('Management.obtainDigitalCode')}
          </span>
        }
      </div>
      <Space.Compact style={{ width: '100%', marginBottom: 8 }}>
        <Input placeholder={fm('Management.enterVerificationCode')} onChange={handleInput} />
        <Button
          disabled={(msgMode === 'voice' && voiceCountDownNum > 0) || (msgMode === 'msg' && messageCountDownNum > 0)}
          style={{ flexShrink: 0 }}
          onClick={getCaptcha}
        >
          {msgMode === 'msg' ? fm('Management.obtainDigitalCode') : fm('Management.getVoiceCode')}
        </Button>
      </Space.Compact>
      {getCountTime()}
    </Modal>
  );
};
