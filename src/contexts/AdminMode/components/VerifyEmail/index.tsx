import React, { useEffect, useState } from 'react';

import { checkEmailStatus } from '@/api/adminMode';
import type { CheckEmailStatusResponse } from '@/model/Admin';
import { EmailStatus } from '@/model/Admin';

import { EmailDeniedModal } from './EmailDeniedModal';
import { NoEmailModal } from './NoEmailModal';
import { NormalEmailModal } from './NormalEmailModal';

interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  showVerifyMobileModal: () => void;
  onSuccess?: () => void;
  onCancel?: () => void;
  clearSuccessCallBack: () => void;
  clearCancelCallBack: () => void;
}

export const VerifyEmailModal: React.FC<Props> = ({
  changeOpen,
  open,
  showVerifyMobileModal,
  onSuccess,
  onCancel,
  clearSuccessCallBack,
  clearCancelCallBack,
}) => {
  const [remains, setRemains] = useState(0);
  const [emailStatus, setEmailStatus] = useState<EmailStatus | null>(null);

  useEffect(() => {
    checkEmailStatus<CheckEmailStatusResponse>().then(({ status, remain_wait_time }) => {
      setRemains(remain_wait_time);
      setEmailStatus(status);
    });
  }, []);

  if (emailStatus === EmailStatus.normal) {
    return (
      <NormalEmailModal
        changeOpen={changeOpen}
        clearCancelCallBack={clearCancelCallBack}
        clearSuccessCallBack={clearSuccessCallBack}
        open={open}
        onCancel={onCancel}
        onSuccess={onSuccess}
      />
    );
  } else if (emailStatus === EmailStatus.denied) {
    return <EmailDeniedModal changeOpen={changeOpen} open={open} remains={remains} />;
  } else if (emailStatus === EmailStatus.noBind) {
    return <NoEmailModal changeOpen={changeOpen} open={open} showVerifyMobileModal={showVerifyMobileModal} />;
  }

  return null;
};
