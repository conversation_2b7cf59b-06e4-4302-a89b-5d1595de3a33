import './noEmailModal.less';

import { Modal } from 'antd';
import classNames from 'classnames';
import React, { useContext, useState } from 'react';

import { createAuthKindRecord, setAuthKind } from '@/api/adminMode';
import type { ErrorModel } from '@/model/Admin';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { convertModalFooter } from '@/utils/modal';

import { MessageContext } from '../../../MessageProvider';
import { AdminModeContext } from '../..';
interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  showVerifyMobileModal: () => void;
}

export const NoEmailModal: React.FC<Props> = ({ changeOpen, open, showVerifyMobileModal }) => {
  const { authMode } = useContext(AdminModeContext);
  const state = useAdminStore((state) => state);
  const messageApi = useContext(MessageContext);
  const [loading, setLoading] = useState(false);
  const unknownError = fm('Management.unknownError');
  const handleRedirectToBinding = () => {
    location.href = '/profile/bindingEmail';
  };

  const handleToggleMobileKind = async () => {
    setLoading(true);

    try {
      // 1. 发送请求，让后端确认该用手机号号验证
      if (authMode) {
        // 如果本身存在授权方式 则更改
        await setAuthKind({ authMode: 'sms' });
      } else {
        // 如果不存在授权方式
        await createAuthKindRecord({ authMode: 'sms' });
      }
      state.setAuthMode('sms');

      // 2. 改用手机号验证
      showVerifyMobileModal();
      // 3. 隐藏当前窗口
      changeOpen(false);
    } catch (error) {
      const errorCode = (error as ErrorModel)?.errorCode;
      if (errorCode) {
        const msg = (error as ErrorModel)?.error || unknownError;

        messageApi?.error(msg);
      }
    }
    setLoading(false);
  };

  return (
    <Modal
      centered
      closable
      cancelButtonProps={{
        loading,
      }}
      cancelText={fm('Management.phoneVerification')}
      footer={convertModalFooter}
      okText={fm('Management.linkOrVerifyNow')}
      open={open}
      title={fm('Management.2StepAuthentication')}
      width={440}
      onCancel={handleToggleMobileKind}
      onOk={handleRedirectToBinding}
    >
      <div className={classNames('no-email-modal-content')}>{fm('Management.authentication')}</div>
      <div style={{ marginTop: 24, marginBottom: 16 }}>
        <div className={classNames('no-email-modal-content')}>
          {`1. ${String(fm('Management.firstEmailAuthentication'))}`}
          <br />
          {`2. ${String(fm('Management.endEmailAuthentication'))}`}
        </div>
      </div>
    </Modal>
  );
};
