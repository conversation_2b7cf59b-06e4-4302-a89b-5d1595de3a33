import './emailDeniedModal.less';

import { Form, Input, Modal } from 'antd';
import classNames from 'classnames';
import React from 'react';

import { fm } from '@/modules/Locale';
import { useMeStore } from '@/store/Me';
import { convertModalFooter } from '@/utils/modal';
interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  remains: number;
}

const { Item } = Form;
function getRemainsText(emailRemains: number) {
  if (!emailRemains) {
    return '';
  }
  const hours = Math.floor(emailRemains / 3600);
  const days = Math.floor(hours / 24);
  return fm(`Management.daysAndHours`, { days, hours: hours - days * 24 });
}

export const EmailDeniedModal: React.FC<Props> = ({ changeOpen, open, remains }) => {
  const me = useMeStore((state) => state.me);

  const handleCancel = () => {
    changeOpen(false);
  };

  return (
    <Modal
      centered
      closable
      footer={convertModalFooter}
      okButtonProps={{
        disabled: true,
      }}
      okText={fm('Management.verify')}
      open={open}
      title={fm('Management.2StepAuthentication')}
      width={420}
      onCancel={handleCancel}
    >
      <Item label={fm('Management.linkEmail')} layout="vertical">
        <Input disabled value={me?.email} />
      </Item>
      <div className={classNames('email-denied-modal-content')}>
        {fm('Management.verificationCodeError2')}
        {<span key={2}>{getRemainsText(remains)}</span>}
        {fm('Management.verificationCodeError3')}
      </div>
    </Modal>
  );
};
