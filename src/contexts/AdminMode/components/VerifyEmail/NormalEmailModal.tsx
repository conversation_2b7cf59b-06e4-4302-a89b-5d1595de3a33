import { Button, Form, Input, Modal, Space } from 'antd';
import React, { useContext, useEffect, useRef, useState } from 'react';

import { fireEmailCode, sendEmailCaptchaCode } from '@/api/adminMode';
import type { ErrorModel } from '@/model/Admin';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { useMeStore } from '@/store/Me';

import { MessageContext } from '../../../MessageProvider';
import { getVerifyErrorText } from '../utils';
interface Props {
  open: boolean;
  changeOpen: (v: boolean) => void;
  onSuccess?: () => void;
  onCancel?: () => void;
  clearSuccessCallBack?: () => void;
  clearCancelCallBack?: () => void;
}

const { Item } = Form;
const Captcha = 'captcha';
const CountDownNum = 60;

export const NormalEmailModal: React.FC<Props> = ({
  changeOpen,
  open,
  onSuccess,
  onCancel,
  clearSuccessCallBack,
  clearCancelCallBack,
}) => {
  const state = useAdminStore((state) => state);
  const me = useMeStore((state) => state.me);
  const [form] = Form.useForm();
  const [countDownNum, setCountDownNum] = useState(0);
  const [loading, setLoading] = useState(false);
  const messageApi = useContext(MessageContext);
  const unknownError = fm('Management.unknownError');
  const timer = useRef<string | number | NodeJS.Timeout | undefined>(undefined);

  const fireEmailCaptcha = () => {
    fireEmailCode().then(() => {
      setCountDownNum(CountDownNum);
    });
  };
  useEffect(() => {
    // 验证码倒计时相关选项
    if (countDownNum > 0) {
      timer.current = setTimeout(() => {
        if (countDownNum === -1) {
          clearTimeout(timer.current);
          return;
        }
        setCountDownNum(countDownNum - 1);
      }, 1000);
    }

    return () => {
      clearInterval(timer.current);
    };
  }, [countDownNum]);

  useEffect(() => {
    if (open) {
      fireEmailCaptcha();
    }
  }, [open]);

  /**
   * 处理确定按钮点击事件的异步函数
   */
  const handleOK = async () => {
    setLoading(true);

    try {
      const formResult = await form.validateFields();
      const captcha = formResult[Captcha];
      await sendEmailCaptchaCode({
        code: captcha,
      });
      state.setAuthSuccess(true);
      onSuccess?.();
      clearSuccessCallBack?.();
      changeOpen(false);
      setCountDownNum(0);
    } catch (err) {
      if ((err as ErrorModel).errorCode) {
        const msg =
          getVerifyErrorText((err as ErrorModel).errorCode as number) || (err as ErrorModel)?.error || unknownError;
        messageApi?.error(msg);
      }
    }
    setLoading(false);
  };

  return (
    <Modal
      centered
      closable
      footer={(_, { OkBtn }) => {
        return <OkBtn />;
      }}
      okButtonProps={{
        loading,
      }}
      open={open}
      title={fm('Management.2StepAuthentication')}
      width={420}
      onCancel={() => {
        onCancel?.();
        clearCancelCallBack?.();
        changeOpen(false);
      }}
      onOk={handleOK}
    >
      <Item label={fm('Management.linkEmail')} layout="vertical">
        <Input disabled value={me?.email} />
      </Item>
      <Form form={form} layout="vertical">
        <Item
          help={countDownNum > 0 && <div>{fm('Management.reacquire', { countDownNum })}</div>}
          label={fm('Management.enterVerificationCode')}
          name={Captcha}
        >
          <Space.Compact style={{ width: '100%' }}>
            <Input placeholder={fm('Management.enterVerificationCode')} />
            <Button disabled={countDownNum > 0} style={{ flexShrink: 0 }} onClick={fireEmailCaptcha}>
              {fm('Management.getVerificationCode')}
            </Button>
          </Space.Compact>
        </Item>
      </Form>
    </Modal>
  );
};
