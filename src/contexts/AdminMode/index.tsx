import classNames from 'classnames';
import type { ReactNode } from 'react';
import { createContext, useCallback, useContext, useEffect, useMemo, useState } from 'react';

import {
  checkAdminMode,
  checkPasswordExists,
  createAuthKindRecord,
  getAuthK<PERSON>,
  quitAdminMode,
  setAuthK<PERSON>,
} from '@/api/adminMode';
import type { Action, AuthMode, CheckPasswordExistsResponse, ErrorModel } from '@/model/Admin';
import { AdminMapType } from '@/model/Admin';
import type { AdminModeState } from '@/model/management-layout';
import { fm } from '@/modules/Locale';
import { useAdminStore } from '@/store/Admin';
import { useMeStore } from '@/store/Me';

import { ReactComponent as WarningIconSVG } from '../../assets/components/modal/warning-icon.svg';
import { MessageContext } from '../MessageProvider';
import { ModalContext } from '../ModalProvider';
import { VerifyEmailModal } from './components/VerifyEmail';
import { VerifyMobileCodeModal } from './components/VerifyMobile';
import { NoMobile } from './components/VerifyMobile/NoMobile';
import { VerifyPasswordModal } from './components/VerifyPassword';
export interface ConfigContextProviderProps {
  children: ReactNode;
  config: AdminModeState;
}

export const AdminModeContext = createContext<Partial<AdminModeState>>({
  authSuccess: false,
  authMode: undefined,
  adminPasswordIsSet: true,
});
export const AdminModeDispatchContext = createContext<{
  openAuthModal: ((data?: { onSuccess?: () => void; onCancel?: () => void }) => void) | null;
  quitAdmin: (() => Promise<boolean>) | null;
  dispatch: (action: Action) => Promise<void>;
}>({
  openAuthModal: null,
  quitAdmin: null,
  dispatch: async () => {},
});
export function AdminModeProvider({ children }: { children: ReactNode }) {
  const state = useAdminStore((state) => state);
  const { setAdminPasswordIsSet, setAuthSuccess, setAuthMode, authMode } = state;
  const [verifyMobileOpen, setVerifyMobileOpen] = useState(false);
  const [verifyEmailOpen, setVerifyEmailOpen] = useState(false);
  const [verifyPasswordOpen, setVerifyPasswordOpen] = useState(false);
  const modal = useContext(ModalContext);
  const messageApi = useContext(MessageContext);
  const me = useMeStore((state) => state.me);
  const hasMobile = me?.mobileAccount;
  const [onSuccessCallBack, setOnSuccessCallBack] = useState<(() => void)[]>([]);
  const [onCancelCallBack, setOnCancelCallBack] = useState<(() => void)[]>([]);
  const unknownError = fm('Management.unknownError');
  const verificationText = useMemo(
    () => ({
      phoneVerification: fm('Management.phoneVerification'),
      emailVerification: fm('Management.emailVerification'),
      emailOrMobile: fm('Management.emailOrMobile'),
    }),
    [],
  );

  const clearSuccessCallBack = () => {
    setOnSuccessCallBack([]);
  };
  const clearCancelCallBack = () => {
    setOnCancelCallBack([]);
  };

  useEffect(() => {
    checkAdminMode<{ in_admin_mode: string }>()
      .then((res) => {
        if (res.in_admin_mode) {
          setAuthSuccess(true);
        } else {
          setAuthSuccess(false);
        }
      })
      .catch(() => {
        setAuthSuccess(false);
      });
  }, [setAuthSuccess]);

  const setToVerifyEmail = useCallback(async () => {
    try {
      if (authMode) {
        await setAuthKind({
          authMode: 'email',
        });
      } else {
        await createAuthKindRecord({
          authMode: 'email',
        });
      }
      setAuthMode('email');
      setVerifyEmailOpen(true);
    } catch (error: unknown) {
      const errorMsg = (error as ErrorModel)?.message || unknownError;
      messageApi?.error(errorMsg);
      console.error(error);
    }
  }, [messageApi, unknownError, setAuthMode, authMode]);

  /**
   * 如果当前非管理模式 触发此方法
   *
   * 如果调用管理员权限接口时，管控模式授权失败，触发此方法
   */
  const openAuthModal = useCallback(
    async (data?: { onSuccess?: () => void; onCancel?: () => void }) => {
      const { onSuccess, onCancel } = data || {};
      try {
        // 获取需要授权的方式
        const { authMode: newAuthMode } = await getAuthKind<{ authMode: AuthMode }>();
        setAuthMode(newAuthMode);
        if (newAuthMode === 'password') {
          // 情况 1 密码授权
          // 私部环境只有密码密码弹框一种情况
          const { adminPasswordIsSet } = await checkPasswordExists<CheckPasswordExistsResponse>();
          setAdminPasswordIsSet(adminPasswordIsSet);
          setVerifyPasswordOpen(true);
          // 完成后启用管理员模式 同时执行之前失败的操作
          if (onSuccess) {
            setOnSuccessCallBack([onSuccess]);
          }
          if (onCancel) {
            setOnCancelCallBack([onCancel]);
          }

          return;
        }

        if (onSuccess) {
          setOnSuccessCallBack([onSuccess]);
        }
        if (onCancel) {
          setOnCancelCallBack([onCancel]);
        }
        if (newAuthMode === 'email') {
          // 情况 2 邮箱授权
          setVerifyEmailOpen(true);
          return;
        }

        if (newAuthMode === 'sms') {
          // 情况 3 验证码授权
          setVerifyMobileOpen(true);
          return;
        }

        // 情况 4 如果没有返回授权模式 则弹出模式选择框
        // a 邮箱验证 b 手机验证
        modal?.confirm({
          title: verificationText.emailOrMobile,
          centered: true,
          width: 420,
          icon: (
            <div className={classNames('email-or-mobile')}>
              <WarningIconSVG />
            </div>
          ),
          maskClosable: false,
          closable: false,
          cancelText: verificationText.phoneVerification,
          onCancel: async () => {
            try {
              if (authMode) {
                await setAuthKind({
                  authMode: 'sms',
                });
              } else {
                await createAuthKindRecord({
                  authMode: 'sms',
                });
              }
              setAuthMode('sms');
              setVerifyMobileOpen(true);
            } catch (error) {}
          },
          okText: verificationText.emailVerification,
          onOk: setToVerifyEmail,
          footer: (_, { CancelBtn, OkBtn }) => {
            return (
              <>
                <OkBtn />
                <CancelBtn />
              </>
            );
          },
        });
      } catch (error) {}
    },
    [modal, setAdminPasswordIsSet, verificationText, setToVerifyEmail, setAuthMode, authMode],
  );

  const quitAdmin = useCallback(() => {
    return quitAdminMode();
  }, []);

  const dispatch = useCallback(
    async (action: Action) => {
      const key = AdminMapType[action.type];
      const fn = state[action.type] as (payload: unknown) => void;
      fn(action.payload[key]);
    },
    [state],
  );

  const contextValue = useMemo(
    () => ({
      authSuccess: state.authSuccess,
      authMode: state.authMode,
      adminPasswordIsSet: state.adminPasswordIsSet,
    }),
    [state.authSuccess, state.authMode, state.adminPasswordIsSet],
  );
  const dispatchValue = useMemo(
    () => ({
      dispatch,
      openAuthModal,
      quitAdmin,
    }),
    [dispatch, openAuthModal, quitAdmin],
  );
  return (
    <AdminModeContext.Provider value={contextValue}>
      <AdminModeDispatchContext.Provider value={dispatchValue}>
        {children}
        {verifyMobileOpen && !hasMobile && (
          <NoMobile
            changeOpen={(v: boolean) => {
              setVerifyMobileOpen(v);
            }}
            open={verifyMobileOpen}
            setToVerifyEmail={setToVerifyEmail}
          />
        )}
        {verifyMobileOpen && hasMobile && (
          <VerifyMobileCodeModal
            changeOpen={(v: boolean) => {
              setVerifyMobileOpen(v);
            }}
            clearCancelCallBack={clearCancelCallBack}
            clearSuccessCallBack={clearSuccessCallBack}
            messageApi={messageApi}
            open={verifyMobileOpen}
            onCancel={onCancelCallBack[0]}
            onSuccess={onSuccessCallBack[0]}
          />
        )}
        {verifyEmailOpen && (
          <VerifyEmailModal
            changeOpen={(v: boolean) => {
              setVerifyEmailOpen(v);
            }}
            clearCancelCallBack={clearCancelCallBack}
            clearSuccessCallBack={clearSuccessCallBack}
            open={verifyEmailOpen}
            showVerifyMobileModal={() => {
              setVerifyMobileOpen(true);
            }}
            onCancel={onCancelCallBack[0]}
            onSuccess={onSuccessCallBack[0]}
          />
        )}
        {verifyPasswordOpen && (
          <VerifyPasswordModal
            changeOpen={(v: boolean) => {
              setVerifyPasswordOpen(v);
            }}
            messageApi={messageApi}
            open={verifyPasswordOpen}
            onCancel={onCancelCallBack[0]}
            onSuccess={onSuccessCallBack[0]}
          />
        )}
      </AdminModeDispatchContext.Provider>
    </AdminModeContext.Provider>
  );
}
