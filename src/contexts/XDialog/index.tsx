import { omit } from 'lodash';
import type { ReactElement, ReactNode } from 'react';
import { createContext, useMemo } from 'react';

import XDialog from '@/components/XDialog';
import { useXDialog } from '@/hooks/useXDialog';
import type { CustomModalProps, XDialogContextModel } from '@/model/Dialog';

export interface XDialogProviderProps {
  children: ReactNode;
  title?: string;
  className?: string;
  modalWidth?: number | string;
  modalHeight?: number | string;
}

export const XDialogContext = createContext<XDialogContextModel>({
  open: () => {},
  onCancel: () => {},
});
// 使用方式，引入这个provider
// open 打开弹窗，onCancel 关闭弹窗
// 然后在需要的地方使用const { open, useXDialog } = useContext(XDialogContext)
// open参数(第一个参数为具体的组件，为弹窗中间的内容， 第二个参数见 CustomModalProps 接口)
// 打开表单一个例子
// const { open, useXDialog } = useContext(XDialogContext)
// open(<div>121</div>, {
//   title: '表单标题',
//   width: 420,
//   onOk: () => { 点击弹框按钮回掉
//     console.log('val');
//   },
//   height: 200,
// })

export function XDialogProvider(props: CustomModalProps): ReactElement {
  const { children } = props;
  const dialogProps = useXDialog({});
  const { open, onCancel, dialogContent, openDialog } = dialogProps;
  const providerData = useMemo(
    () => ({
      open,
      onCancel,
    }),
    [open, onCancel],
  );
  return (
    <XDialogContext.Provider value={providerData}>
      {children}
      <XDialog {...omit(dialogProps, ['children', 'open', 'openDialog'])} open={openDialog}>
        {dialogContent}
      </XDialog>
    </XDialogContext.Provider>
  );
}
