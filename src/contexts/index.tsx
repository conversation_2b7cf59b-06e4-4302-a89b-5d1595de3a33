import type { ReactNode } from 'react';

import { AntdComponentsProviders } from '@/contexts/AntdComponentsProviders';

import { AdminModeProvider } from './AdminMode';
import { PreferencesProvider } from './preferences';
import { XDialogProvider } from './XDialog';
export const AppProvider = ({ children }: { children: ReactNode }) => {
  return (
    <AntdComponentsProviders>
      <XDialogProvider>
        <PreferencesProvider>
          <AdminModeProvider>{children}</AdminModeProvider>
        </PreferencesProvider>
      </XDialogProvider>
    </AntdComponentsProviders>
  );
};
