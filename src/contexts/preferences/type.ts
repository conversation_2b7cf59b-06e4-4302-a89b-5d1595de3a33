export interface Preferences {
  clickFileArea: string;
  createFileByTemplate: boolean;
  homePage: 'desktop' | 'recent' | 'space';
  isShowIconOnly: boolean;
  openFileLocation: 'newTab' | 'currentTab';
  personalizedADRecommandation: boolean;
}

export interface PreferencesResponse {
  domain: string;
  requestId: string;
  data: Preferences;
}

export interface Action {
  type: 'setPreferences';
  payload: Preferences;
}
