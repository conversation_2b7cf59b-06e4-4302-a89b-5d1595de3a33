import type { ReactNode } from 'react';
import React, { createContext, useCallback } from 'react';

import { usePreferencesStore } from '@/store/Preferences';

import type { Action, Preferences } from './type';

export const PreferencesContext = createContext<Preferences | null>(null);
export const PreferencesDispatchContext = createContext<null | React.Dispatch<Action>>(null);

export const PreferencesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const { preferences, setPreferences } = usePreferencesStore((state) => state);
  const dispatch = useCallback(
    async (action: Action) => {
      setPreferences(action.payload);
    },
    [setPreferences],
  );
  return (
    <PreferencesContext.Provider value={preferences}>
      <PreferencesDispatchContext.Provider value={dispatch}>{children}</PreferencesDispatchContext.Provider>
    </PreferencesContext.Provider>
  );
};
