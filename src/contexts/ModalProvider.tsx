import { Modal } from 'antd';
import type { HookAPI } from 'antd/es/modal/useModal';
import type { ReactNode } from 'react';
import React, { createContext } from 'react';

export const ModalContext = createContext<HookAPI | undefined>(undefined);

export const ModalProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [modal, content] = Modal.useModal();

  return (
    <ModalContext.Provider value={modal}>
      {children}
      {content}
    </ModalContext.Provider>
  );
};
