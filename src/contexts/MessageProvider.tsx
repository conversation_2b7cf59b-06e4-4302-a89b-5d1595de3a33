import { message as messageAntd } from 'antd';
import type { MessageInstance } from 'antd/es/message/interface';
import type { ReactNode } from 'react';
import React, { createContext } from 'react';

export const MessageContext = createContext<MessageInstance | undefined>(undefined);

export const MessageProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [message, content] = messageAntd.useMessage();
  return (
    <MessageContext.Provider value={message}>
      {children}
      {content}
    </MessageContext.Provider>
  );
};
