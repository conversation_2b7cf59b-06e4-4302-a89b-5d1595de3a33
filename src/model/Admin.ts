export type AuthMode = 'email' | 'sms' | 'password' | undefined;
export interface SelectedMenuStore {
  openAuthModal: ((data?: { onSuccess?: () => void; onCancel?: () => void }) => void) | null;
  quitAdmin: (() => Promise<boolean>) | null;
}
export interface ErrorModel {
  message?: string;
  error?: string;
  errorCode?: number;
}
export type Action = {
  type: 'setAuthSuccess' | 'setAuthMode' | 'setAdminPasswordIsSet';
  payload: Partial<AdminModeState>;
};
export enum AdminMapType {
  setAuthSuccess = 'authSuccess',
  setAuthMode = 'authMode',
  setAdminPasswordIsSet = 'adminPasswordIsSet',
}
export interface AdminModeState {
  /**
   * 是否处于授权状态
   */
  authSuccess: boolean;
  /**
   * 授权方式
   */
  authMode: AuthMode;
  /**
   * 管理员是否已经设置了密码
   */
  adminPasswordIsSet: boolean;
}
export enum EmailStatus {
  /**
   * 0 正常的输入验证码状态
   */
  normal = 0,
  /**
   * 1 未绑定邮箱，弹出需要绑定的对话提示
   */
  noBind = 1,
  /**
   * 2 绑定的时间间隔太短，弹出绑定时间限制弹框
   */
  denied = 2,
}

export interface CheckEmailStatusResponse {
  status: EmailStatus;
  remain_wait_time: number;
}
export interface CheckPasswordExistsResponse {
  adminPasswordIsSet: boolean;
}
export interface PreferencesModel {
  clickFileArea: string;
  createFileByTemplate: boolean;
  homePage: 'desktop' | 'recent' | 'space';
  isShowIconOnly: boolean;
  openFileLocation: 'newTab' | 'currentTab';
  personalizedADRecommandation: boolean;
}
