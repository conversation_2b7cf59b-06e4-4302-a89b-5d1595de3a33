import type { ModalProps } from 'antd';
import type { FC, ReactNode } from 'react';
export interface CustomModalProps extends Omit<ModalProps, 'children'> {
  children: React.ReactNode;
  onOk?: () => void;
  onCancel?: () => void;

  footer?: (
    _: ReactNode,
    { OkBtn, CancelBtn }: { OkBtn: FC; CancelBtn: FC },
  ) => ReactNode | React.ReactNode | undefined;
  /**
   *  标题
   */
  title?: React.ReactNode;
  modalStyles?: {
    body: React.CSSProperties;
    content: React.CSSProperties;
  };
}

export interface XDialogContextModel {
  open: (content: ReactNode, newProps?: Omit<CustomModalProps, 'children'>) => void;
  onCancel: () => void;
}
