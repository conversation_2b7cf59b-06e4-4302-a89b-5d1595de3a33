export type TableActionType = 'add' | 'edit';
export enum TableActionKey {
  add = 'add',
  edit = 'edit',
}
//文件创建类型
export type FileCreateType = 'newdoc' | 'modoc' | 'mosheet' | 'table' | 'presentation' | 'form';
export enum FileCreateTypeKey {
  doc = 'newdoc',
  docx = 'modoc',
  sheet = 'mosheet',
  table = 'table',
  presentation = 'presentation',
  form = 'form',
}
//文件路径类型
export type FilePathType = 'docs' | 'docx' | 'sheets' | 'tables' | 'presentation' | 'forms';
export enum FilePathTypeKey {
  doc = 'docs',
  docx = 'docx',
  sheet = 'sheets',
  table = 'tables',
  presentation = 'presentation',
  form = 'forms',
}
//文件创建值
export type FileCreateTypeValue = -2 | -6 | -4 | -11 | -10 | -8;
export enum FileCreateValueKey {
  doc = -2,
  docx = -6,
  sheet = -4,
  table = -11,
  presentation = -10,
  form = -8,
}

export type FromType = 'filter' | 'form';
export enum FromTypeKey {
  filter = 'filter',
  form = 'form',
}

export type ExcludedFileTypes = 'table-form' | 'test-form' | 'folder' | 'space' | 'upload-file';
