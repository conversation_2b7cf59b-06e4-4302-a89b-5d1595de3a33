export interface LogRecord {
  id: number;
  teamId: number;
  operator: string;
  operatorId: number;
  action: number;
  targetUserId: number;
  targetOrgId: number;
  targetFileId: string;
  targetFile: {
    name: string;
    url: string;
  };
  notes: string;
  ip: string;
  operatorAvatar: string;
  ua: string;
  details: string;
  createdAt: string;
  key: string; // 这个参数是给
}

export interface Operation {
  id: string;
  children: Operation[];
}

export interface ClassificationDataType {
  auditEvents: Operation[];
  description: { [v: string]: string };
  sensitiveIds: number[];
  [key: string]: unknown;
}

export interface SelectionItem {
  tabId: string;
  groupId: string;
  id: string;
  name: string;
}

export type SelectionList = SelectionItem[];

export interface Filters {
  operator: string;
  guid?: string;
  operations: string[];
  dates?: [number, number];
}

export interface LogResponse {
  data: LogRecord[];
  nextUrl: string;
}

export interface Params {
  limit: number;
  startTimestamp?: number;
  endTimestamp?: number;
  operatorId?: string;
  targetFileId?: string;
  actionId?: string[];
}

export interface operationNode {
  id: string;
  children?: operationNode[] | null;
  tabId?: string;
}

export interface Filters {
  operator: string;
  operations: string[];
  guid?: string;
  dates?: [number, number];
}

export type AuditEvents = operationNode[];
export type Description = { [v: string]: string | null };

export interface AuditState {
  logList: LogRecord[];
  auditEvents: AuditEvents;
  description: Description;
  sensitiveIds: number[];
  selectionList: SelectionList;
  nextUrl: string;
  filters: Filters;
  /**
   * 点击查询之后保存的筛选数据，用来给导出按钮使用
   *
   * 导出按钮是按照已经查询的列表筛选数据为准
   */
  confirmFilters: Filters;
}

export interface LogListResponse {
  logList: LogRecord[];
  nextUrl: string;
}

export interface Action {
  type: 'setLogList' | 'setClassification' | 'setSelectionList' | 'setFilters' | 'setConfirmFilters';
  payload: LogListResponse | ClassificationDataType | SelectionList | Filters;
}

export interface User {
  type: string;
  user: {
    id: string;
    name: string;
    alias: string;
    avatar: string;
    isOutsider: boolean;
    email: string;
    role: null | string;
    departmentNames: string[];
    labels: string[];
    hitAttribute: {
      type: number;
      key: string;
      value: string;
    };
    teamRole: string;
    canBother: boolean;
  };
}

export interface UserListResponse {
  data: {
    next: number | string;
    results: User[];
  };
  domain: string;
  requestID: string;
}
