export interface TrashState {
  /**
   * 页面加载状态
   */
  loading: boolean;
  /**
   * 管理模式-验证成功
   */
  isManageMode: boolean;
}

export interface TrashAction {
  setLoading: (loading: boolean) => void;
  setData: (data: { isManageMode: boolean }) => void;
}

import type { TableProps } from 'antd';

export interface TrashState {
  /**
   * 页面加载状态
   */
  loading: boolean;
  /**
   * 管理模式-验证成功
   */
  isManageMode: boolean;
}

export interface Filter {
  keyword: string;
  fileTypeIds: string[];
  creator?: number;
  deletor?: number;
  begin: string;
  end: string;
}

/**
 * 后端返回的回收站文件
 */
export interface TrashFileEntity {
  /**
   * 文件或空间 guid
   */
  guid: string;
  /**
   * 文件或空间 id
   */
  id: number;
  /**
   * 文件或空间名
   */
  name: string;
  /**
   * 类型
   */
  type: number;
  /**
   * 子类型
   */
  subType: number;
  /**
   * 文件的创建人
   */
  createdBy: {
    id: number;
    name: string;
  };
  /**
   * 该文件的删除人
   */
  deletedBy: {
    id: number;
    name: string;
  };
  /**
   * 文件从个人回收站被删除的时间，单位时间戳毫秒
   */
  deletedAt: number;
}

export interface User {
  name: string;
  email: string;
  avatar: string;
  id: number;
  teamId?: number;
}

export interface SpaceEntity {
  /**
   * 团队空间名称
   */
  name: string;
  /**
   * 团队空间 id
   */
  guid: string;
}

export interface RecoverFile {
  guid: string;
  name: string;
  type: number;
  subType: number;
  url?: string;
}

export interface RestoreFileRes {
  guid: string;
  name: string;
  subType: number;
  type: number;
  url: string;
}

export enum FileTypeValue {
  Table = -11,
  Presentation = -10,
  Board = -9,
  Form = -8,
  Mindmap = -7,
  Modoc = -6,
  Sheet = -4,
  Doc = -2,
  Folder = 1,
  Space = 2,
  Img = 3,
  Pdf = 4,
  Xls = 5,
  Docx = 6,
  Ppt = 7,
  Mp3 = 8,
  Zip = 9,
  Mp4 = 10,
  Wps = 11,
  Xmind = 12,
}

export interface TrashTableProps {
  columns: TableProps<any>['columns'];
  dataSource: TrashFileEntity[];
  handleDelete: () => void;
  tableLoading: boolean;
  selectedRowKeys: string[];
  onChangeSelectedRowKeys: (guids: string[]) => void;
  handleTableScroll: (e: React.UIEvent<HTMLDivElement>) => void;
  initTableData: (params?: Filter, next?: string) => void;
}
