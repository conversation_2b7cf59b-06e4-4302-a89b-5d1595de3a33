import type { ReactNode } from 'react';
export interface BasicInfoHeaderType {
  title: string;
  teamId: number;
  teamName: string;
  breadcrumbItems?: { title: ReactNode; href?: string }[];
}
export type AuthMode = 'email' | 'sms' | 'password' | undefined;

export interface AdminModeState {
  /**
   * 是否处于授权状态
   */
  authSuccess: boolean;
  /**
   * 授权方式
   */
  authMode: AuthMode;
  /**
   * 管理员是否已经设置了密码
   */
  adminPasswordIsSet: boolean;
  setAuthSuccess: (authSuccess: boolean) => void;
  setAuthMode: (authMode: AuthMode) => void;
  setAdminPasswordIsSet: (adminPasswordIsSet: boolean) => void;
  [key: string]: boolean | AuthMode | ((data: boolean & AuthMode) => void);
}
