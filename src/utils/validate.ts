import { fm } from '@/modules/Locale';
export const emailReg = new RegExp(
  '^(([^<>()\\[\\]\\\\.,;:\\s@"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@"]+)*)|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.' +
    '[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$',
);

export function isEmail(str: string) {
  const tmpStr = str.trim();
  return tmpStr.length <= 255 && emailReg.test(tmpStr);
}

export function isMobile(mobile: string) {
  return /^1[0-9]{10}$/.test(mobile);
}

export function isMobileOrEmail(str: string) {
  return isMobile(str) || isEmail(str);
}

const validPasswdPattern = /^[a-zA-Z0-9·`~!@#$%^&*()-_+=[\]{}\\|;:'",<.>/?\s\uFEFF\xA0]*$/;
const blankReg = /^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/;
const minLength = 8;

export function getPasswordError(value: string) {
  if (!value) {
    return fm('Management.passwordCannotBeEmpty');
  }
  if (value.length < minLength) {
    return fm('Management.passwordThan8Characters');
  }
  if (value.length > 72) {
    return fm('Management.passwordCannotBeLongerThan');
  }
  if (!/[0-9]/.test(value) || !/[a-z]/.test(value) || !/[A-Z]/.test(value)) {
    return fm('Management.pwdContainNCL');
  }
  if (!validPasswdPattern.test(value) || blankReg.test(value)) {
    return fm('Management.pwdContainsSupported');
  }
  return '';
}
