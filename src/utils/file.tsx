import 'dayjs/locale/zh-cn';

import dayjs from 'dayjs';
import { useCallback } from 'react';
import { history } from 'umi';

import { cancelStar, exportFile, exportProgress, exportTable, putStar, userAction } from '@/api/File';
import { fm, getStoredLocale } from '@/modules/Locale';

export const useFormatTime = () => {
  const formatTime = useCallback((time: number | string | null, type: 'default' | 'detail' | 'message' = 'default') => {
    if (!time) return '--';
    const locale = getStoredLocale();

    const target = dayjs(time);
    const now = dayjs();
    const diff = Math.abs(target.diff(now));

    if (type === 'default') {
      // 模块: 编辑器内 文件内的更新时间
      // 文件创建时间
      // 回收站 文件删除时间
      // 文件创建时间-非今年

      // 一分钟内（60,000 毫秒）
      if (diff <= 60000) return fm('formatTime.justNow');

      // 一小时内（3,600,000 毫秒）
      if (diff <= 3600000) return fm('formatTime.minutesAgo', { minutes: Math.floor(diff / 60000) });

      // 今天（年月日相同）
      if (target.isSame(now, 'day')) return fm('formatTime.today', { hhmm: target.format('HH:mm') });

      // 昨天（与当前时间减 1 天的日期相同）
      if (target.isSame(now.subtract(1, 'day'), 'day'))
        return fm('formatTime.yesterday', { hhmm: target.format('HH:mm') });

      // 今年（年份相同）
      if (target.isSame(now, 'year')) {
        if (locale !== 'en-US') {
          return target.format('MM月DD日, HH:mm');
        } else if (locale === 'en-US') {
          return target.format('MMM DD, HH:mm');
        }
      }

      // 一年前（早于当前时间减 1 年）
      if (locale !== 'en-US') {
        return target.format('YYYY年MM月DD日 HH:mm');
      } else if (locale === 'en-US') {
        return target.format('MMM DD, YYYY, HH:mm');
      }
    } else if (type === 'message') {
      // 一分钟内（60,000 毫秒）
      if (diff <= 60000) return fm('formatTime.justNow');

      // 一小时内（3,600,000 毫秒）
      if (diff <= 3600000) return fm('formatTime.minutesAgo', { minutes: Math.floor(diff / 60000) });

      // 今天（年月日相同）
      if (target.isSame(now, 'day')) return fm('formatTime.today', { hhmm: null });

      // 昨天（与当前时间减 1 天的日期相同）
      if (target.isSame(now.subtract(1, 'day'), 'day')) return fm('formatTime.yesterday', { hhmm: null });

      // 今年（年份相同）
      if (target.isSame(now, 'year')) {
        if (locale !== 'en-US') {
          return target.format('MM月DD日');
        } else if (locale === 'en-US') {
          return target.format('MMM DD');
        }
      }

      // 一年前（早于当前时间减 1 年）
      if (locale !== 'en-US') {
        return target.format('YYYY年MM月DD日');
      } else if (locale === 'en-US') {
        return target.format('MMM DD, YYYY');
      }
    } else {
      // 模块: 文件内的详细信息
      if (locale !== 'en-US') {
        return target.format('YYYY年MM月DD日 HH:mm');
      } else if (locale === 'en-US') {
        return target.format('MMM DD, YYYY, HH:mm');
      }
    }
  }, []);
  return {
    formatTime,
  };
};

export function formatFileSizeHuman(bytes: number) {
  if (bytes < 1024) return `${bytes} B`;

  const units = ['KB', 'MB', 'GB', 'TB'];
  let unitIndex = 0;
  let size = bytes / 1024; // 初始化为 KB

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

export const getFileType = ({ type, isSpace }: { type: string; isSpace?: boolean }) => {
  if (!type) return '';
  if (type === 'folder') {
    return isSpace ? 'space' : 'folder';
  }
  return type;
};

export function getFileIcon({ type, isSpace }: { type: string; isSpace?: boolean }) {
  const type_ = getFileType({ type, isSpace });
  const typeMap: { [key: string]: any } = {
    /** 轻文档 */
    newdoc: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 传统文档 */
    docx: require('@/assets/images/fileIcon/<EMAIL>'),
    spreadsheet: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 专业表格 */
    mosheet: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 应用表格 */
    table: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 专业幻灯片 */
    presentation: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 文件夹 */
    folder: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 项目空间 */
    space: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 项目空间 */
    form: require('@/assets/images/fileIcon/<EMAIL>'),
    /** 收藏 */
    favorites: require('@/assets/images/common/<EMAIL>'),
    doc: require('@/assets/images/fileIcon/<EMAIL>'),
    modoc: require('@/assets/images/fileIcon/<EMAIL>'),
    md: require('@/assets/images/fileIcon/<EMAIL>'),
    rtf: require('@/assets/images/fileIcon/<EMAIL>'),
    wps: require('@/assets/images/fileIcon/<EMAIL>'),
    wpt: require('@/assets/images/fileIcon/<EMAIL>'),
    txt: require('@/assets/images/fileIcon/<EMAIL>'),
    pdf: require('@/assets/images/fileIcon/<EMAIL>'),
    ofd: require('@/assets/images/fileIcon/<EMAIL>'),
    xls: require('@/assets/images/fileIcon/<EMAIL>'),
    xlsx: require('@/assets/images/fileIcon/<EMAIL>'),
    csv: require('@/assets/images/fileIcon/<EMAIL>'),
    ppt: require('@/assets/images/fileIcon/<EMAIL>'),
    pptx: require('@/assets/images/fileIcon/<EMAIL>'),
    img: require('@/assets/images/fileIcon/<EMAIL>'),
    jpg: require('@/assets/images/fileIcon/<EMAIL>'),
    tiff: require('@/assets/images/fileIcon/<EMAIL>'),
    gif: require('@/assets/images/fileIcon/<EMAIL>'),
    svg: require('@/assets/images/fileIcon/<EMAIL>'),
    mp3: require('@/assets/images/fileIcon/<EMAIL>'),
    mp4: require('@/assets/images/fileIcon/<EMAIL>'),
    mov: require('@/assets/images/fileIcon/<EMAIL>'),
    qt: require('@/assets/images/fileIcon/<EMAIL>'),
    zip: require('@/assets/images/fileIcon/<EMAIL>'),
    rar: require('@/assets/images/fileIcon/<EMAIL>'),
  };
  return typeMap[type_] || require('@/assets/images/fileIcon/<EMAIL>');
}

export const openFile = ({
  type,
  guid,
  disabled,
  url,
  model,
}: {
  type: string;
  isSpace?: boolean;
  guid: string;
  disabled?: boolean;
  url: string;
  model?: 'default' | 'new';
}) => {
  if (disabled) return;

  if (model === 'new') {
    // 新标签页打开
    if (type === 'folder') userAction(guid, { trackOpen: 1 });
    window.open(url);
    return;
  }

  if (type === 'folder') {
    // 打开文件夹｜空间
    userAction(guid, { trackOpen: 1 });
    history.push(url);
  } else {
    // 打开文件
    // 编辑器页面默认执行用户打开动作，会影响阅读次数的精准
    window.open(url);
  }
};

export const fileStar = ({
  guid,
  status,
}: {
  guid: string;
  /** 是否已经收藏 */
  status: boolean;
}) => {
  if (status) {
    return new Promise<{ type: 'star' | 'cancelStar' }>((resolve, reject) => {
      cancelStar(guid)
        .then(() => {
          resolve({ type: 'cancelStar' });
        })
        .catch(() => {
          reject({ type: 'cancelStar' });
        });
    });
  } else {
    return new Promise<{ type: 'star' | 'cancelStar' }>((resolve, reject) => {
      putStar(guid)
        .then(() => {
          resolve({ type: 'star' });
        })
        .catch(() => {
          reject({ type: 'star' });
        });
    });
  }
};

export const downloadFile = (url: string, filename?: string) => {
  if (!url || typeof url !== 'string') return;
  const a = document.createElement('a');
  a.href = url;
  if (filename) {
    a.download = filename;
  }
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

export const getGenerateTempId = () => {
  const timestamp = Date.now().toString();
  const random = Math.random().toString(36).substring(2, 8);
  return `${timestamp}-${random}`;
};

export const hasSameName = (arr1: any[], arr2: any[]) => {
  const nameSet = new Set(arr1.map((item) => item.name));
  return arr2.some((item) => nameSet.has(item.name));
};

/** 导出表格文件 */
export function exportTableFile({ guid, name }: { guid: string; name: string }) {
  return new Promise<object>((resolve, reject) => {
    exportTable(guid)
      .then((res) => {
        if (res?.status === 200) {
          downloadFile(res.data.url, `${name}.xlsx`);
          resolve(res);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
}

interface Exportdata {
  type: string; // 'pdf' | 'docx' | 'jpg' | 'md' | 'xlsx' | 'wps' | 'pptx' | 'jpg' | 'xmind';
  width?: number; // 导出图片的宽度 Default: 884
}

// 轮训获取导出进度
async function getExportProgress({ taskId, name }: { taskId: string; name: string }) {
  exportProgress({ taskId }).then((res) => {
    if (res?.status === 200) {
      const { progress, downloadUrl } = res.data.data;
      if (progress < 100) {
        setTimeout(() => {
          getExportProgress({ taskId, name });
        }, 1500);
      } else if (progress === 100) {
        downloadFile(downloadUrl, `${name}`);
      }
    }
  });
}

/** 根据类型导出文件 */
export function exportFileType({ guid, data, name }: { guid: string; data: Exportdata; name: string }) {
  return new Promise<object>((resolve, reject) => {
    exportFile(guid, data)
      .then((res) => {
        if (res?.status === 200) {
          const { taskId } = res.data.data;
          getExportProgress({ taskId, name });
          resolve(res);
        }
      })
      .catch((error) => {
        reject(error);
      });
  });
}
