/*
/**
 * try catch 的替代方法
 * 使用方法参照 https://www.npmjs.com/package/await-to-js
 * @param promise 要执行的 Promise
 * @param errorExt 传递给err对象的附加信息
 * @return 返回包含错误和数据的元组
 */

// 定义API错误类型
interface ApiErrorType {
  data?: {
    code?: string;
    msg?: string;
    tid?: string;
  };
  status?: number;
  statusText?: string;
  message?: string;
}

function to<T, U = ApiErrorType>(promise: Promise<T>, errorExt?: object): Promise<[U?, T?]> {
  return promise
    .then<[undefined, T]>((data: T) => {
      return [undefined, data];
    })
    .catch<[U, undefined]>((err: U) => {
      if (errorExt) {
        Object.assign(err as object, errorExt);
      }
      return [err, undefined];
    });
}

export { to };
export default to;
