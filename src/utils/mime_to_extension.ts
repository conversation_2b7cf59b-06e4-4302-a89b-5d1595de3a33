export function mimeToExtension(mimeType: string | number) {
  const mimeMap: any = {
    'application/msword': ['newdoc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
    'application/vnd.ms-excel': ['table'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['mosheet'],
    'application/vnd.ms-powerpoint': ['presentation'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['presentation'],
  };

  if (mimeMap[mimeType]) {
    return mimeMap[mimeType][0];
  }
  return null;
}

export function fileToGotoSdkPage(mimeType: string | number) {
  const mimeMap: any = {
    'application/msword': ['docs'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['docx'],
    'application/vnd.ms-excel': ['tables'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['sheets'],
    'application/vnd.ms-powerpoint': ['presentation'],
    'application/vnd.openxmlformats-officedocument.presentationml.presentation': ['presentation'],
  };

  if (mimeMap[mimeType]) {
    return mimeMap[mimeType][0];
  }
  return 'files';
}
