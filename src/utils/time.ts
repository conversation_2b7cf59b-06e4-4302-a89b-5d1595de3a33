import { DatesPresetAbsolute, DatesPresetRelative, isJustNow, stringify } from '@shimo/lo-dates';
import type { Dayjs } from 'dayjs';
import dayjs from 'dayjs';

import { fm2 } from '@/modules/Locale';

const i18nText = {
  hour: fm2('Time.hour'),
  mine: fm2('Time.mine'),
  second: fm2('Time.second'),
};
export const stringifyAbsoluteFullDate = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.FullDate);
};

export const formatExpiredAt = (expiredAt: Date, { withDistance = false }: { withDistance?: boolean } = {}): string => {
  let message = stringifyAbsoluteFullDate(expiredAt);
  if (withDistance) {
    const now = dayjs();
    const distanceMessage = Math.abs(now.diff(expiredAt, 'd'));
    const expiredDay = fm2('Time.expiredDay', { day: distanceMessage });
    const dayExpired = fm2('Time.dayExpired', { day: distanceMessage });
    message += now.isAfter(expiredAt) ? expiredDay : dayExpired;
  }
  return message;
};

export const isSame = (time1: dayjs.ConfigType, time2: dayjs.ConfigType) => {
  return dayjs(time1).isSame(time2);
};

/**
 * 以传入的时间为参照点
 * 计算当前时间距离参照时间的天数
 * @param timeStr - 参照时间
 * @param nowStr - 当前时间，默认为 Date.now()
 */
export const getDistanceBetweenDays = (timeStr: dayjs.ConfigType, nowStr?: dayjs.ConfigType) => {
  const now = nowStr ? dayjs(nowStr) : dayjs();
  const then = dayjs(timeStr);
  const diff = Math.abs(now.diff(then, 'day'));
  return diff;
};

/**
 * 根据传入的秒数返回时间描述
 * 传入秒数大于 1小时免责显示 x小时x分钟
 * 1-60 分钟则显示 x分钟
 * 小于 1分钟则显示 x秒
 * @param seconds - 秒数
 * @returns 时间描述
 */
export const formatTime = (seconds: number) => {
  const minutes = Math.floor(seconds / 60);
  const hours = Math.floor(minutes / 60);
  if (hours > 0) {
    return `${hours}${i18nText.hour}${minutes - hours * 60}${i18nText.mine}`;
  }
  if (minutes < 1) {
    return `${seconds}${i18nText.second}`;
  }
  return `${minutes}${i18nText.mine}`;
};

type DateType = number | string | Date | Dayjs;

export const stringifyAbsoluteDateOnly = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.DateOnly);
};

export const stringifyAbsoluteDateTime = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.DateTime);
};

export const stringifyAbsoluteFullDateTime = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.FullDateTime);
};

export const stringifyAbsoluteDateDay = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.DateDay);
};

export const stringifyAbsoluteFullDateDay = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.FullDateDay);
};

export const stringifyAbsoluteDateDayTime = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.DateDayTime);
};

export const stringifyAbsoluteFullDateDayTime = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.FullDateDayTime);
};

export const stringifyAbsoluteTimeOnly = (date: DateType): string => {
  return stringify(date, DatesPresetAbsolute.TimeOnly);
};

export const stringifyRelativeDateOnly = (date: DateType): string => {
  return stringify(date, DatesPresetRelative.DateOnly);
};

export const stringifyRelativeDateTime = (date: DateType): string => {
  return stringify(date, DatesPresetRelative.DateTime);
};

export const stringifyRelativeDateDay = (date: DateType): string => {
  return stringify(date, DatesPresetRelative.DateDay);
};

export const stringifyRelativeDateDayTime = (date: DateType): string => {
  return stringify(date, DatesPresetRelative.DateDayTime);
};

// 秒级时间戳转换成 Date 逻辑
export const secondsToDate = (seconds: number): Date => {
  return dayjs.unix(seconds).toDate();
};

export { isJustNow };
