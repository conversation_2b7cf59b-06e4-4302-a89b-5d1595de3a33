// 列表滚动
export function useMyScroll() {
  let lastScrollTop = 0;

  const scrollHandler = (options: {
    el: HTMLElement;
    cb: (direction: 'up' | 'down') => void;
    distance?: number; // 距离顶部或者底部的距离，达到时触发回调函数
  }) => {
    const { el, cb, distance = 200 } = options;
    // 如果是 document，使用 window 的相关属性
    const isDocument = el instanceof Document;
    let scrollTop = 0,
      scrollHeight = 0,
      clientHeight = 0;
    if (!isDocument) {
      scrollTop = el.scrollTop;
      scrollHeight = el.scrollHeight;
      clientHeight = el.clientHeight;
    } else {
      scrollTop = window.scrollY;
      scrollHeight = document.body.scrollHeight;
      clientHeight = window.innerHeight;
    }

    const isScrollingDown = scrollTop > lastScrollTop;
    if (scrollTop + clientHeight >= scrollHeight - distance) {
      if (cb) cb(isScrollingDown ? 'down' : 'up');
    }

    lastScrollTop = scrollTop <= 0 ? 0 : scrollTop; // 在移动设备上或在某些情况下(用户快速滑动列表)，滚动位置可能会变为负值
    return scrollTop;
  };
  return scrollHandler;
}

// 图片压缩函数
export const compressImage = (file: File): Promise<File> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader();

    reader.onload = (e) => {
      const img = new Image(); // 修复点：添加参数
      img.onload = () => {
        // 设置压缩质量
        const MAX_WIDTH = 800;
        const MAX_HEIGHT = 800;
        let width = img.width;
        let height = img.height;

        // 如果图片尺寸超过最大尺寸，则按比例缩放
        if (width > MAX_WIDTH) {
          height *= MAX_WIDTH / width;
          width = MAX_WIDTH;
        }

        if (height > MAX_HEIGHT) {
          width *= MAX_HEIGHT / height;
          height = MAX_HEIGHT;
        }

        // 创建canvas进行图片压缩
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        if (!ctx) {
          reject(new Error('无法获取canvas上下文'));
          return;
        }

        canvas.width = width;
        canvas.height = height;

        // 绘制图片到canvas
        ctx.drawImage(img, 0, 0, width, height);

        // 将canvas内容转换为Blob对象
        canvas.toBlob(
          (blob) => {
            if (!blob) {
              reject(new Error('图片转换失败'));
              return;
            }

            // 创建新的File对象
            const compressedFile = new File([blob], file.name, { type: file.type, lastModified: Date.now() });

            resolve(compressedFile);
          },
          file.type,
          0.7, // 压缩质量，范围0-1
        );
      };

      img.onerror = () => reject(new Error('图片加载失败'));
      img.src = e.target?.result as string;
    };

    reader.onerror = () => reject(new Error('读取文件失败'));
    reader.readAsDataURL(file);
  });
};
