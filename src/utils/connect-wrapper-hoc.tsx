import type { ComponentType, ReactElement, ReactNode } from 'react';

interface ChildrenProps {
  children: ReactNode;
}
export function connectWrapperHOC<Props extends ChildrenProps>(
  Content: ComponentType<Props>,
  ...Wrappers: Array<ComponentType<{ children: ReactNode }>>
): ComponentType<Props> {
  return Wrappers.reduce((Child, Wrapper) => {
    return function WrapperHOC(props): ReactElement {
      return (
        <Wrapper>
          <Child {...props} />
        </Wrapper>
      );
    };
  }, Content);
}
