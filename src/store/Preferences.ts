import { createStore, useStore } from 'zustand';

import type { PreferencesModel } from '@/model/Admin';

interface PreferencesStore {
  preferences: PreferencesModel;
  setPreferences: (preferences: PreferencesModel) => void;
}

export const preferencesStore = createStore<PreferencesStore>((set) => ({
  preferences: {
    clickFileArea: 'nameAndIcon',
    createFileByTemplate: false,
    homePage: 'recent',
    isShowIconOnly: false,
    openFileLocation: 'newTab',
    personalizedADRecommandation: true,
  },
  setPreferences: (preferences: PreferencesModel) => set({ preferences }),
}));

export const usePreferencesStore = <U>(selector: (state: PreferencesStore) => U) => {
  return useStore(preferencesStore, selector);
};
