import { createStore, useStore } from 'zustand';

import type { TrashAction, TrashState } from '@/model/Enterprise/Trash';

const initState: TrashState = {
  loading: true,
  isManageMode: true,
};

type TrashStore = TrashState & TrashAction;

export const trashStore = createStore<TrashStore>((set) => ({
  ...initState,
  setLoading: (loading: boolean) => set(() => ({ loading })),
  setData: (data: { isManageMode: boolean }) => set(() => data),
}));

export const useTrashStore = <U>(selector: (state: TrashStore) => U) => {
  return useStore(trashStore, selector);
};
