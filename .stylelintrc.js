module.exports = {
  // 使用 Umi Max 项目配置（包含了 umi/stylelint 的所有规则）
  extends: require.resolve('@umijs/max/stylelint'),

  rules: {
    // ===== 基础规则 =====
    // 完全禁用value-keyword-case规则，避免把 CSS 里面的动态值变成小写
    'value-keyword-case': null,

    // ===== 重要性和性能规则 =====
    // 禁止代码中使用 !important（警告级别，便于逐步修复）
    'declaration-no-important': [true, { severity: 'warning' }],
    'keyframe-declaration-no-important': true,

    // ===== 单位和兼容性规则 =====
    // 禁止使用 vh/vw 单位
    'unit-disallowed-list': [['vh', 'vw'], { severity: 'warning' }],
    // gap 属性警告，避免 flexbox gap 兼容问题
    'property-disallowed-list': [['gap'], { severity: 'warning' }],

    // ===== 颜色相关规则 =====
    // 使用短格式的十六进制颜色
    'color-hex-length': 'short',
    // 确保十六进制颜色有效
    'color-no-invalid-hex': true,
    // 避免使用命名颜色，推荐使用 CSS 变量或十六进制（允许常用的系统颜色）
    'color-named': [
      'never',
      {
        severity: 'warning',
      },
    ],
    // 在主题系统中，推荐使用 CSS 变量而不是直接的十六进制颜色
    'color-no-hex': [
      true,
      {
        severity: 'warning',
        message: '推荐使用 CSS 变量 (var(--theme-*)) 而不是直接的十六进制颜色以支持主题切换',
      },
    ],

    // ===== 字体相关规则 =====
    // 字体名称引号规则
    'font-family-name-quotes': 'always-where-recommended',
    // 避免重复的字体名称
    'font-family-no-duplicate-names': true,
    // 确保字体权重值有效
    'font-weight-notation': 'numeric',

    // ===== CSS 变量和自定义属性规则 =====
    // 自定义属性命名模式（遵循项目的 --theme-* 模式，但允许现有的变量）
    'custom-property-pattern': [
      '^(theme|brand|component|red|orange|yellow|green|blue|purple|gray|transparency)-[a-z0-9]+(-[a-z0-9]+)*$',
      {
        message: 'CSS 变量推荐遵循 --theme-*, --brand-*, --component-* 命名模式',
        severity: 'warning',
      },
    ],

    // ===== 选择器规则 =====
    // 类名命名模式（支持 kebab-case 和 camelCase）
    'selector-class-pattern': [
      '^[a-z]([a-z0-9-]+)?([A-Z][a-z0-9]*)*$',
      {
        message: '类名应使用 kebab-case 或 camelCase 格式',
        severity: 'warning',
      },
    ],
    // 限制选择器复杂度（适当放宽以适应现有代码）
    'selector-max-compound-selectors': [6, { severity: 'warning' }],
    // 避免过深的嵌套（适当放宽以适应现有代码）
    'max-nesting-depth': [
      4,
      {
        severity: 'warning',
        ignore: ['blockless-at-rules', 'pseudo-classes'],
      },
    ],

    // ===== 声明和属性规则 =====
    // 避免重复的属性
    'declaration-block-no-duplicate-properties': [
      true,
      {
        ignore: ['consecutive-duplicates-with-different-values'],
      },
    ],
    // 属性值中避免使用 vendor 前缀（应该由 autoprefixer 处理）
    'value-no-vendor-prefix': [true, { severity: 'warning' }],
    'property-no-vendor-prefix': [true, { severity: 'warning' }],

    // ===== 函数和值规则 =====
    // URL 引号规则
    'function-url-quotes': 'always',
    // 避免无效的十六进制颜色
    'color-no-invalid-hex': true,

    // ===== 注释规则 =====
    // 避免空注释
    'comment-no-empty': true,

    // ===== React/组件特定规则 =====
    // 避免使用未知的 @规则
    'at-rule-no-unknown': [
      true,
      {
        ignoreAtRules: [
          'tailwind',
          'apply',
          'variants',
          'responsive',
          'screen',
          'layer',
          'config',
          'import-glob',
          'charset',
          'import',
          'media',
          'supports',
          'keyframes',
        ],
      },
    ],

    // ===== 性能和可访问性规则 =====
    // 避免低性能的选择器
    'selector-max-universal': [1, { severity: 'warning' }],
    // 避免过长的选择器
    'selector-max-specificity': ['0,4,0', { severity: 'warning' }],

    // ===== 代码质量规则 =====
    // 避免空的规则块
    'block-no-empty': [true, { severity: 'warning' }],
    // 避免重复的选择器
    'no-duplicate-selectors': [true, { severity: 'warning' }],
  },

  ignoreFiles: [
    '/externals/**/*',
    '**/assets/styles/**/*.less', // 忽略主题文件中的颜色规则
  ],
};
